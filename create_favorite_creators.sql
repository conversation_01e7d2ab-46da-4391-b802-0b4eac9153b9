-- Create the favorite_creators table for user recommendations
CREATE TABLE IF NOT EXISTS favorite_creators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    writer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, writer_id)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_favorite_creators_user_id ON favorite_creators(user_id);
CREATE INDEX IF NOT EXISTS idx_favorite_creators_writer_id ON favorite_creators(writer_id);

-- Enable RLS
ALTER TABLE favorite_creators ENABLE ROW LEVEL SECURITY;

-- RLS Policies
DROP POLICY IF EXISTS "Users can manage their own favorite creators" ON favorite_creators;
CREATE POLICY "Users can manage their own favorite creators" ON favorite_creators
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Anyone can view favorite creators" ON favorite_creators;
CREATE POLICY "Anyone can view favorite creators" ON favorite_creators
    FOR SELECT USING (true);