-- Analysis script to understand current RLS policies before optimization
-- This script ON<PERSON><PERSON> reads data and does NOT modify anything

-- Show all current RLS policies that contain auth function calls
SELECT 
    schemaname,
    tablename, 
    policyname,
    cmd,
    CASE 
        WHEN cmd = 'r' THEN 'SELECT'
        WHEN cmd = 'a' THEN 'INSERT' 
        WHEN cmd = 'w' THEN 'UPDATE'
        WHEN cmd = 'd' THEN 'DELETE'
        WHEN cmd = '*' THEN 'ALL'
        ELSE cmd
    END as command_type,
    qual as using_clause,
    with_check as with_check_clause
FROM pg_policies 
WHERE schemaname = 'public'
AND (
    qual LIKE '%auth.uid()%' OR 
    qual LIKE '%auth.role()%' OR
    with_check LIKE '%auth.uid()%' OR 
    with_check LIKE '%auth.role()%'
)
ORDER BY tablename, policyname;

-- Count total policies that would be affected
SELECT 
    COUNT(*) as policies_with_auth_calls,
    COUNT(DISTINCT tablename) as tables_affected
FROM pg_policies 
WHERE schemaname = 'public'
AND (
    qual LIKE '%auth.uid()%' OR 
    qual LIKE '%auth.role()%' OR
    with_check LIKE '%auth.uid()%' OR 
    with_check LIKE '%auth.role()%'
);

-- Show sample of what the optimized versions would look like
SELECT 
    tablename,
    policyname,
    'ORIGINAL' as version,
    qual as policy_text
FROM pg_policies 
WHERE schemaname = 'public'
AND qual LIKE '%auth.uid()%'
LIMIT 2;

-- Show what those same policies would look like optimized
SELECT 
    tablename,
    policyname,
    'OPTIMIZED' as version,
    REPLACE(REPLACE(qual, 'auth.uid()', '(select auth.uid())'), 'auth.role()', '(select auth.role())') as policy_text
FROM pg_policies 
WHERE schemaname = 'public'
AND qual LIKE '%auth.uid()%'
LIMIT 2;