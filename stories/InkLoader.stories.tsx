import type { Meta, StoryObj } from '@storybook/react'
import InkLoader from '@/components/InkLoader'

const meta: Meta<typeof InkLoader> = {
  title: 'Components/InkLoader',
  component: InkLoader,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A minimalist ink-reveal style loader with animated bars that shrink from left to right.',
      },
    },
  },
  argTypes: {
    lines: {
      control: { type: 'range', min: 1, max: 5, step: 1 },
      description: 'Number of ink bars to display',
    },
    delay: {
      control: { type: 'range', min: 0, max: 1, step: 0.1 },
      description: 'Initial delay before animation starts (in seconds)',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    lines: 3,
    delay: 0,
  },
}

export const SingleLine: Story = {
  args: {
    lines: 1,
    delay: 0,
  },
}

export const FiveLines: Story = {
  args: {
    lines: 5,
    delay: 0,
  },
}

export const WithDelay: Story = {
  args: {
    lines: 3,
    delay: 0.5,
  },
}

export const CustomStyling: Story = {
  args: {
    lines: 3,
    delay: 0,
    className: 'w-12',
  },
  parameters: {
    docs: {
      description: {
        story: 'Custom width applied via className prop',
      },
    },
  },
}

export const InButton: Story = {
  render: () => (
    <button className="bg-gray-800 text-white px-6 py-3 rounded-lg font-medium min-h-[48px] flex items-center justify-center">
      <InkLoader lines={3} className="w-8" />
    </button>
  ),
  parameters: {
    docs: {
      description: {
        story: 'InkLoader used inside a button component',
      },
    },
  },
}

export const ColorVariations: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="p-4 bg-white border rounded">
        <p className="text-sm mb-2">Default (Gray)</p>
        <InkLoader lines={3} />
      </div>
      <div className="p-4 bg-gray-800 rounded">
        <p className="text-sm mb-2 text-white">White bars</p>
        <div className="[&_span]:bg-white">
          <InkLoader lines={3} />
        </div>
      </div>
      <div className="p-4 bg-blue-50 border border-blue-200 rounded">
        <p className="text-sm mb-2 text-blue-800">Blue bars</p>
        <div className="[&_span]:bg-blue-600">
          <InkLoader lines={3} />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different color variations using CSS overrides',
      },
    },
  },
}
