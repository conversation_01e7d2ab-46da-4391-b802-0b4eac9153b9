import { useState, useEffect } from 'react'
import { generateVideoThumbnailWithAspectRatio } from '@/lib/video-thumbnail'

interface UseVideoThumbnailOptions {
  aspectRatio?: number
  timeInSeconds?: number
  enabled?: boolean
}

export function useVideoThumbnail(
  videoUrl: string | null,
  options: UseVideoThumbnailOptions = {}
) {
  const {
    aspectRatio = 4/3,
    timeInSeconds = 1,
    enabled = true
  } = options

  const [thumbnail, setThumbnail] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!videoUrl || !enabled) {
      setThumbnail(null)
      setError(null)
      return
    }

    let isCancelled = false
    setLoading(true)
    setError(null)

    generateVideoThumbnailWithAspectRatio(videoUrl, aspectRatio, timeInSeconds)
      .then((thumbnailDataUrl) => {
        if (!isCancelled) {
          setThumbnail(thumbnailDataUrl)
          setLoading(false)
        }
      })
      .catch((err) => {
        if (!isCancelled) {
          console.error('Failed to generate video thumbnail:', err)
          setError(err.message || 'Failed to generate thumbnail')
          setLoading(false)
        }
      })

    return () => {
      isCancelled = true
    }
  }, [videoUrl, aspectRatio, timeInSeconds, enabled])

  return {
    thumbnail,
    loading,
    error,
    retry: () => {
      if (videoUrl && enabled) {
        setError(null)
        setLoading(true)
        generateVideoThumbnailWithAspectRatio(videoUrl, aspectRatio, timeInSeconds)
          .then(setThumbnail)
          .catch((err) => {
            setError(err.message || 'Failed to generate thumbnail')
          })
          .finally(() => setLoading(false))
      }
    }
  }
}
