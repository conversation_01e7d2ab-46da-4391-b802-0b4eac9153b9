'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface WaitlistStats {
  totalWaitlistSize: number
  projectsWithWaitlists: number
  recentSignups: number
  topProject: { title: string; count: number } | null
}

export function useWaitlistStats(userId: string | null) {
  const [stats, setStats] = useState<WaitlistStats>({
    totalWaitlistSize: 0,
    projectsWithWaitlists: 0,
    recentSignups: 0,
    topProject: null
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!userId) {
      setLoading(false)
      return
    }

    const fetchWaitlistStats = async () => {
      try {
        const supabase = createSupabaseClient()
        
        // Get all projects with their waitlists
        const { data: projects, error: projectsError } = await supabase
          .from('projects')
          .select(`
            id,
            title,
            project_waitlist (
              id,
              created_at
            )
          `)
          .eq('user_id', userId)

        if (projectsError) {
          throw projectsError
        }

        if (!projects) {
          setStats({
            totalWaitlistSize: 0,
            projectsWithWaitlists: 0,
            recentSignups: 0,
            topProject: null
          })
          return
        }

        // Calculate stats
        const sevenDaysAgo = new Date()
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

        let totalWaitlistSize = 0
        let projectsWithWaitlists = 0
        let recentSignups = 0
        let topProject: { title: string; count: number } | null = null

        projects.forEach(project => {
          const waitlistCount = project.project_waitlist?.length || 0
          
          if (waitlistCount > 0) {
            projectsWithWaitlists++
            totalWaitlistSize += waitlistCount

            // Check if this is the top project
            if (!topProject || waitlistCount > topProject.count) {
              topProject = { title: project.title, count: waitlistCount }
            }

            // Count recent signups
            const recentCount = project.project_waitlist?.filter(
              (entry: any) => new Date(entry.created_at) > sevenDaysAgo
            ).length || 0
            recentSignups += recentCount
          }
        })

        setStats({
          totalWaitlistSize,
          projectsWithWaitlists,
          recentSignups,
          topProject
        })

      } catch (err) {
        console.error('Error fetching waitlist stats:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch waitlist stats')
      } finally {
        setLoading(false)
      }
    }

    fetchWaitlistStats()
  }, [userId])

  return { stats, loading, error }
}
