'use client'

import { useState, useEffect } from 'react'

interface DeviceInfo {
  isDesktop: boolean
  isMobile: boolean
  isTablet: boolean
  screenWidth: number
  screenHeight: number
  hasHover: boolean
  hasTouch: boolean
  pixelRatio: number
}

export function useDeviceDetection(): DeviceInfo {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    isDesktop: false,
    isMobile: false,
    isTablet: false,
    screenWidth: 0,
    screenHeight: 0,
    hasHover: false,
    hasTouch: false,
    pixelRatio: 1,
  })

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      const pixelRatio = window.devicePixelRatio || 1

      // Device type detection
      const isMobile = width < 768
      const isTablet = width >= 768 && width < 1024
      const isDesktop = width >= 1024

      // Capability detection
      const hasHover = window.matchMedia('(hover: hover)').matches
      const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0

      setDeviceInfo({
        isDesktop,
        isMobile,
        isTablet,
        screenWidth: width,
        screenHeight: height,
        hasHover,
        hasTouch,
        pixelRatio,
      })
    }

    // Initial detection
    updateDeviceInfo()

    // Listen for resize events
    window.addEventListener('resize', updateDeviceInfo)
    
    // Listen for orientation changes
    window.addEventListener('orientationchange', () => {
      setTimeout(updateDeviceInfo, 100) // Delay to ensure accurate measurements
    })

    return () => {
      window.removeEventListener('resize', updateDeviceInfo)
      window.removeEventListener('orientationchange', updateDeviceInfo)
    }
  }, [])

  return deviceInfo
}

// Utility functions for common device checks
export function useIsDesktop(): boolean {
  const { isDesktop } = useDeviceDetection()
  return isDesktop
}

export function useIsMobile(): boolean {
  const { isMobile } = useDeviceDetection()
  return isMobile
}

export function useHasHover(): boolean {
  const { hasHover } = useDeviceDetection()
  return hasHover
}

export function useHasTouch(): boolean {
  const { hasTouch } = useDeviceDetection()
  return hasTouch
}

// Advanced device capabilities
export function useDeviceCapabilities() {
  const deviceInfo = useDeviceDetection()
  
  return {
    ...deviceInfo,
    // Derived capabilities
    canHover: deviceInfo.hasHover && !deviceInfo.hasTouch,
    isHighDPI: deviceInfo.pixelRatio > 1.5,
    isLargeScreen: deviceInfo.screenWidth > 1440,
    isUltrawide: deviceInfo.screenWidth / deviceInfo.screenHeight > 1.8,
    // Performance hints
    shouldUseAdvancedAnimations: deviceInfo.isDesktop && deviceInfo.pixelRatio <= 2,
    shouldPreloadImages: deviceInfo.isDesktop,
    shouldUseHoverEffects: deviceInfo.hasHover && !deviceInfo.hasTouch,
  }
}
