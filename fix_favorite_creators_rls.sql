-- Fix RLS policies for favorite_creators table

-- Drop existing policies
DROP POLICY IF EXISTS "Users can manage their own favorite creators" ON favorite_creators;
DROP POLICY IF EXISTS "Anyone can view favorite creators" ON favorite_creators;

-- Create new policies that allow proper access
CREATE POLICY "Users can manage their own favorite creators" ON favorite_creators
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view favorite creators" ON favorite_creators
    FOR SELECT USING (true);

-- Also create policy for system/service role access
CREATE POLICY "Service role can manage favorite creators" ON favorite_creators
    FOR ALL USING (true);