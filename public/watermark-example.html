<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyDiary Video Watermark Example</title>
    <style>
        /* OnlyDiary Video Watermark Styles */
        .video-container {
            position: relative;
            display: inline-block;
            max-width: 100%;
        }

        .video-container video {
            display: block;
            width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .onlydiary-watermark {
            position: absolute;
            bottom: 16px;
            right: 16px;
            z-index: 1000;
            
            /* Styling */
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
            
            /* Typography */
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 13px;
            font-weight: 700;
            color: white;
            letter-spacing: 0.5px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
            
            /* Effects */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
            opacity: 0.85;
            
            /* Prevent interaction */
            pointer-events: none;
            user-select: none;
        }

        .onlydiary-watermark::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.15), rgba(236, 72, 153, 0.15));
            border-radius: 8px;
            z-index: -1;
        }

        /* Demo styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        h1, h2 {
            color: #333;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 16px 0;
        }
    </style>
</head>
<body>
    <h1>OnlyDiary Video Watermark</h1>
    
    <div class="demo-section">
        <h2>Live Example</h2>
        <p>This shows how the OnlyDiary watermark appears on videos:</p>
        
        <div class="video-container">
            <!-- Replace with your video -->
            <video controls width="100%" style="background: #000;">
                <source src="sample-video.mp4" type="video/mp4">
                Your browser doesn't support video playback.
            </video>
            
            <!-- OnlyDiary Watermark -->
            <div class="onlydiary-watermark">
                Only on OnlyDiary.app
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>How to Use</h2>
        <p>To add the OnlyDiary watermark to any video, wrap your video element in a container and add the watermark div:</p>
        
        <div class="code-block">
&lt;div class="video-container"&gt;
    &lt;video controls&gt;
        &lt;source src="your-video.mp4" type="video/mp4"&gt;
    &lt;/video&gt;
    
    &lt;div class="onlydiary-watermark"&gt;
        Only on OnlyDiary.app
    &lt;/div&gt;
&lt;/div&gt;
        </div>
    </div>

    <div class="demo-section">
        <h2>CSS Required</h2>
        <p>Include this CSS in your stylesheet or style tag:</p>
        
        <div class="code-block">
.video-container {
    position: relative;
    display: inline-block;
}

.onlydiary-watermark {
    position: absolute;
    bottom: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 13px;
    font-weight: 700;
    color: white;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    opacity: 0.85;
    pointer-events: none;
    user-select: none;
    z-index: 1000;
}
        </div>
    </div>

    <div class="demo-section">
        <h2>Features</h2>
        <ul>
            <li>✨ <strong>Always visible</strong> - Stays on top during video playback</li>
            <li>🎯 <strong>Non-intrusive</strong> - Semi-transparent with blur effect</li>
            <li>📱 <strong>Responsive</strong> - Adapts to different screen sizes</li>
            <li>🚫 <strong>Unremovable</strong> - Cannot be clicked or selected</li>
            <li>🎨 <strong>Branded</strong> - Consistent OnlyDiary styling</li>
            <li>⚡ <strong>Lightweight</strong> - Pure CSS, no JavaScript required</li>
        </ul>
    </div>
</body>
</html>
