-- SAFE RLS Analysis - shows what actually needs optimization
-- This prevents double-wrapping already optimized auth calls

-- Show policies that need optimization (contain bare auth.uid() calls)
SELECT 
    tablename,
    policyname,
    'NEEDS_OPTIMIZATION' as status,
    qual as policy_text
FROM pg_policies 
WHERE schemaname = 'public'
AND (
    -- Find bare auth.uid() calls that are NOT already wrapped
    qual ~ 'auth\.uid\(\)' 
    AND qual !~ '\(select auth\.uid\(\)\)'
)
ORDER BY tablename, policyname;

-- Show policies that are already optimized
SELECT 
    tablename,
    policyname,
    'ALREADY_OPTIMIZED' as status,
    qual as policy_text
FROM pg_policies 
WHERE schemaname = 'public'
AND qual ~ '\(select auth\.uid\(\)\)'
ORDER BY tablename, policyname;

-- Count summary
SELECT 
    'NEEDS_OPTIMIZATION' as category,
    COUNT(*) as count
FROM pg_policies 
WHERE schemaname = 'public'
AND qual ~ 'auth\.uid\(\)' 
AND qual !~ '\(select auth\.uid\(\)\)'

UNION ALL

SELECT 
    'ALREADY_OPTIMIZED' as category,
    COUNT(*) as count
FROM pg_policies 
WHERE schemaname = 'public'
AND qual ~ '\(select auth\.uid\(\)\)';

-- Show policies with WITH CHECK clauses that need optimization
SELECT 
    tablename,
    policyname,
    'WITH_CHECK_NEEDS_OPT' as status,
    with_check as policy_text
FROM pg_policies 
WHERE schemaname = 'public'
AND with_check ~ 'auth\.uid\(\)' 
AND with_check !~ '\(select auth\.uid\(\)\)'
ORDER BY tablename, policyname;