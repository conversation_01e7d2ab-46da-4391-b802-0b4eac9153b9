-- Fix search_path for functions - using correct syntax
-- Note: You may need to adjust parameter types based on actual function signatures

-- Functions with no parameters
ALTER FUNCTION public.update_bookmark_count SET search_path = '';
ALTER FUNCTION public.update_flower_count SET search_path = '';
ALTER FUNCTION public.update_subscriber_count SET search_path = '';
ALTER FUNCTION public.update_entry_count SET search_path = '';
ALTER FUNCTION public.notify_comment_created SET search_path = '';
ALTER FUNCTION public.update_updated_at_column SET search_path = '';
ALTER FUNCTION public.check_photo_limit SET search_path = '';
ALTER FUNCTION public.update_post_credits_updated_at SET search_path = '';
ALTER FUNCTION public.update_love_count SET search_path = '';
ALTER FUNCTION public.update_hourly_love_stats SET search_path = '';
ALTER FUNCTION public.get_top_diary_entries_hourly SET search_path = '';

-- Functions with UUID parameters
ALTER FUNCTION public.is_following(uuid) SET search_path = '';
ALTER FUNCTION public.get_follower_count(uuid) SET search_path = '';
ALTER FUNCTION public.user_can_comment_on_entry(uuid, uuid) SET search_path = '';
ALTER FUNCTION public.user_has_active_subscription(uuid, uuid) SET search_path = '';
ALTER FUNCTION public.get_writer_public_data(uuid) SET search_path = '';
ALTER FUNCTION public.get_writer_locked_entries(uuid, uuid) SET search_path = '';
ALTER FUNCTION public.user_can_read_post(uuid, uuid) SET search_path = '';
ALTER FUNCTION public.consume_credit_for_post(uuid, uuid) SET search_path = '';
ALTER FUNCTION public.get_entry_preview(uuid, uuid) SET search_path = '';