# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

- **Development**: `npm run dev` (uses turbopack for faster builds)
- **Build**: `npm run build` 
- **Lint**: `npm run lint`
- **Production**: `npm run start`

## Architecture

This is a Next.js 15 project using the App Router architecture with TypeScript, Tailwind CSS, and integrated third-party services.

### Tech Stack
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS v4
- **Database**: Supabase integration
- **Payments**: Stripe integration
- **Fonts**: <PERSON><PERSON><PERSON> and <PERSON>eist Mono

### Project Structure
- `app/` - Next.js App Router pages and layouts
- `app/layout.tsx` - Root layout with Geist font configuration
- `app/page.tsx` - Main landing page
- `app/globals.css` - Global Tailwind styles

### Configuration
- TypeScript path mapping: `@/*` points to root directory
- ESLint extends Next.js core web vitals and TypeScript configs
- Uses turbopack in development for faster builds