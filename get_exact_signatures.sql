-- Get exact function signatures for all problematic functions
SELECT 
    p.proname as function_name,
    pg_get_function_identity_arguments(p.oid) as arguments,
    'ALTER FUNCTION public.' || p.proname || '(' || pg_get_function_identity_arguments(p.oid) || ') SET search_path = '''';' as fix_command
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND p.proname IN (
    'update_bookmark_count',
    'update_flower_count',
    'is_following',
    'get_follower_count',
    'user_can_comment_on_entry',
    'update_subscriber_count',
    'update_entry_count',
    'notify_comment_created',
    'update_updated_at_column',
    'check_photo_limit',
    'user_has_active_subscription',
    'get_writer_public_data',
    'get_writer_locked_entries',
    'update_post_credits_updated_at',
    'user_can_read_post',
    'consume_credit_for_post',
    'update_love_count',
    'get_entry_preview',
    'update_hourly_love_stats',
    'get_top_diary_entries_hourly'
)
ORDER BY p.proname;