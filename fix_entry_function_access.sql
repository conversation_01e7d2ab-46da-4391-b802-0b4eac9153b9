-- Fix get_entry_preview function to work with new RLS policies
-- The function needs to bypass RLS or have proper policies to read diary_entries

-- Option 1: Make the function truly bypass <PERSON>LS
CREATE OR REPLACE FUNCTION get_entry_preview(
    entry_id UUID,
    viewer_id UUID DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    title TEXT,
    body_md TEXT,
    is_free BOOLEAN,
    is_hidden BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    user_id UUID,
    writer_id UUID,
    writer_name TEXT,
    writer_custom_url TEXT,
    writer_price INTEGER,
    can_read_full BOOLEAN,
    love_count INTEGER
) AS $$
BEGIN
    -- Explicitly set role to bypass <PERSON><PERSON> for this function
    SET LOCAL row_security = off;
    
    RETURN QUERY
    SELECT 
        de.id,
        de.title,
        de.body_md,
        de.is_free,
        de.is_hidden,
        de.created_at,
        de.updated_at,
        de.user_id,
        de.user_id as writer_id,
        u.name as writer_name,
        u.custom_url as writer_custom_url,
        COALESCE(u.price_monthly, 999) as writer_price,
        CASE 
            -- Entry is free
            WHEN de.is_free = true THEN true
            -- Viewer is the writer
            WHEN viewer_id = de.user_id THEN true
            -- Viewer has credits for this writer
            WHEN viewer_id IS NOT NULL AND EXISTS(
                SELECT 1 FROM post_credits pc 
                WHERE pc.user_id = viewer_id 
                AND pc.writer_id = de.user_id 
                AND pc.credits_remaining > 0
            ) THEN true
            -- Viewer has already read this post
            WHEN viewer_id IS NOT NULL AND EXISTS(
                SELECT 1 FROM post_reads pr 
                WHERE pr.user_id = viewer_id 
                AND pr.diary_entry_id = de.id
            ) THEN true
            -- Default: cannot read full
            ELSE false
        END as can_read_full,
        COALESCE((
            SELECT COUNT(*)::INTEGER 
            FROM loves l 
            WHERE l.diary_entry_id = de.id
        ), 0) as love_count
    FROM diary_entries de
    JOIN users u ON de.user_id = u.id
    WHERE de.id = entry_id
    AND de.is_hidden = false; -- Only show published entries
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure proper permissions
GRANT EXECUTE ON FUNCTION get_entry_preview(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_entry_preview(UUID, UUID) TO anon;