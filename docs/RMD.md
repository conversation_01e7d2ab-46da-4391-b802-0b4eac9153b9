PROJECT NAME: Only Diary
Theme: Paperwhite — light-grey canvas (#F7F7F7), black serif headings, charcoal body text, wide line-height, zero visual clutter.
Mission: a mobile-first diary hub where readers get one free taste of each writer’s work, then decide if the monthly paywall (price set by the writer) is worth it. Writers keep 80 % of every subscription/tip; the platform keeps 20 %.

R · M · D (Requirements • Modules • Deliverables)
1 REQUIREMENTS
Area	Must-Have	Keep-It-Simple Notes
User Roles	visitor, subscriber, writer, admin	Single enum in users table.
Paywall Logic	• Each writer designates one free entry (default = their first).
• Visitors can read that full entry without subscribing.
• All other entries show first 120 chars + “Unlock $X/mo”.	No cost to writers.
Subscription Pricing	Writer sets $2 – $50/mo.	Numeric price_monthly field; server-side validation.
Revenue Split	80 % writer / 20 % platform	Stripe Connect destination charges.
Diary Post Limits	Up to 20 photos + 20 000 chars per entry.	Guard in both client + server.
Comments	Subscribers only; soft-delete moderation.	is_deleted flag, not hard-remove.
Donations	Quick-pay buttons ($2, $5, $10, custom).	Re-use writer’s Stripe account.
AI First-Pass Moderation	All photos run through AWS Rekognition NSFW. Hard-block violations;
flag borderline content for admin review.	~$1 per 1 000 images.
Self-Running Ops	Automated payouts, daily digests, auto-hide after N flags, nightly backups.	Supabase cron + Stripe webhooks.
Accessibility	WCAG AA colors, alt text required on photo upload.	Drop upload if no alt text.

2 MODULES
Tag	Module	Key Endpoints / Components
A	Auth & Profile	/api/auth/* (Supabase Auth)
/profile page: avatar, bio, price slider, earnings.
B	Diary Engine	/api/diary CRUD (Markdown)
/api/photo (signed upload URL → Supabase Storage → Rekognition → publish on “approved”).
DiaryEntry component: renders MD + gallery lightbox.
C	Paywall & Subscriptions	/api/subscribe (Stripe Checkout) → webhook stamps subscriptions.active_until.
Client hook usePaywall(writerId) handles “teaser vs full”.
D	Comments	/api/comment CRUD; guard: user must have active sub for that writer.
E	Donations	/api/donate → Stripe PaymentIntent with metadata.
F	Admin Panel	Routes /admin/* (role check). Sub-pages: Flagged Content, Revenue, Users, Settings.
G	Background Jobs	Supabase Edge Functions:
• enforceSubscriptionStatus() (every 15 min)
• sendDailyDigestEmails() (00:05 UTC)
• weeklyBackupToS3() (Sun 03:00 UTC).

Minimal Database
sql
Copy
Edit
users(id, email, role, name, avatar, bio, price_monthly)
diary_entries(id, user_id, title, body_md, is_free, created_at, is_hidden)
photos(id, diary_id, url, moderation_status['approved','flagged','rejected'])
subscriptions(id, subscriber_id, writer_id, active_until, stripe_sub_id)
payments(id, payer_id, writer_id, kind['sub','donation'], amount_cents, stripe_id, created_at)
comments(id, diary_id, user_id, body, created_at, is_deleted)
flags(id, diary_entry_id, reporter_id, reason, created_at, resolved)
settings(global_fee_percent DEFAULT 20)
3 DELIVERABLES
Layer	Stack	Output
Frontend	Next.js 14 + TypeScript + Tailwind CSS	Pages:
• Home / (trending writers)
• Diary /d/[id] (content, comments, donate)
• Writer /u/[id] (bio, free post, subscribe CTA)
• Auth /login, /register
Backend	Supabase (Postgres + Auth + Storage)	Edge Functions (/api/*) with RLS.
Payments	Stripe Connect (Standard)	Auto-split 80/20, weekly automatic payouts.
Image Moderation	AWS Rekognition DetectModerationLabels	Lambda-style Edge Function before moderation_status='approved'.
Observability	Vercel Analytics + Slack webhooks	/api/health ping every 5 min.
Docs	README.md, .env.example, Postman collection	Copy-paste ready for Claude Code.

4 BUILD ORDER (for Claude Code)
Scaffold Next.js + Tailwind + Supabase client.

Create DB with schema above; enable RLS.

Auth (email/password).

Diary CRUD + free-entry flag.

Photo Upload with Rekognition pipeline.

Stripe Connect subs + donations; webhook handlers.

Paywall Hook & teaser rendering.

Comments (subscriber-only).

Admin Panel (flag queue, revenue).

Cron Jobs + Slack alerts.

Polish UI to Paperwhite spec; Lighthouse audit.

5 ACCEPTANCE CHECKLIST
 Visitor can fully read exactly one free entry per writer; others locked.

 Subscribing unlocks all current + future posts until sub expires/fails.

 Writers upload ≤ 20 photos; Rekognition auto-approves or flags.

 Comments visible only to subscribers.

 Tips & subs route 80 % to writer, 20 % to platform; payouts weekly.

 Any diary auto-hidden after N flags (config setting).

 Admin dashboard shows flagged list, revenue charts, user role toggle.


 Nightly backups & health pings in place.