-- OnlyDiary Improved Schema
-- Clean business logic for diary entries + book projects

-- ============================================================================
-- USERS & AUTHENTICATION
-- ============================================================================

CREATE TYPE user_role AS ENUM ('visitor', 'subscriber', 'writer', 'admin');

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    role user_role DEFAULT 'visitor',
    name TEXT,
    avatar TEXT,
    bio TEXT,
    
    -- Subscription pricing for diary entries
    subscription_price_monthly INTEGER CHECK (subscription_price_monthly >= 299 AND subscription_price_monthly <= 5000), -- $2.99-$50 in cents
    
    -- Stripe Connect for payouts
    stripe_account_id TEXT,
    
    -- Profile customization
    custom_url TEXT UNIQUE,
    profile_picture_url TEXT,
    social_twitter TEXT,
    social_instagram TEXT,
    social_website TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- DIARY ENTRIES (Subscription Model)
-- ============================================================================

CREATE TABLE diary_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    body_md TEXT NOT NULL CHECK (LENGTH(body_md) <= 20000), -- 20k char limit
    
    -- Monetization options
    is_free BOOLEAN DEFAULT FALSE,           -- Free for everyone
    is_hidden BOOLEAN DEFAULT FALSE,         -- Hidden from public timeline
    allow_donations BOOLEAN DEFAULT TRUE,    -- Show donation button
    
    -- Engagement
    view_count INTEGER DEFAULT 0,
    love_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- BOOK PROJECTS (One-time Purchase Model)
-- ============================================================================

CREATE TYPE project_pricing AS ENUM ('free', 'per_chapter', 'full_project', 'donation_based');

CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    cover_image_url TEXT,
    genre TEXT,
    
    -- Privacy & completion
    is_private BOOLEAN DEFAULT FALSE,
    is_complete BOOLEAN DEFAULT FALSE,
    
    -- Pricing model
    pricing_type project_pricing DEFAULT 'per_chapter',
    project_price INTEGER CHECK (project_price >= 0), -- Full project price in cents
    default_chapter_price INTEGER CHECK (default_chapter_price >= 0), -- Default per chapter in cents
    allow_donations BOOLEAN DEFAULT TRUE,
    
    -- Stats (auto-calculated)
    total_chapters INTEGER DEFAULT 0,
    total_words INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE chapters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT DEFAULT '',
    chapter_number INTEGER NOT NULL,
    word_count INTEGER DEFAULT 0,
    
    -- Publishing & pricing
    is_published BOOLEAN DEFAULT FALSE,
    is_free BOOLEAN DEFAULT FALSE,           -- Override project pricing for this chapter
    chapter_price INTEGER,                   -- Override default chapter price
    allow_donations BOOLEAN DEFAULT TRUE,    -- Show donation button
    
    -- Engagement
    love_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(project_id, chapter_number)
);

-- ============================================================================
-- SUBSCRIPTIONS (For Diary Entries)
-- ============================================================================

CREATE TYPE subscription_status AS ENUM ('active', 'cancelled', 'expired');

CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reader_id UUID REFERENCES users(id) ON DELETE CASCADE,
    writer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status subscription_status DEFAULT 'active',
    
    -- Stripe subscription details
    stripe_subscription_id TEXT UNIQUE,
    current_period_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    current_period_end TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '30 days',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(reader_id, writer_id)
);

-- ============================================================================
-- PURCHASES (For Book Projects/Chapters)
-- ============================================================================

CREATE TYPE purchase_type AS ENUM ('chapter', 'full_project');

CREATE TABLE purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    buyer_id UUID REFERENCES users(id) ON DELETE SET NULL,
    seller_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- What was purchased
    purchase_type purchase_type NOT NULL,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES chapters(id) ON DELETE SET NULL, -- NULL for full project purchases
    
    -- Payment details
    amount_cents INTEGER NOT NULL CHECK (amount_cents > 0),
    stripe_payment_id TEXT UNIQUE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate purchases
    UNIQUE(buyer_id, project_id, chapter_id)
);

-- ============================================================================
-- DONATIONS (For Both Diary Entries and Book Content)
-- ============================================================================

CREATE TYPE donation_target AS ENUM ('diary_entry', 'chapter', 'project', 'creator');

CREATE TABLE donations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    donor_id UUID REFERENCES users(id) ON DELETE SET NULL,
    recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- What the donation is for
    target_type donation_target NOT NULL,
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE SET NULL,
    project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
    chapter_id UUID REFERENCES chapters(id) ON DELETE SET NULL,
    
    -- Donation details
    amount_cents INTEGER NOT NULL CHECK (amount_cents >= 100), -- Minimum $1.00
    message TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    stripe_payment_id TEXT UNIQUE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- UNIFIED PAYMENTS TABLE (All Revenue Tracking)
-- ============================================================================

CREATE TYPE payment_kind AS ENUM ('subscription', 'chapter_purchase', 'project_purchase', 'donation');

CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payer_id UUID REFERENCES users(id) ON DELETE SET NULL,
    recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Payment classification
    kind payment_kind NOT NULL,
    amount_cents INTEGER NOT NULL CHECK (amount_cents > 0),
    platform_fee_cents INTEGER NOT NULL CHECK (platform_fee_cents >= 0),
    creator_earnings_cents INTEGER NOT NULL CHECK (creator_earnings_cents >= 0),
    
    -- Related records
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE SET NULL,
    purchase_id UUID REFERENCES purchases(id) ON DELETE SET NULL,
    donation_id UUID REFERENCES donations(id) ON DELETE SET NULL,
    
    -- Stripe details
    stripe_payment_id TEXT UNIQUE,
    stripe_transfer_id TEXT, -- For creator payouts
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- SUPPORTING TABLES
-- ============================================================================

-- Photos for diary entries
CREATE TABLE photos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    alt_text TEXT NOT NULL,
    moderation_status TEXT DEFAULT 'approved' CHECK (moderation_status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Comments on diary entries
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    body TEXT NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User favorites/bookmarks
CREATE TABLE favorite_creators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    writer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, writer_id)
);

-- ============================================================================
-- BUSINESS LOGIC SUMMARY
-- ============================================================================

/*
DIARY ENTRIES:
- Free entries: Anyone can read
- Paid entries: Subscribers only (monthly subscription)
- Donations: 5% platform fee, 95% to creator
- Subscriptions: 20% platform fee, 80% to creator

BOOK PROJECTS:
- Free projects: Anyone can read, donations available
- Per-chapter: Buy individual chapters
- Full project: Buy entire book at once
- Donation-based: Free to read, donations encouraged
- Sales: 20% platform fee, 80% to creator

REVENUE TRACKING:
- All payments tracked in unified payments table
- Platform fees calculated automatically
- Creator payouts via Stripe Connect
- Clear separation between subscription and purchase revenue
*/
