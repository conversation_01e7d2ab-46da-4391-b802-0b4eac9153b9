# Supabase Email Templates for OnlyDiary

## Email Confirmation Template

**Subject:** Confirm your OnlyDiary account

**HTML Body:**
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm Your OnlyDiary Account</title>
</head>
<body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="text-align: center; padding: 40px 20px 30px; background-color: #ffffff;">
            <h1 style="margin: 0; font-size: 28px; font-weight: bold; color: #1f2937; font-family: Georgia, serif;">
                OnlyDiary
            </h1>
            <p style="margin: 8px 0 0; color: #6b7280; font-size: 14px; font-style: italic;">
                Where Life Becomes Literature
            </p>
        </div>

        <!-- Content -->
        <div style="padding: 0 40px 40px;">
            <h2 style="color: #1f2937; font-size: 24px; margin-bottom: 20px; font-family: Georgia, serif;">
                Confirm Your Account
            </h2>
            
            <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 24px;">
                Welcome to OnlyDiary! Please confirm your email address to complete your account setup and start sharing your stories.
            </p>

            <div style="text-align: center; margin: 32px 0;">
                <a href="{{ .ConfirmationURL }}" 
                   style="display: inline-block; background-color: #1f2937; color: #ffffff; text-decoration: none; padding: 14px 28px; border-radius: 8px; font-weight: 600; font-size: 16px;">
                    Confirm Email Address
                </a>
            </div>

            <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin-top: 32px;">
                If you didn't create an OnlyDiary account, you can safely ignore this email.
            </p>

            <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin-top: 16px;">
                If the button doesn't work, copy and paste this link into your browser:<br>
                <span style="word-break: break-all;">{{ .ConfirmationURL }}</span>
            </p>
        </div>

        <!-- Footer -->
        <div style="background-color: #f9fafb; padding: 20px 40px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
                © 2025 OnlyDiary. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
```

## Password Reset Template

**Subject:** Reset your OnlyDiary password

**HTML Body:**
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your OnlyDiary Password</title>
</head>
<body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="text-align: center; padding: 40px 20px 30px; background-color: #ffffff;">
            <h1 style="margin: 0; font-size: 28px; font-weight: bold; color: #1f2937; font-family: Georgia, serif;">
                OnlyDiary
            </h1>
            <p style="margin: 8px 0 0; color: #6b7280; font-size: 14px; font-style: italic;">
                Where Life Becomes Literature
            </p>
        </div>

        <!-- Content -->
        <div style="padding: 0 40px 40px;">
            <h2 style="color: #1f2937; font-size: 24px; margin-bottom: 20px; font-family: Georgia, serif;">
                Reset Your Password
            </h2>
            
            <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 24px;">
                We received a request to reset your OnlyDiary password. Click the button below to create a new password.
            </p>

            <div style="text-align: center; margin: 32px 0;">
                <a href="{{ .ConfirmationURL }}" 
                   style="display: inline-block; background-color: #dc2626; color: #ffffff; text-decoration: none; padding: 14px 28px; border-radius: 8px; font-weight: 600; font-size: 16px;">
                    Reset Password
                </a>
            </div>

            <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin-top: 32px;">
                If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged.
            </p>

            <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin-top: 16px;">
                This link will expire in 24 hours for security reasons.
            </p>

            <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin-top: 16px;">
                If the button doesn't work, copy and paste this link into your browser:<br>
                <span style="word-break: break-all;">{{ .ConfirmationURL }}</span>
            </p>
        </div>

        <!-- Footer -->
        <div style="background-color: #f9fafb; padding: 20px 40px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
                © 2025 OnlyDiary. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
```

## Magic Link Template

**Subject:** Sign in to OnlyDiary

**HTML Body:**
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign in to OnlyDiary</title>
</head>
<body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="text-align: center; padding: 40px 20px 30px; background-color: #ffffff;">
            <h1 style="margin: 0; font-size: 28px; font-weight: bold; color: #1f2937; font-family: Georgia, serif;">
                OnlyDiary
            </h1>
            <p style="margin: 8px 0 0; color: #6b7280; font-size: 14px; font-style: italic;">
                Where Life Becomes Literature
            </p>
        </div>

        <!-- Content -->
        <div style="padding: 0 40px 40px;">
            <h2 style="color: #1f2937; font-size: 24px; margin-bottom: 20px; font-family: Georgia, serif;">
                Sign In to Your Account
            </h2>
            
            <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 24px;">
                Click the button below to securely sign in to your OnlyDiary account.
            </p>

            <div style="text-align: center; margin: 32px 0;">
                <a href="{{ .ConfirmationURL }}" 
                   style="display: inline-block; background-color: #059669; color: #ffffff; text-decoration: none; padding: 14px 28px; border-radius: 8px; font-weight: 600; font-size: 16px;">
                    Sign In to OnlyDiary
                </a>
            </div>

            <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin-top: 32px;">
                If you didn't request this sign-in link, you can safely ignore this email.
            </p>

            <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin-top: 16px;">
                This link will expire in 1 hour for security reasons.
            </p>
        </div>

        <!-- Footer -->
        <div style="background-color: #f9fafb; padding: 20px 40px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
                © 2025 OnlyDiary. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
```

## Setup Instructions

1. **Go to Supabase Dashboard** → **Authentication** → **Email Templates**
2. **Replace each template** with the branded versions above
3. **Update redirect URLs** to point to your domain:
   - Site URL: `https://onlydiary.app`
   - Redirect URLs: `https://onlydiary.app/auth/callback`
4. **Test email delivery** with a test account
