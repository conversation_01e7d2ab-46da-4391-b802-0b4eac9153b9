-- Book Store Schema Extensions for OnlyDiary
-- Builds on existing projects/chapters tables

-- Add book-specific fields to existing projects table
ALTER TABLE projects ADD COLUMN IF NOT EXISTS book_type TEXT CHECK (book_type IN ('diary_collection', 'standalone_book', 'serial_novel')) DEFAULT 'standalone_book';
ALTER TABLE projects ADD COLUMN IF NOT EXISTS sales_count INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS daily_sales INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS bestseller_rank INTEGER;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS featured_until TIMESTAMP WITH TIME ZONE;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS preview_chapters INTEGER DEFAULT 1; -- How many chapters are free
ALTER TABLE projects ADD COLUMN IF NOT EXISTS reading_time_minutes INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}';

-- Book purchases table (separate from subscriptions)
CREATE TABLE IF NOT EXISTS book_purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    purchase_price_cents INTEGER NOT NULL,
    stripe_payment_id TEXT,
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, project_id) -- One purchase per user per book
);

-- Reading progress tracking
CREATE TABLE IF NOT EXISTS reading_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
    progress_percentage DECIMAL(5,2) DEFAULT 0, -- 0.00 to 100.00
    last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reading_time_seconds INTEGER DEFAULT 0,
    
    UNIQUE(user_id, project_id, chapter_id)
);

-- Bookmarks
CREATE TABLE IF NOT EXISTS bookmarks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
    position_in_chapter INTEGER DEFAULT 0, -- Character position
    note TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Daily bestsellers tracking
CREATE TABLE IF NOT EXISTS daily_bestsellers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    sales_count INTEGER NOT NULL,
    rank INTEGER NOT NULL,
    genre TEXT,
    
    UNIQUE(date, project_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_book_purchases_user_project ON book_purchases(user_id, project_id);
CREATE INDEX IF NOT EXISTS idx_reading_progress_user_project ON reading_progress(user_id, project_id);
CREATE INDEX IF NOT EXISTS idx_daily_bestsellers_date_rank ON daily_bestsellers(date, rank);
CREATE INDEX IF NOT EXISTS idx_projects_sales_count ON projects(sales_count DESC);
CREATE INDEX IF NOT EXISTS idx_projects_book_type ON projects(book_type);
CREATE INDEX IF NOT EXISTS idx_projects_tags ON projects USING GIN(tags);
