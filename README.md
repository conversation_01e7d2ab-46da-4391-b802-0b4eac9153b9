This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Testing Vertical Slice #1 - Writer Profile & Paywall

To test the writer profile page and paywall functionality locally:

### Prerequisites
1. Set up your Supabase project and configure environment variables in `.env.local`:
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

2. Run the database migrations in your Supabase SQL editor:
   ```sql
   -- Execute the files in order:
   -- supabase/migrations/001_initial_schema.sql
   -- supabase/migrations/002_rls_policies.sql  
   -- supabase/migrations/003_writer_page_helpers.sql
   ```

### Test Data Setup
Insert test data in Supabase SQL editor:

```sql
-- Create a test writer
INSERT INTO users (id, email, role, name, bio, price_monthly) VALUES 
('123e4567-e89b-12d3-a456-426614174000', '<EMAIL>', 'writer', 'Jane Doe', 'A passionate writer sharing daily thoughts', 1000);

-- Create a free entry
INSERT INTO diary_entries (id, user_id, title, body_md, is_free) VALUES 
('223e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174000', 'My First Free Entry', 'Welcome to my diary! This is my free entry where I share my thoughts about writing and life. Feel free to read this completely and consider subscribing for more content.', true);

-- Create some locked entries
INSERT INTO diary_entries (id, user_id, title, body_md, is_free) VALUES 
('323e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174000', 'Deep Thoughts on Creativity', 'This is a longer entry about creativity and inspiration. Only subscribers can read the full content. Here I share my personal insights...', false),
('423e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174000', 'Morning Reflections', 'Another entry with deeper thoughts that requires subscription. I talk about my morning routine and how it affects my writing...', false);
```

### Testing Steps
1. Start the development server: `npm run dev`
2. Visit: `http://localhost:3000/u/123e4567-e89b-12d3-a456-426614174000`
3. Verify you can see:
   - Writer profile (name, bio, subscription price)
   - One free entry shown in full
   - Locked entries showing only teasers (120 chars + "...")
   - Subscribe and Unlock buttons (console.log placeholders)

### Expected Behavior
- ✅ Free entry displays completely
- ✅ Locked entries show title + date + 120-char teaser
- ✅ Subscribe button shows writer's monthly price  
- ✅ Unlock buttons show pricing
- ✅ All buttons log 'todo' messages to browser console
- ✅ Paperwhite theme: light grey background (#F7F7F7), serif fonts, clean cards
