import { createSupabase<PERSON>lient } from './client'

// Enhanced storage operations with retry logic
const DEFAULT_RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2
}

const sleep = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms))

const getRetryDelay = (attempt: number): number => {
  const delay = DEFAULT_RETRY_CONFIG.baseDelay * Math.pow(DEFAULT_RETRY_CONFIG.backoffMultiplier, attempt - 1)
  return Math.min(delay, DEFAULT_RETRY_CONFIG.maxDelay)
}

const withStorageRetry = async <T>(
  operation: () => Promise<T>,
  operationName: string
): Promise<T> => {
  let lastError: unknown
  
  for (let attempt = 1; attempt <= DEFAULT_RETRY_CONFIG.maxRetries + 1; attempt++) {
    try {
      const result = await operation()
      
      if (attempt > 1) {
        console.log(`✅ ${operationName} succeeded on attempt ${attempt}`)
      }
      
      return result
    } catch (error) {
      lastError = error
      
      // Don't retry on certain errors
      if (error?.status === 400 || error?.status === 401 || error?.status === 403) {
        throw error
      }
      
      if (attempt > DEFAULT_RETRY_CONFIG.maxRetries) {
        console.error(`❌ ${operationName} failed after ${DEFAULT_RETRY_CONFIG.maxRetries} retries:`, error)
        throw error
      }
      
      const delay = getRetryDelay(attempt)
      console.warn(`⚠️ ${operationName} failed (attempt ${attempt}/${DEFAULT_RETRY_CONFIG.maxRetries}). Retrying in ${delay}ms...`)
      
      await sleep(delay)
    }
  }
  
  throw lastError
}

// Enhanced storage utilities
export const enhancedStorage = {
  upload: async (bucket: string, path: string, file: File | Blob, options?: unknown) => {
    const supabase = createSupabaseClient()
    
    return withStorageRetry(
      () => supabase.storage.from(bucket).upload(path, file, options),
      `storage.upload to ${bucket}/${path}`
    )
  },

  download: async (bucket: string, path: string) => {
    const supabase = createSupabaseClient()
    
    return withStorageRetry(
      () => supabase.storage.from(bucket).download(path),
      `storage.download from ${bucket}/${path}`
    )
  },

  getPublicUrl: (bucket: string, path: string) => {
    const supabase = createSupabaseClient()
    return supabase.storage.from(bucket).getPublicUrl(path)
  },

  createSignedUrl: async (bucket: string, path: string, expiresIn: number) => {
    const supabase = createSupabaseClient()
    
    return withStorageRetry(
      () => supabase.storage.from(bucket).createSignedUrl(path, expiresIn),
      `storage.createSignedUrl for ${bucket}/${path}`
    )
  },

  remove: async (bucket: string, paths: string[]) => {
    const supabase = createSupabaseClient()
    
    return withStorageRetry(
      () => supabase.storage.from(bucket).remove(paths),
      `storage.remove from ${bucket}`
    )
  },

  list: async (bucket: string, path?: string, options?: unknown) => {
    const supabase = createSupabaseClient()
    
    return withStorageRetry(
      () => supabase.storage.from(bucket).list(path, options),
      `storage.list from ${bucket}/${path || ''}`
    )
  }
}

// Convenience functions for specific buckets
export const projectCoverStorage = {
  upload: (path: string, file: File | Blob, options?: unknown) => 
    enhancedStorage.upload('project-covers', path, file, options),
  
  getPublicUrl: (path: string) => 
    enhancedStorage.getPublicUrl('project-covers', path),
  
  remove: (paths: string[]) => 
    enhancedStorage.remove('project-covers', paths)
}

export const photoStorage = {
  upload: (path: string, file: File | Blob, options?: unknown) => 
    enhancedStorage.upload('photos', path, file, options),
  
  getPublicUrl: (path: string) => 
    enhancedStorage.getPublicUrl('photos', path),
  
  remove: (paths: string[]) => 
    enhancedStorage.remove('photos', paths)
}

export const profilePictureStorage = {
  upload: (path: string, file: File | Blob, options?: unknown) => 
    enhancedStorage.upload('profile-pictures', path, file, options),
  
  getPublicUrl: (path: string) => 
    enhancedStorage.getPublicUrl('profile-pictures', path),
  
  remove: (paths: string[]) => 
    enhancedStorage.remove('profile-pictures', paths)
}
