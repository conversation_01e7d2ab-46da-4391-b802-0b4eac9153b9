// Video watermark utilities for OnlyDiary

export interface WatermarkConfig {
  text: string
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  fontSize: number
  opacity: number
  color: string
  backgroundColor: string
  padding: number
  borderRadius: number
}

export const defaultWatermarkConfig: WatermarkConfig = {
  text: 'Only on OnlyDiary.app',
  position: 'bottom-right',
  fontSize: 14,
  opacity: 0.8,
  color: '#ffffff',
  backgroundColor: 'rgba(0, 0, 0, 0.6)',
  padding: 8,
  borderRadius: 6
}

// Generate CSS for video watermark overlay
export function generateWatermarkCSS(config: WatermarkConfig = defaultWatermarkConfig): string {
  const positionStyles = {
    'top-left': 'top: 16px; left: 16px;',
    'top-right': 'top: 16px; right: 16px;',
    'bottom-left': 'bottom: 16px; left: 16px;',
    'bottom-right': 'bottom: 16px; right: 16px;'
  }

  return `
    .onlydiary-watermark {
      position: absolute;
      ${positionStyles[config.position]}
      z-index: 1000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: ${config.fontSize}px;
      font-weight: 700;
      color: ${config.color};
      background: ${config.backgroundColor};
      backdrop-filter: blur(4px);
      padding: ${config.padding}px;
      border-radius: ${config.borderRadius}px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      opacity: ${config.opacity};
      pointer-events: none;
      user-select: none;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    
    .onlydiary-watermark::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(236, 72, 153, 0.2));
      border-radius: ${config.borderRadius}px;
      z-index: -1;
    }
  `
}

// Generate HTML for watermark overlay
export function generateWatermarkHTML(config: WatermarkConfig = defaultWatermarkConfig): string {
  return `
    <div class="onlydiary-watermark">
      ${config.text}
    </div>
  `
}

// Apply watermark to video element (for client-side)
export function applyVideoWatermark(
  videoElement: HTMLVideoElement, 
  config: WatermarkConfig = defaultWatermarkConfig
): HTMLDivElement {
  const container = videoElement.parentElement
  if (!container) {
    throw new Error('Video element must have a parent container')
  }

  // Ensure container is positioned relatively
  if (getComputedStyle(container).position === 'static') {
    container.style.position = 'relative'
  }

  // Create watermark element
  const watermark = document.createElement('div')
  watermark.className = 'onlydiary-watermark'
  watermark.textContent = config.text
  
  // Apply styles
  const styles = {
    position: 'absolute',
    zIndex: '1000',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: `${config.fontSize}px`,
    fontWeight: '700',
    color: config.color,
    background: config.backgroundColor,
    backdropFilter: 'blur(4px)',
    padding: `${config.padding}px`,
    borderRadius: `${config.borderRadius}px`,
    border: '1px solid rgba(255, 255, 255, 0.2)',
    opacity: config.opacity.toString(),
    pointerEvents: 'none',
    userSelect: 'none',
    letterSpacing: '0.5px',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)'
  }

  // Position styles
  const positionStyles = {
    'top-left': { top: '16px', left: '16px' },
    'top-right': { top: '16px', right: '16px' },
    'bottom-left': { bottom: '16px', left: '16px' },
    'bottom-right': { bottom: '16px', right: '16px' }
  }

  Object.assign(watermark.style, styles, positionStyles[config.position])

  // Add to container
  container.appendChild(watermark)

  return watermark
}

// Remove watermark from video
export function removeVideoWatermark(watermarkElement: HTMLDivElement): void {
  if (watermarkElement && watermarkElement.parentElement) {
    watermarkElement.parentElement.removeChild(watermarkElement)
  }
}

// Get watermark text variations
export const watermarkTexts = {
  standard: 'Only on OnlyDiary.app',
  short: 'OnlyDiary.app',
  branded: '✨ OnlyDiary.app',
  exclusive: 'Exclusive on OnlyDiary.app',
  premium: '💎 OnlyDiary.app Premium'
}

// Preset configurations
export const watermarkPresets = {
  subtle: {
    ...defaultWatermarkConfig,
    opacity: 0.6,
    fontSize: 12
  },
  standard: defaultWatermarkConfig,
  prominent: {
    ...defaultWatermarkConfig,
    opacity: 0.9,
    fontSize: 16,
    padding: 12
  },
  minimal: {
    ...defaultWatermarkConfig,
    text: watermarkTexts.short,
    fontSize: 11,
    opacity: 0.7,
    padding: 6
  }
}
