import { Resend } from 'resend'

// Initialize Resend client
const resend = new Resend(process.env.RESEND_API_KEY!)

export interface EmailOptions {
  to: string
  subject: string
  html?: string
  text?: string
  from?: string
  tag?: string
}

// Send a simple email
export async function sendEmail(options: EmailOptions) {
  try {
    const fromEmail = options.from || process.env.RESEND_FROM_EMAIL!
    
    const result = await resend.emails.send({
      from: fromEmail,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
      tags: options.tag ? [{ name: 'type', value: options.tag }] : undefined,
    })
    
    console.log('✅ Email sent successfully:', {
      to: options.to,
      subject: options.subject,
      id: result.data?.id
    })
    
    return { success: true, id: result.data?.id }
  } catch (error) {
    console.error('❌ Failed to send email:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Test email connection
export async function testEmailConnection() {
  try {
    // Send a test email to verify connection
    const testResult = await resend.emails.send({
      from: process.env.RESEND_FROM_EMAIL!,
      to: '<EMAIL>', // Resend's test email
      subject: 'Connection Test',
      html: '<p>Testing Resend connection</p>',
    })
    
    console.log('✅ Resend connection successful')
    return { success: true, id: testResult.data?.id }
  } catch (error) {
    console.error('❌ Resend connection failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Email templates for common use cases
export const EmailTemplates = {
  // Welcome email for new users
  welcome: (userName: string, userRole: 'subscriber' | 'writer') => ({
    subject: `Welcome to OnlyDiary, ${userName}! 🌟`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to OnlyDiary</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="text-align: center; padding: 40px 20px 30px; background-color: #ffffff;">
            <h1 style="color: #1f2937; font-size: 28px; margin: 0; font-weight: normal;">Welcome to OnlyDiary</h1>
            <p style="color: #6b7280; font-size: 16px; margin: 10px 0 0 0; font-style: italic;">Where Life Becomes Literature</p>
          </div>
          
          <!-- Main Content -->
          <div style="padding: 0 20px 40px;">
            <div style="background-color: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 30px;">
              <h2 style="color: #1f2937; font-size: 20px; margin: 0 0 15px 0;">Hi ${userName}! 👋</h2>
              <p style="color: #374151; line-height: 1.6; margin: 0 0 15px 0; font-size: 16px;">
                Thank you for joining OnlyDiary! You're now part of a community where creators share their most intimate thoughts and stories.
              </p>
              ${userRole === 'writer' ? `
                <p style="color: #374151; line-height: 1.6; margin: 0; font-size: 16px;">
                  As a <strong>Creator</strong>, you can start sharing your stories and building your audience. Your voice matters, and we can't wait to see what you create!
                </p>
              ` : `
                <p style="color: #374151; line-height: 1.6; margin: 0; font-size: 16px;">
                  As a <strong>Reader</strong>, you can discover amazing creators and support them by subscribing to their content. Get ready for some incredible stories!
                </p>
              `}
            </div>
            
            <!-- CTA Buttons -->
            <div style="text-align: center; margin-bottom: 30px;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/dashboard"
                 style="background-color: #1f2937; color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block; font-size: 16px; margin-bottom: 15px;">
                ${userRole === 'writer' ? 'Start Creating' : 'Discover Creators'}
              </a>
            </div>

            <!-- Viral Invite Section -->
            <div style="background-color: #fef3c7; border: 2px solid #f59e0b; padding: 25px; border-radius: 8px; margin-bottom: 25px; text-align: center;">
              <h3 style="color: #92400e; font-size: 18px; margin: 0 0 10px 0;">🌟 OnlyDiary is Better with Friends!</h3>
              <p style="color: #78350f; line-height: 1.5; margin: 0 0 15px 0; font-size: 15px;">
                Help us grow our intimate community! Invite your closest friends to discover amazing creators and authentic stories.
              </p>
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/invite-friends?ref=${userName.replace(/\s+/g, '')}"
                 style="background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block; font-size: 15px;">
                📱 Invite Friends via Text
              </a>
            </div>
            
            <!-- Footer -->
            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
              <p style="color: #6b7280; font-size: 14px; margin: 0; line-height: 1.5;">
                Questions? Reply to this email - we'd love to help!<br>
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}" style="color: #1f2937; text-decoration: none;">OnlyDiary.app</a>
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
Welcome to OnlyDiary, ${userName}!

Thank you for joining OnlyDiary! You're now part of a community where creators share their most intimate thoughts and stories.

${userRole === 'writer' 
  ? 'As a Creator, you can start sharing your stories and building your audience. Your voice matters!'
  : 'As a Reader, you can discover amazing creators and support them by subscribing to their content.'
}

Get started: ${process.env.NEXT_PUBLIC_SITE_URL}/dashboard

🌟 OnlyDiary is Better with Friends!
Help us grow our intimate community! Invite your closest friends to discover amazing creators and authentic stories.

Invite friends via text: ${process.env.NEXT_PUBLIC_SITE_URL}/invite-friends?ref=${userName.replace(/\s+/g, '')}

Questions? Reply to this email - we'd love to help!
OnlyDiary.app
    `,
    tag: 'welcome'
  }),

  // Password reset email
  passwordReset: (resetLink: string) => ({
    subject: 'Reset your OnlyDiary password',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="text-align: center; padding: 40px 20px 30px;">
            <h1 style="color: #1f2937; font-size: 24px; margin: 0;">Password Reset</h1>
          </div>
          
          <!-- Main Content -->
          <div style="padding: 0 20px 40px;">
            <div style="background-color: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 30px;">
              <p style="color: #374151; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;">
                We received a request to reset your OnlyDiary password. Click the button below to create a new password:
              </p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${resetLink}" 
                   style="background-color: #dc2626; color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block; font-size: 16px;">
                  Reset Password
                </a>
              </div>
              
              <p style="color: #6b7280; font-size: 14px; margin: 0; line-height: 1.5;">
                This link will expire in 1 hour. If you didn't request this reset, you can safely ignore this email.
              </p>
            </div>
            
            <!-- Footer -->
            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
              <p style="color: #6b7280; font-size: 14px; margin: 0;">
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}" style="color: #1f2937; text-decoration: none;">OnlyDiary.app</a>
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
Password Reset - OnlyDiary

We received a request to reset your OnlyDiary password. 

Reset your password: ${resetLink}

This link will expire in 1 hour. If you didn't request this reset, you can safely ignore this email.

OnlyDiary.app
    `,
    tag: 'password-reset'
  }),

  // Test email
  test: () => ({
    subject: 'OnlyDiary Email Test ✅',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Test</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <div style="text-align: center; padding: 40px 20px;">
            <div style="background-color: #f0fdf4; padding: 30px; border-radius: 8px; border: 2px solid #16a34a;">
              <h1 style="color: #16a34a; font-size: 24px; margin: 0 0 15px 0;">✅ Email Test Successful!</h1>
              <p style="color: #374151; line-height: 1.6; margin: 0; font-size: 16px;">
                Your OnlyDiary email system is working perfectly with Resend. Welcome emails, notifications, and digests are ready to go!
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
✅ Email Test Successful!

Your OnlyDiary email system is working perfectly with Resend. Welcome emails, notifications, and digests are ready to go!
    `,
    tag: 'test'
  })
}
