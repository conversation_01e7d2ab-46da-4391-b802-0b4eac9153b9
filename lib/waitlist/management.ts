// Waitlist management functions for authors
import { createSupabaseClient } from '@/lib/supabase/client'

export interface WaitlistEntry {
  id: string
  user_email: string
  user_name: string | null
  user_id: string | null
  notification_sent: boolean
  created_at: string
}

export interface ProjectWaitlistSummary {
  project_id: string
  project_title: string
  waitlist_count: number
  recent_signups: number // Last 7 days
}

// Get waitlist for a specific project (for project owners)
export async function getProjectWaitlist(projectId: string, userId: string): Promise<WaitlistEntry[]> {
  const supabase = createSupabaseClient()
  
  // Verify user owns this project
  const { data: project } = await supabase
    .from('projects')
    .select('id')
    .eq('id', projectId)
    .eq('user_id', userId)
    .single()
  
  if (!project) {
    throw new Error('Project not found or access denied')
  }
  
  const { data: waitlist, error } = await supabase
    .from('project_waitlist')
    .select('*')
    .eq('project_id', projectId)
    .order('created_at', { ascending: false })
  
  if (error) {
    console.error('Error fetching waitlist:', error)
    return []
  }
  
  return waitlist || []
}

// Get waitlist count for a project
export async function getWaitlistCount(projectId: string): Promise<number> {
  const supabase = createSupabaseClient()
  
  const { count, error } = await supabase
    .from('project_waitlist')
    .select('*', { count: 'exact', head: true })
    .eq('project_id', projectId)
  
  if (error) {
    console.error('Error getting waitlist count:', error)
    return 0
  }
  
  return count || 0
}

// Get all waitlists for an author (across all their projects)
export async function getAuthorWaitlists(userId: string): Promise<ProjectWaitlistSummary[]> {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select(`
      id,
      title,
      project_waitlist (
        id,
        created_at
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
  
  if (error) {
    console.error('Error fetching author waitlists:', error)
    return []
  }
  
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
  
  return (data || []).map(project => ({
    project_id: project.id,
    project_title: project.title,
    waitlist_count: project.project_waitlist.length,
    recent_signups: project.project_waitlist.filter(
      entry => new Date(entry.created_at) > sevenDaysAgo
    ).length
  }))
}

// Export waitlist to CSV for author
export async function exportWaitlistCSV(projectId: string, userId: string): Promise<string> {
  const waitlist = await getProjectWaitlist(projectId, userId)
  
  const headers = ['Email', 'Name', 'Signup Date', 'Notified']
  const rows = waitlist.map(entry => [
    entry.user_email,
    entry.user_name || '',
    new Date(entry.created_at).toLocaleDateString(),
    entry.notification_sent ? 'Yes' : 'No'
  ])
  
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(field => `"${field}"`).join(','))
  ].join('\n')
  
  return csvContent
}

// Manually notify waitlist (for when author makes project public)
export async function notifyProjectWaitlist(projectId: string, userId: string): Promise<{
  success: boolean
  notified: number
  total: number
  error?: string
}> {
  try {
    const supabase = createSupabaseClient()
    
    // Verify user owns this project
    const { data: project } = await supabase
      .from('projects')
      .select('id, title')
      .eq('id', projectId)
      .eq('user_id', userId)
      .single()
    
    if (!project) {
      return { success: false, notified: 0, total: 0, error: 'Project not found or access denied' }
    }
    
    // Get waitlist entries that haven't been notified
    const { data: waitlist, error: waitlistError } = await supabase
      .from('project_waitlist')
      .select('*')
      .eq('project_id', projectId)
      .eq('notification_sent', false)
    
    if (waitlistError) {
      return { success: false, notified: 0, total: 0, error: waitlistError.message }
    }
    
    if (!waitlist || waitlist.length === 0) {
      return { success: true, notified: 0, total: 0 }
    }
    
    // Here you would integrate with your email service
    // For now, we'll just mark them as notified
    const { error: updateError } = await supabase
      .from('project_waitlist')
      .update({ notification_sent: true })
      .eq('project_id', projectId)
      .eq('notification_sent', false)
    
    if (updateError) {
      return { success: false, notified: 0, total: waitlist.length, error: updateError.message }
    }
    
    return { success: true, notified: waitlist.length, total: waitlist.length }
    
  } catch (error) {
    return { 
      success: false, 
      notified: 0, 
      total: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

// Get waitlist analytics for author dashboard
export async function getWaitlistAnalytics(userId: string): Promise<{
  total_waitlist_signups: number
  projects_with_waitlists: number
  recent_signups: number
  top_project: { title: string; count: number } | null
}> {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select(`
      title,
      project_waitlist (
        id,
        created_at
      )
    `)
    .eq('user_id', userId)
  
  if (error || !data) {
    return {
      total_waitlist_signups: 0,
      projects_with_waitlists: 0,
      recent_signups: 0,
      top_project: null
    }
  }
  
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
  
  let totalSignups = 0
  let recentSignups = 0
  let projectsWithWaitlists = 0
  let topProject = { title: '', count: 0 }
  
  data.forEach(project => {
    const waitlistCount = project.project_waitlist.length
    if (waitlistCount > 0) {
      projectsWithWaitlists++
      totalSignups += waitlistCount
      
      if (waitlistCount > topProject.count) {
        topProject = { title: project.title, count: waitlistCount }
      }
      
      recentSignups += project.project_waitlist.filter(
        entry => new Date(entry.created_at) > sevenDaysAgo
      ).length
    }
  })
  
  return {
    total_waitlist_signups: totalSignups,
    projects_with_waitlists: projectsWithWaitlists,
    recent_signups: recentSignups,
    top_project: topProject.count > 0 ? topProject : null
  }
}
