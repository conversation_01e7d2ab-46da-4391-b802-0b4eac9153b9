/**
 * Generate a thumbnail image from a video URL
 * @param videoUrl - The URL of the video
 * @param timeInSeconds - Time in seconds to capture the thumbnail (default: 1)
 * @returns Promise<string> - Data URL of the generated thumbnail
 */
export function generateVideoThumbnail(
  videoUrl: string, 
  timeInSeconds: number = 1
): Promise<string> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    video.crossOrigin = 'anonymous'
    video.preload = 'metadata'
    video.muted = true // Required for some browsers
    
    video.onloadedmetadata = () => {
      // Set canvas dimensions to match video
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      
      // Seek to the desired time
      video.currentTime = Math.min(timeInSeconds, video.duration - 0.1)
    }
    
    video.onseeked = () => {
      try {
        // Draw the current frame to canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
        
        // Convert canvas to data URL
        const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8)
        
        // Clean up
        video.remove()
        canvas.remove()
        
        resolve(thumbnailDataUrl)
      } catch (error) {
        reject(error)
      }
    }
    
    video.onerror = () => {
      reject(new Error('Failed to load video for thumbnail generation'))
    }
    
    video.onloadstart = () => {
      console.log('Loading video for thumbnail:', videoUrl)
    }
    
    // Start loading the video
    video.src = videoUrl
    video.load()
  })
}

/**
 * Generate a thumbnail with specific aspect ratio
 * @param videoUrl - The URL of the video
 * @param aspectRatio - Desired aspect ratio (width/height)
 * @param timeInSeconds - Time in seconds to capture the thumbnail
 * @returns Promise<string> - Data URL of the generated thumbnail
 */
export function generateVideoThumbnailWithAspectRatio(
  videoUrl: string,
  aspectRatio: number = 4/3, // Default to match image cards
  timeInSeconds: number = 1
): Promise<string> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    // Don't set crossOrigin to avoid CORS issues with R2
    video.preload = 'metadata'
    video.muted = true
    video.playsInline = true // Better mobile support

    let hasMetadata = false
    let hasSeeked = false

    const cleanup = () => {
      try {
        video.pause()
        video.removeAttribute('src')
        video.load()
        video.remove()
      } catch (e) {
        // Ignore cleanup errors
      }
    }

    const generateThumbnail = () => {
      try {
        if (!hasMetadata || !video.videoWidth || !video.videoHeight) {
          throw new Error('Video metadata not available')
        }

        const videoAspectRatio = video.videoWidth / video.videoHeight

        // Calculate canvas dimensions to match desired aspect ratio
        const baseSize = 400
        let canvasWidth, canvasHeight
        let sourceX = 0, sourceY = 0, sourceWidth = video.videoWidth, sourceHeight = video.videoHeight

        if (videoAspectRatio > aspectRatio) {
          // Video is wider than desired ratio - crop sides
          canvasHeight = baseSize
          canvasWidth = baseSize * aspectRatio
          sourceWidth = video.videoHeight * aspectRatio
          sourceX = (video.videoWidth - sourceWidth) / 2
        } else {
          // Video is taller than desired ratio - crop top/bottom
          canvasWidth = baseSize * aspectRatio
          canvasHeight = baseSize
          sourceHeight = video.videoWidth / aspectRatio
          sourceY = (video.videoHeight - sourceHeight) / 2
        }

        canvas.width = canvasWidth
        canvas.height = canvasHeight

        // Draw the cropped frame to canvas
        ctx.drawImage(
          video,
          sourceX, sourceY, sourceWidth, sourceHeight,
          0, 0, canvas.width, canvas.height
        )

        // Convert canvas to data URL
        const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8)

        cleanup()
        resolve(thumbnailDataUrl)
      } catch (error) {
        cleanup()
        reject(error)
      }
    }

    video.onloadedmetadata = () => {
      hasMetadata = true
      console.log('Video metadata loaded:', {
        width: video.videoWidth,
        height: video.videoHeight,
        duration: video.duration
      })

      // Seek to the desired time
      const seekTime = Math.min(timeInSeconds, Math.max(0, video.duration - 0.1))
      video.currentTime = seekTime
    }

    video.onseeked = () => {
      if (!hasSeeked) {
        hasSeeked = true
        console.log('Video seeked to:', video.currentTime)
        generateThumbnail()
      }
    }

    video.oncanplay = () => {
      // Fallback if seeking doesn't work
      if (hasMetadata && !hasSeeked) {
        setTimeout(() => {
          if (!hasSeeked) {
            console.log('Using fallback thumbnail generation')
            generateThumbnail()
          }
        }, 500)
      }
    }

    video.onerror = (e) => {
      console.error('Video loading error:', e, 'URL:', videoUrl)
      cleanup()
      reject(new Error('Failed to load video for thumbnail generation'))
    }

    video.onabort = () => {
      console.warn('Video loading aborted')
      cleanup()
      reject(new Error('Video loading was aborted'))
    }

    // Timeout fallback
    const timeout = setTimeout(() => {
      cleanup()
      reject(new Error('Video thumbnail generation timed out'))
    }, 10000) // 10 second timeout

    const originalResolve = resolve
    const originalReject = reject
    resolve = (value) => {
      clearTimeout(timeout)
      originalResolve(value)
    }
    reject = (reason) => {
      clearTimeout(timeout)
      originalReject(reason)
    }

    // Start loading the video
    console.log('Loading video for thumbnail:', videoUrl)
    video.src = videoUrl
    video.load()
  })
}
