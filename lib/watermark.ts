/**
 * Watermarking utility for OnlyDiary
 * Adds "www.OnlyDiary.app" watermark to uploaded images
 */

export interface WatermarkOptions {
  text?: string
  fontSize?: number
  opacity?: number
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center'
  margin?: number
  color?: string
  fontFamily?: string
  quality?: number
}

const DEFAULT_OPTIONS: Required<WatermarkOptions> = {
  text: 'www.OnlyDiary.app',
  fontSize: 16,
  opacity: 0.7,
  position: 'bottom-right',
  margin: 20,
  color: '#ffffff',
  fontFamily: 'Arial, sans-serif',
  quality: 0.9
}

/**
 * Add watermark to an image file
 */
export async function addWatermarkToFile(
  file: File,
  options: WatermarkOptions = {}
): Promise<File> {
  const opts = { ...DEFAULT_OPTIONS, ...options }

  console.log('Adding watermark to file:', file.name, 'size:', file.size)

  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }
    
    img.onload = () => {
      try {
        // Set canvas size to match image
        canvas.width = img.width
        canvas.height = img.height
        
        // Draw original image
        ctx.drawImage(img, 0, 0)
        
        // Add watermark
        addWatermarkToCanvas(ctx, canvas.width, canvas.height, opts)
        
        // Convert canvas to blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to create watermarked image'))
              return
            }
            
            // Create new file with watermark
            const watermarkedFile = new File(
              [blob],
              file.name,
              {
                type: file.type,
                lastModified: Date.now()
              }
            )

            console.log('Watermark added successfully to:', file.name)
            resolve(watermarkedFile)
          },
          file.type,
          opts.quality
        )
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }
    
    // Load image from file
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target?.result) {
        img.src = e.target.result as string
      }
    }
    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }
    reader.readAsDataURL(file)
  })
}

/**
 * Add watermark to canvas context
 */
function addWatermarkToCanvas(
  ctx: CanvasRenderingContext2D,
  width: number,
  height: number,
  options: Required<WatermarkOptions>
) {
  // Calculate responsive font size based on image dimensions
  const responsiveFontSize = Math.max(
    Math.min(width, height) * 0.03, // 3% of smaller dimension
    12 // Minimum font size
  )
  
  const fontSize = Math.min(responsiveFontSize, options.fontSize * 2) // Cap at 2x requested size
  
  // Set text properties
  ctx.font = `${fontSize}px ${options.fontFamily}`
  ctx.fillStyle = options.color
  ctx.globalAlpha = options.opacity
  ctx.textAlign = 'left'
  ctx.textBaseline = 'bottom'
  
  // Add text shadow for better visibility
  ctx.shadowColor = 'rgba(0, 0, 0, 0.8)'
  ctx.shadowOffsetX = 1
  ctx.shadowOffsetY = 1
  ctx.shadowBlur = 2
  
  // Measure text
  const textMetrics = ctx.measureText(options.text)
  const textWidth = textMetrics.width
  const textHeight = fontSize
  
  // Calculate position
  let x: number, y: number
  
  switch (options.position) {
    case 'bottom-right':
      x = width - textWidth - options.margin
      y = height - options.margin
      break
    case 'bottom-left':
      x = options.margin
      y = height - options.margin
      break
    case 'top-right':
      x = width - textWidth - options.margin
      y = textHeight + options.margin
      break
    case 'top-left':
      x = options.margin
      y = textHeight + options.margin
      break
    case 'center':
      x = (width - textWidth) / 2
      y = (height + textHeight) / 2
      break
    default:
      x = width - textWidth - options.margin
      y = height - options.margin
  }
  
  // Draw watermark text
  ctx.fillText(options.text, x, y)
  
  // Reset shadow and alpha
  ctx.shadowColor = 'transparent'
  ctx.shadowOffsetX = 0
  ctx.shadowOffsetY = 0
  ctx.shadowBlur = 0
  ctx.globalAlpha = 1
}

/**
 * Batch watermark multiple files
 */
export async function addWatermarkToFiles(
  files: File[],
  options: WatermarkOptions = {}
): Promise<File[]> {
  const watermarkPromises = files.map(file => addWatermarkToFile(file, options))
  return Promise.all(watermarkPromises)
}

/**
 * Check if file is a supported image type
 */
export function isSupportedImageType(file: File): boolean {
  const supportedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp'
  ]
  return supportedTypes.includes(file.type.toLowerCase())
}

/**
 * Validate image file for watermarking
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  if (!isSupportedImageType(file)) {
    return {
      valid: false,
      error: `Unsupported image type: ${file.type}. Supported types: JPEG, PNG, WebP`
    }
  }
  
  // Check file size (max 50MB for processing)
  const maxSize = 50 * 1024 * 1024
  if (file.size > maxSize) {
    return {
      valid: false,
      error: `Image too large for watermarking: ${(file.size / 1024 / 1024).toFixed(1)}MB. Max: 50MB`
    }
  }
  
  return { valid: true }
}
