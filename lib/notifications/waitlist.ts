// Waitlist notification system
import { createSupabaseClient } from '@/lib/supabase/client'

interface WaitlistNotification {
  projectId: string
  projectTitle: string
  writerName: string
  projectUrl: string
}

// Get all waitlist entries that need notification
export async function getWaitlistToNotify(projectId: string) {
  const supabase = createSupabaseClient()
  
  const { data: waitlistEntries, error } = await supabase
    .from('project_waitlist')
    .select(`
      id,
      user_email,
      user_name,
      user_id,
      projects!inner (
        title,
        users!inner (
          name
        )
      )
    `)
    .eq('project_id', projectId)
    .eq('notification_sent', false)

  if (error) {
    console.error('Error fetching waitlist:', error)
    return []
  }

  return waitlistEntries || []
}

// Mark waitlist entries as notified
export async function markWaitlistNotified(waitlistIds: string[]) {
  const supabase = createSupabaseClient()
  
  const { error } = await supabase
    .from('project_waitlist')
    .update({ notification_sent: true })
    .in('id', waitlistIds)

  if (error) {
    console.error('Error marking waitlist as notified:', error)
    return false
  }

  return true
}

// Send email notification (placeholder - integrate with your email service)
export async function sendWaitlistEmail(
  email: string, 
  name: string | null, 
  notification: WaitlistNotification
) {
  // For now, just log the notification
  // In production, integrate with services like:
  // - Resend
  // - SendGrid
  // - Mailgun
  // - AWS SES
  
  console.log('📧 Waitlist notification email:', {
    to: email,
    name: name || 'Reader',
    subject: `${notification.projectTitle} is now available!`,
    message: `
      Hi ${name || 'there'}!
      
      Great news! The project "${notification.projectTitle}" by ${notification.writerName} 
      that you waitlisted is now available to read.
      
      Check it out here: ${notification.projectUrl}
      
      Happy reading!
      - The OnlyDiary Team
    `
  })

  // Return true for now - in production, return actual email send result
  return true
}

// Main function to notify waitlist when project goes public
export async function notifyProjectWaitlist(projectId: string) {
  try {
    const waitlistEntries = await getWaitlistToNotify(projectId)
    
    if (waitlistEntries.length === 0) {
      console.log('No waitlist entries to notify for project:', projectId)
      return { success: true, notified: 0 }
    }

    const projectUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://onlydiary.app'}/projects/${projectId}`
    
    let successCount = 0
    const notifiedIds: string[] = []

    for (const entry of waitlistEntries) {
      const notification: WaitlistNotification = {
        projectId,
        projectTitle: entry.projects.title,
        writerName: entry.projects.users.name,
        projectUrl
      }

      const emailSent = await sendWaitlistEmail(
        entry.user_email,
        entry.user_name,
        notification
      )

      if (emailSent) {
        successCount++
        notifiedIds.push(entry.id)
      }
    }

    // Mark successful notifications as sent
    if (notifiedIds.length > 0) {
      await markWaitlistNotified(notifiedIds)
    }

    console.log(`✅ Notified ${successCount}/${waitlistEntries.length} waitlist subscribers for project ${projectId}`)
    
    return {
      success: true,
      notified: successCount,
      total: waitlistEntries.length
    }

  } catch (error) {
    console.error('Error notifying waitlist:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Function for writers to manually trigger waitlist notifications
export async function triggerWaitlistNotification(projectId: string, userId: string) {
  const supabase = createSupabaseClient()
  
  // Verify the user owns this project
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select('id, title, user_id')
    .eq('id', projectId)
    .eq('user_id', userId)
    .single()

  if (projectError || !project) {
    throw new Error('Project not found or you do not have permission')
  }

  return await notifyProjectWaitlist(projectId)
}

// Get waitlist count for a project (for writers to see interest)
export async function getWaitlistCount(projectId: string) {
  const supabase = createSupabaseClient()
  
  const { count, error } = await supabase
    .from('project_waitlist')
    .select('*', { count: 'exact', head: true })
    .eq('project_id', projectId)

  if (error) {
    console.error('Error getting waitlist count:', error)
    return 0
  }

  return count || 0
}
