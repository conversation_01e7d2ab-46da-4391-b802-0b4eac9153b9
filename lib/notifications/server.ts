// Server-side push notification utilities
import webpush from 'web-push'
import { createSupabaseClient } from '@/lib/supabase/client'

// Configure web-push with VAPID keys
webpush.setVapidDetails(
  process.env.NEXT_PUBLIC_VAPID_EMAIL!,
  process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!
)

export interface NotificationPayload {
  title: string
  body: string
  data?: Record<string, unknown>
  tag?: string
  requireInteraction?: boolean
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
}

// Send push notification to a specific user
export async function sendPushNotificationToUser(
  userId: string, 
  payload: NotificationPayload
): Promise<{ success: boolean; sent: number; failed: number }> {
  const supabase = createSupabaseClient()
  
  try {
    // Get all push subscriptions for this user
    const { data: subscriptions, error } = await supabase
      .from('push_subscriptions')
      .select('id, subscription')
      .eq('user_id', userId)
    
    if (error || !subscriptions || subscriptions.length === 0) {
      console.log(`No push subscriptions found for user ${userId}`)
      return { success: true, sent: 0, failed: 0 }
    }
    
    let sent = 0
    let failed = 0
    
    // Send to each subscription
    for (const sub of subscriptions) {
      try {
        await webpush.sendNotification(
          sub.subscription,
          JSON.stringify(payload)
        )
        sent++
        console.log(`✅ Push notification sent to subscription ${sub.id}`)
      } catch (error) {
        failed++
        console.error(`❌ Failed to send push notification to subscription ${sub.id}:`, error)
        
        // If subscription is invalid, remove it
        if (error instanceof Error && (
          error.message.includes('410') || 
          error.message.includes('invalid') ||
          error.message.includes('expired')
        )) {
          await supabase
            .from('push_subscriptions')
            .delete()
            .eq('id', sub.id)
          console.log(`🗑️ Removed invalid subscription ${sub.id}`)
        }
      }
    }
    
    return { success: true, sent, failed }
    
  } catch (error) {
    console.error('Error sending push notifications:', error)
    return { success: false, sent: 0, failed: 0 }
  }
}

// Process pending notifications and send push notifications
export async function processPendingNotifications(): Promise<void> {
  const supabase = createSupabaseClient()
  
  try {
    // Get notifications from the last 5 minutes that haven't been processed
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    
    const { data: notifications, error } = await supabase
      .from('notifications')
      .select('*')
      .gte('sent_at', fiveMinutesAgo.toISOString())
      .is('clicked_at', null) // Not yet processed
      .limit(100) // Process in batches
    
    if (error || !notifications || notifications.length === 0) {
      return
    }
    
    console.log(`Processing ${notifications.length} pending notifications`)
    
    for (const notification of notifications) {
      const payload: NotificationPayload = {
        title: notification.title,
        body: notification.body,
        data: {
          ...notification.data,
          notificationId: notification.id
        },
        tag: `${notification.type}-${notification.id}`,
        requireInteraction: true,
        actions: [
          {
            action: 'view',
            title: 'View',
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      }
      
      const result = await sendPushNotificationToUser(notification.user_id, payload)
      
      if (result.success && result.sent > 0) {
        // Mark as processed by setting clicked_at (we use this as "processed" flag)
        await supabase
          .from('notifications')
          .update({ clicked_at: new Date().toISOString() })
          .eq('id', notification.id)
      }
    }
    
  } catch (error) {
    console.error('Error processing pending notifications:', error)
  }
}

// Send comment notification
export async function sendCommentNotification(
  recipientUserId: string,
  commenterName: string,
  diaryTitle: string,
  diaryEntryId: string,
  isReply: boolean = false
): Promise<boolean> {
  const payload: NotificationPayload = {
    title: isReply ? 'New reply to your comment' : 'New comment on your diary',
    body: isReply 
      ? `${commenterName} replied to your comment`
      : `${commenterName} commented on "${diaryTitle}"`,
    data: {
      type: isReply ? 'reply' : 'comment',
      diaryEntryId,
      url: `/d/${diaryEntryId}`
    },
    tag: `comment-${diaryEntryId}`,
    requireInteraction: true
  }
  
  const result = await sendPushNotificationToUser(recipientUserId, payload)
  return result.success && result.sent > 0
}

// API endpoint helper to process notifications
export async function handleNotificationProcessing(): Promise<Response> {
  try {
    await processPendingNotifications()
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Error in notification processing:', error)
    return new Response(JSON.stringify({ error: 'Failed to process notifications' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
