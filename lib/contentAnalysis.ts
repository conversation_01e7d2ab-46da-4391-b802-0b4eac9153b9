// Simple content analysis for post recommendations
export function extractKeywords(text: string): string[] {
  // Common words to ignore
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall',
    'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
    'my', 'your', 'his', 'her', 'its', 'our', 'their', 'this', 'that', 'these', 'those',
    'what', 'when', 'where', 'why', 'how', 'who', 'which', 'all', 'any', 'some', 'no', 'not',
    'just', 'now', 'then', 'here', 'there', 'up', 'down', 'out', 'off', 'over', 'under',
    'again', 'further', 'once', 'more', 'most', 'other', 'such', 'only', 'own', 'same',
    'so', 'than', 'too', 'very', 'can', 'will', 'just', 'should', 'now'
  ])

  // Extract meaningful words (3+ characters, not stop words)
  const words = text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ') // Remove punctuation
    .split(/\s+/)
    .filter(word => 
      word.length >= 3 && 
      !stopWords.has(word) &&
      !/^\d+$/.test(word) // Remove pure numbers
    )

  // Count word frequency
  const wordCount: { [key: string]: number } = {}
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1
  })

  // Return top keywords (words that appear more than once or are longer)
  return Object.entries(wordCount)
    .filter(([word, count]) => count > 1 || word.length > 6)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10) // Top 10 keywords
    .map(([word]) => word)
}

export function calculateSimilarity(keywords1: string[], keywords2: string[]): number {
  if (keywords1.length === 0 || keywords2.length === 0) return 0
  
  const set1 = new Set(keywords1)
  const set2 = new Set(keywords2)
  
  // Calculate Jaccard similarity (intersection / union)
  const intersection = new Set([...set1].filter(x => set2.has(x)))
  const union = new Set([...set1, ...set2])
  
  return intersection.size / union.size
}

export function getTopSimilarPosts(
  userLikedPosts: { id: string; keywords: string[] }[],
  candidatePosts: { id: string; keywords: string[] }[],
  limit: number = 10
): string[] {
  // Calculate similarity scores for each candidate post
  const scores = candidatePosts.map(candidate => {
    let maxSimilarity = 0
    
    // Find highest similarity with any liked post
    userLikedPosts.forEach(likedPost => {
      const similarity = calculateSimilarity(likedPost.keywords, candidate.keywords)
      maxSimilarity = Math.max(maxSimilarity, similarity)
    })
    
    return {
      postId: candidate.id,
      similarity: maxSimilarity
    }
  })
  
  // Return top similar posts
  return scores
    .filter(score => score.similarity > 0.1) // Minimum similarity threshold
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, limit)
    .map(score => score.postId)
}
