import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Utility to check if we're in development mode
export const isDev = process.env.NODE_ENV === 'development'

// Utility for performance monitoring
export function measurePerformance<T>(
  name: string,
  fn: () => T
): T {
  if (!isDev) return fn()

  const start = performance.now()
  const result = fn()
  const end = performance.now()

  console.log(`⚡ ${name}: ${(end - start).toFixed(2)}ms`)
  return result
}

// Utility to prevent layout shift during loading states
export function preserveLayoutDimensions(element: HTMLElement | null) {
  if (!element) return

  const { width, height } = element.getBoundingClientRect()
  element.style.minWidth = `${width}px`
  element.style.minHeight = `${height}px`
}