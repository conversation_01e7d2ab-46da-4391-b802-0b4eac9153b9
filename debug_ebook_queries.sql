-- Debug queries for ebook/chapter issues

-- 1. Check if the book exists and its details
SELECT id, title, is_ebook, user_id, price_amount, created_at 
FROM projects 
WHERE id = '62898999-f28b-4112-8954-9d7b516638c2';

-- 2. Check if chapters table exists and its structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'chapters' 
ORDER BY ordinal_position;

-- 3. Check if there are any chapters for this book
SELECT id, title, chapter_number, is_published, word_count, created_at
FROM chapters 
WHERE project_id = '62898999-f28b-4112-8954-9d7b516638c2'
ORDER BY chapter_number;

-- 4. Check all chapters in the database (to see if table has any data)
SELECT project_id, title, chapter_number, is_published, created_at
FROM chapters 
ORDER BY created_at DESC 
LIMIT 10;

-- 5. Check if the book is in user's library
SELECT ul.*, p.title as book_title
FROM user_library ul
JOIN projects p ON ul.project_id = p.id
WHERE ul.user_id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e'
AND ul.project_id = '62898999-f28b-4112-8954-9d7b516638c2';

-- 6. Check if there are any book purchases for this book
SELECT bp.*, p.title as book_title
FROM book_purchases bp
JOIN projects p ON bp.project_id = p.id
WHERE bp.project_id = '62898999-f28b-4112-8954-9d7b516638c2';

-- 7. Check all ebooks in the system
SELECT id, title, is_ebook, price_amount, user_id, created_at
FROM projects 
WHERE is_ebook = true
ORDER BY created_at DESC;

-- 8. Check if there are any processed chapters (from ebook processing)
SELECT p.title, c.title as chapter_title, c.chapter_number, c.is_published
FROM projects p
LEFT JOIN chapters c ON p.id = c.project_id
WHERE p.is_ebook = true
ORDER BY p.created_at DESC, c.chapter_number;
