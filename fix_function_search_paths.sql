-- Fix search_path for all functions to prevent search_path injection attacks

-- Set search_path for bookmark functions
ALTER FUNCTION update_bookmark_count() SET search_path = '';
ALTER FUNCTION update_flower_count() SET search_path = '';

-- Set search_path for follower functions  
ALTER FUNCTION is_following(UUID) SET search_path = '';
ALTER FUNCTION get_follower_count(UUID) SET search_path = '';

-- Set search_path for comment functions
ALTER FUNCTION user_can_comment_on_entry(UUID, UUID) SET search_path = '';
ALTER FUNCTION notify_comment_created() SET search_path = '';

-- Set search_path for subscription functions
ALTER FUNCTION update_subscriber_count() SET search_path = '';
ALTER FUNCTION user_has_active_subscription(UUID, UUID) SET search_path = '';

-- Set search_path for entry functions
ALTER FUNCTION update_entry_count() SET search_path = '';
ALTER FUNCTION get_entry_preview(UUID, UUID) SET search_path = '';

-- Set search_path for utility functions
ALTER FUNCTION update_updated_at_column() SET search_path = '';
ALTER FUNCTION check_photo_limit() SET search_path = '';

-- Set search_path for writer functions
ALTER FUNCTION get_writer_public_data(UUID) SET search_path = '';
ALTER FUNCTION get_writer_locked_entries(UUID, UUID) SET search_path = '';

-- Set search_path for credit functions
ALTER FUNCTION update_post_credits_updated_at() SET search_path = '';
ALTER FUNCTION user_can_read_post(UUID, UUID) SET search_path = '';
ALTER FUNCTION consume_credit_for_post(UUID, UUID) SET search_path = '';

-- Set search_path for love functions
ALTER FUNCTION update_love_count() SET search_path = '';
ALTER FUNCTION update_hourly_love_stats() SET search_path = '';
ALTER FUNCTION get_top_diary_entries_hourly() SET search_path = '';