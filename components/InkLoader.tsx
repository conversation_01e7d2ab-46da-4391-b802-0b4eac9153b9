"use client"

import { motion } from 'framer-motion'

interface InkLoaderProps {
  lines?: number
  delay?: number
  className?: string
}

export default function InkLoader({ lines = 3, delay = 0, className = "" }: InkLoaderProps) {
  return (
    <span className={`inline-block px-1 ${className}`}>
      {Array.from({ length: lines }).map((_, i) => (
        <motion.span
          key={i}
          className="block h-[2px] w-full bg-gray-900 mb-[2px] last:mb-0"
          initial={{ scaleX: 1 }}
          animate={{ scaleX: 0 }}
          style={{ transformOrigin: "left" }}
          transition={{ 
            delay: delay + i * 0.12, 
            duration: 0.3, 
            ease: 'easeInOut',
            repeat: Infinity,
            repeatType: 'reverse',
            repeatDelay: 0.5
          }}
        />
      ))}
    </span>
  )
}
