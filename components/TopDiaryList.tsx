"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Link from "next/link"

interface TopDiaryEntry {
  entry_id: string
  title: string
  writer_name: string
  writer_id: string
  writer_avatar: string | null
  hourly_loves: number
  total_loves: number
  first_photo_url: string | null
  created_at: string
  is_free: boolean
}

export function TopDiaryList() {
  const [entries, setEntries] = useState<TopDiaryEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const supabase = createSupabaseClient()

  const loadTopEntries = async () => {
    try {
      const { data, error } = await supabase.rpc('get_top_diary_entries_hourly', {
        limit_count: 10
      })

      if (error) {
        console.error('Error loading top entries:', error)
        setError("Failed to load top diary entries")
      } else {
        setEntries(data || [])
        setLastUpdated(new Date())
      }
    } catch (err) {
      console.error('Error:', err)
      setError("Failed to load top diary entries")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadTopEntries()

    // Refresh every hour
    const interval = setInterval(loadTopEntries, 60 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return "Just now"
    if (diffInHours === 1) return "1 hour ago"
    if (diffInHours < 24) return `${diffInHours} hours ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays === 1) return "1 day ago"
    return `${diffInDays} days ago`
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">🔥 Trending Now</h2>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex gap-4">
                <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">🔥 Trending Now</h2>
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-gray-900">🔥 Trending Now</h2>
        {lastUpdated && (
          <span className="text-xs text-gray-500">
            Updated {lastUpdated.toLocaleTimeString()}
          </span>
        )}
      </div>

      {entries.length === 0 ? (
        <p className="text-gray-500 text-sm">No trending entries right now</p>
      ) : (
        <div className="space-y-4">
          {entries.map((entry, index) => (
            <div key={entry.entry_id} className="group">
              <Link href={`/d/${entry.entry_id}`} className="block">
                <div className="flex gap-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  {/* Ranking Number */}
                  <div className="flex-shrink-0 w-6 text-center">
                    <span className={`text-sm font-bold ${
                      index === 0 ? 'text-yellow-600' :
                      index === 1 ? 'text-gray-500' :
                      index === 2 ? 'text-orange-600' :
                      'text-gray-400'
                    }`}>
                      #{index + 1}
                    </span>
                  </div>

                  {/* Photo or Placeholder */}
                  <div className="flex-shrink-0">
                    {entry.first_photo_url ? (
                      <img
                        src={entry.first_photo_url}
                        alt={entry.title}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                        <span className="text-gray-400 text-xs">📝</span>
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 group-hover:text-gray-700 line-clamp-2 text-sm">
                      {entry.title}
                    </h3>
                    
                    <div className="flex items-center gap-2 mt-1">
                      {/* Writer Avatar */}
                      <div className="w-4 h-4 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                        {entry.writer_avatar ? (
                          <img
                            src={entry.writer_avatar}
                            alt={entry.writer_name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-300"></div>
                        )}
                      </div>
                      
                      <span className="text-xs text-gray-600 truncate">
                        {entry.writer_name}
                      </span>
                      
                      {entry.is_free && (
                        <span className="text-xs bg-green-100 text-green-700 px-1 py-0.5 rounded">
                          FREE
                        </span>
                      )}
                    </div>

                    <div className="flex items-center gap-3 mt-1 text-xs text-gray-500">
                      <span>❤️ {entry.hourly_loves} this hour</span>
                      <span>•</span>
                      <span>{entry.total_loves} total</span>
                      <span>•</span>
                      <span>{formatTimeAgo(entry.created_at)}</span>
                    </div>
                  </div>

                  {/* Subscribe Button */}
                  <div className="flex-shrink-0">
                    <Link
                      href={`/u/${entry.writer_id}`}
                      onClick={(e) => e.stopPropagation()}
                      className="text-xs bg-gray-800 text-white px-3 py-1 rounded-full hover:bg-gray-700 transition-colors"
                    >
                      Subscribe
                    </Link>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      )}

      <div className="mt-4 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-500 text-center">
          Rankings update every hour based on recent activity
        </p>
      </div>
    </div>
  )
}
