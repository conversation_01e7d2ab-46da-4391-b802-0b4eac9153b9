"use client"

import { useEffect, useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Link from "next/link"

interface StoryMapProps {
  currentEntryId: string
  writerId: string
  writerName: string
}

interface StoryEntry {
  id: string
  title: string
  created_at: string
  is_free: boolean
  is_pinned?: boolean
}

export function StoryMap({ currentEntryId, writerId, writerName }: StoryMapProps) {
  const [entries, setEntries] = useState<StoryEntry[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    loadStoryEntries()
  }, [currentEntryId, writerId])

  const loadStoryEntries = async () => {
    try {
      const { data } = await supabase
        .from('diary_entries')
        .select('id, title, created_at, is_free')
        .eq('writer_id', writerId)
        .eq('is_hidden', false)
        .order('created_at', { ascending: false })
        .limit(20)

      setEntries(data || [])
    } catch (error) {
      console.error('Error loading story entries:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 animate-pulse">
        <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  if (entries.length <= 1) {
    return null // Don't show if only one entry
  }

  const currentIndex = entries.findIndex(entry => entry.id === currentEntryId)

  return (
    <div className="bg-white rounded-2xl p-4 sm:p-6 shadow-lg border border-gray-100 mt-8">
      <h3 className="text-lg sm:text-xl font-serif text-gray-800 mb-4 sm:mb-6 flex items-center gap-2">
        <span>🗺️</span>
        <span className="hidden sm:inline">Explore {writerName}'s Story Journey</span>
        <span className="sm:hidden">{writerName}'s Stories</span>
      </h3>
      
      {/* Mobile Timeline View */}
      <div className="block sm:hidden space-y-3">
        {entries.slice(0, 6).map((entry, index) => (
          <Link
            key={entry.id}
            href={`/d/${entry.id}`}
            className={`group flex items-center gap-3 p-3 rounded-lg transition-colors border ${
              entry.id === currentEntryId
                ? 'bg-purple-50 border-purple-200'
                : 'hover:bg-gray-50 border-gray-100'
            }`}
          >
            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold text-white ${
              entry.id === currentEntryId
                ? 'bg-purple-600'
                : 'bg-gradient-to-br from-purple-500 to-blue-500'
            }`}>
              {entries.length - index}
            </div>
            <div className="min-w-0 flex-1">
              <h4 className={`text-sm font-medium truncate ${
                entry.id === currentEntryId
                  ? 'text-purple-700'
                  : 'text-gray-800 group-hover:text-purple-600'
              }`}>
                {entry.title}
              </h4>
              <div className="flex items-center gap-2 mt-1">
                <p className="text-xs text-gray-500">
                  {new Date(entry.created_at).toLocaleDateString()}
                </p>
                {entry.is_free && (
                  <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">FREE</span>
                )}
                {entry.id === currentEntryId && (
                  <span className="text-xs bg-purple-100 text-purple-700 px-1.5 py-0.5 rounded">READING</span>
                )}
              </div>
            </div>
            {entry.id !== currentEntryId && (
              <svg className="w-4 h-4 text-gray-400 group-hover:text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            )}
          </Link>
        ))}
      </div>

      {/* Desktop Grid View */}
      <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
        {entries.slice(0, 12).map((entry, index) => (
          <Link
            key={entry.id}
            href={`/d/${entry.id}`}
            className={`group flex items-center gap-3 p-3 rounded-lg transition-colors border ${
              entry.id === currentEntryId
                ? 'bg-purple-50 border-purple-200'
                : 'hover:bg-gray-50 border-gray-100'
            }`}
          >
            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold text-white ${
              entry.id === currentEntryId
                ? 'bg-purple-600'
                : 'bg-gradient-to-br from-purple-500 to-blue-500'
            }`}>
              {entries.length - index}
            </div>
            <div className="min-w-0 flex-1">
              <h4 className={`text-sm font-medium truncate ${
                entry.id === currentEntryId
                  ? 'text-purple-700'
                  : 'text-gray-800 group-hover:text-purple-600'
              }`}>
                {entry.title}
              </h4>
              <p className="text-xs text-gray-500">
                {new Date(entry.created_at).toLocaleDateString()}
              </p>
            </div>
            {entry.is_free && (
              <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">FREE</span>
            )}
            {entry.id === currentEntryId && (
              <span className="text-xs bg-purple-100 text-purple-700 px-1.5 py-0.5 rounded">•</span>
            )}
          </Link>
        ))}
      </div>
      
      {entries.length > 12 && (
        <div className="mt-4 text-center">
          <Link
            href={`/u/${writerId}`}
            className="text-sm text-purple-600 hover:text-purple-700 font-medium px-4 py-2 rounded-lg hover:bg-purple-50 transition-colors"
          >
            View All {entries.length} Stories →
          </Link>
        </div>
      )}
    </div>
  )
}
