"use client"

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface InvitePromptProps {
  onClose?: () => void
  variant?: 'modal' | 'card' // modal for after publish, card for dashboard
  userName?: string
}

export function InvitePrompt({ onClose, variant = 'modal', userName }: InvitePromptProps) {
  const [loading, setLoading] = useState(false)
  const supabase = createSupabaseClient()

  const generateInviteLink = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return null

      // Create unique invite code
      const inviteCode = `${userName?.replace(/\s+/g, '') || 'friend'}${Date.now()}`
      const inviteLink = `https://onlydiary.app/invite/${inviteCode}`

      // Track the invite creation
      await supabase.from('invites').insert({
        inviter_id: user.id,
        invite_code: inviteCode,
        method: 'sms'
      })

      return { inviteLink, inviteCode }
    } catch (error) {
      console.error('Error generating invite:', error)
      return null
    }
  }

  const handleSMSInvite = async () => {
    setLoading(true)
    try {
      const invite = await generateInviteLink()
      if (!invite) {
        alert('Failed to generate invite link')
        return
      }

      const message = `Hey! I'm sharing my personal stories on OnlyDiary - where life becomes literature. Join me: ${invite.inviteLink}`
      
      // Open SMS with pre-filled message
      window.open(`sms:?body=${encodeURIComponent(message)}`)
      
      onClose?.()
    } catch (error) {
      console.error('Error sending SMS invite:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCopyLink = async () => {
    setLoading(true)
    try {
      const invite = await generateInviteLink()
      if (!invite) {
        alert('Failed to generate invite link')
        return
      }

      const message = `Hey! I'm sharing my personal stories on OnlyDiary - where life becomes literature. Join me: ${invite.inviteLink}`
      
      await navigator.clipboard.writeText(message)
      alert('Invite message copied to clipboard!')
      
      onClose?.()
    } catch (error) {
      console.error('Error copying invite:', error)
      alert('Failed to copy invite link')
    } finally {
      setLoading(false)
    }
  }

  if (variant === 'modal') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-2xl p-6 max-w-md w-full">
          <div className="text-center mb-6">
            <h3 className="text-xl font-serif text-gray-800 mb-2">
              🎉 Story Published!
            </h3>
            <p className="text-gray-600">
              Want to invite friends to read your stories on OnlyDiary?
            </p>
          </div>

          <div className="space-y-3">
            <button
              onClick={handleSMSInvite}
              disabled={loading}
              className="w-full bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              {loading ? 'Creating invite...' : 'Invite via SMS'}
            </button>

            <button
              onClick={handleCopyLink}
              disabled={loading}
              className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              Copy Invite Link
            </button>

            <button
              onClick={onClose}
              className="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200"
            >
              Maybe Later
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Card variant for dashboard
  return (
    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-start gap-3">
        <div className="bg-blue-100 rounded-full p-2 flex-shrink-0">
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
          </svg>
        </div>
        <div className="flex-1">
          <h4 className="font-medium text-gray-900 mb-1">
            OnlyDiary is better with friends
          </h4>
          <p className="text-sm text-gray-600 mb-3">
            Invite friends to read your stories and discover theirs
          </p>
          <div className="flex gap-2">
            <button
              onClick={handleSMSInvite}
              disabled={loading}
              className="bg-blue-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Invite Friends'}
            </button>
            <button
              onClick={handleCopyLink}
              disabled={loading}
              className="bg-white text-gray-700 px-3 py-1.5 rounded text-sm font-medium hover:bg-gray-50 border border-gray-200 disabled:opacity-50"
            >
              Copy Link
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
