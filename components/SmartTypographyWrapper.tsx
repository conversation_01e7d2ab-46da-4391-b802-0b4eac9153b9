'use client'

import { SmartTypography } from './SmartTypography'
import { useDeviceCapabilities } from '@/hooks/useDeviceDetection'

interface SmartTypographyWrapperProps {
  content: string
  className?: string
  isPreview?: boolean
  zenMode?: boolean
}

export function SmartTypographyWrapper({ 
  content, 
  className = '', 
  isPreview = false, 
  zenMode = false 
}: SmartTypographyWrapperProps) {
  const deviceCapabilities = useDeviceCapabilities()

  return (
    <SmartTypography
      content={content}
      className={className}
      isPreview={isPreview}
      zenMode={zenMode}
      isDesktop={deviceCapabilities.isDesktop}
      enableAdvancedFeatures={deviceCapabilities.shouldUseAdvancedAnimations}
    />
  )
}
