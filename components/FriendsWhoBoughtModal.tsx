'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Image from 'next/image'
import Link from 'next/link'

interface Friend {
  id: string
  name: string
  avatar_url: string | null
  username: string
  purchased_at: string
  relationship_type: 'following' | 'follower' | 'mutual'
}

interface FriendsWhoBoughtModalProps {
  isOpen: boolean
  onClose: () => void
  bookId: string
  currentUserId: string
}

export function FriendsWhoBoughtModal({ 
  isOpen, 
  onClose, 
  bookId, 
  currentUserId 
}: FriendsWhoBoughtModalProps) {
  const [friends, setFriends] = useState<Friend[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (isOpen && bookId && currentUserId) {
      fetchFriendsWhoBought()
    }
  }, [isOpen, bookId, currentUserId])

  const fetchFriendsWhoBought = async () => {
    try {
      const supabase = createSupabaseClient()
      
      // Get users who follow the current user AND bought this book
      const { data: followers, error: followersError } = await supabase
        .from('subscriptions')
        .select(`
          reader_id,
          users!subscriptions_reader_id_fkey(
            id,
            name,
            avatar_url,
            username
          )
        `)
        .eq('writer_id', currentUserId)
        .eq('active_until', true) // Active subscriptions only

      // Get users the current user follows AND who bought this book
      const { data: following, error: followingError } = await supabase
        .from('subscriptions')
        .select(`
          writer_id,
          users!subscriptions_writer_id_fkey(
            id,
            name,
            avatar_url,
            username
          )
        `)
        .eq('reader_id', currentUserId)
        .eq('active_until', true) // Active subscriptions only

      if (followersError || followingError) {
        console.error('Error fetching relationships:', followersError || followingError)
        return
      }

      // Get all user IDs from both relationships
      const followerIds = followers?.map(f => f.reader_id) || []
      const followingIds = following?.map(f => f.writer_id) || []
      const allRelatedUserIds = [...new Set([...followerIds, ...followingIds])]

      if (allRelatedUserIds.length === 0) {
        setFriends([])
        setLoading(false)
        return
      }

      // Now get which of these users have purchased this book
      const { data: purchases, error: purchasesError } = await supabase
        .from('book_purchases')
        .select(`
          user_id,
          purchased_at,
          users(
            id,
            name,
            avatar_url,
            username
          )
        `)
        .eq('project_id', bookId)
        .in('user_id', allRelatedUserIds)
        .order('purchased_at', { ascending: false })

      if (purchasesError) {
        console.error('Error fetching purchases:', purchasesError)
        return
      }

      // Map the results with relationship types
      const friendsData: Friend[] = purchases?.map(purchase => {
        const isFollower = followerIds.includes(purchase.user_id)
        const isFollowing = followingIds.includes(purchase.user_id)
        
        let relationshipType: 'following' | 'follower' | 'mutual' = 'follower'
        if (isFollower && isFollowing) {
          relationshipType = 'mutual'
        } else if (isFollowing) {
          relationshipType = 'following'
        }

        return {
          id: purchase.user_id,
          name: purchase.users?.name || 'Unknown User',
          avatar_url: purchase.users?.avatar_url || null,
          username: purchase.users?.username || '',
          purchased_at: purchase.purchased_at,
          relationship_type: relationshipType
        }
      }) || []

      setFriends(friendsData)
    } catch (error) {
      console.error('Error in fetchFriendsWhoBought:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getRelationshipIcon = (type: string) => {
    switch (type) {
      case 'mutual': return '🤝'
      case 'following': return '👤'
      case 'follower': return '👥'
      default: return '👤'
    }
  }

  const getRelationshipText = (type: string) => {
    switch (type) {
      case 'mutual': return 'Mutual follow'
      case 'following': return 'You follow'
      case 'follower': return 'Follows you'
      default: return 'Connected'
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            👥 Friends Who Bought This Book
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-96">
          {loading ? (
            <div className="p-6">
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-center space-x-3 animate-pulse">
                    <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : friends.length === 0 ? (
            <div className="p-6 text-center">
              <div className="text-gray-400 text-4xl mb-3">📚</div>
              <p className="text-gray-600 mb-2">No friends have bought this book yet</p>
              <p className="text-sm text-gray-500">
                When people you follow purchase this book, they'll appear here
              </p>
            </div>
          ) : (
            <div className="p-6">
              <div className="space-y-4">
                {friends.map((friend) => (
                  <Link
                    key={friend.id}
                    href={`/${friend.username}`}
                    className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                    onClick={onClose}
                  >
                    {/* Avatar */}
                    <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gray-200">
                      {friend.avatar_url ? (
                        <Image
                          src={friend.avatar_url}
                          alt={friend.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-500 text-sm">
                          {friend.name.charAt(0).toUpperCase()}
                        </div>
                      )}
                    </div>

                    {/* User Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-gray-900 truncate">
                          {friend.name}
                        </p>
                        <span className="text-xs" title={getRelationshipText(friend.relationship_type)}>
                          {getRelationshipIcon(friend.relationship_type)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500">
                        Purchased {formatDate(friend.purchased_at)}
                      </p>
                    </div>

                    {/* Arrow */}
                    <div className="text-gray-400">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {friends.length > 0 && (
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <p className="text-xs text-gray-500 text-center">
              Showing {friends.length} friend{friends.length !== 1 ? 's' : ''} who purchased this book
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
