'use client'

import { useState } from 'react'
import { CreatorsList } from './CreatorsList'
import { PaymentActivity } from './PaymentActivity'
import { AdminStats } from './AdminStats'

interface Creator {
  id: string
  name: string
  email: string
  role: string
  stripe_account_id: string | null
  stripe_onboarding_complete: boolean
  price_monthly: number | null
  created_at: string
  updated_at: string
}

interface SubscriptionStat {
  writer_id: string
  status: string
  created_at: string
}

interface PaymentActivity {
  id: string
  writer_id: string
  amount: number
  status: string
  payment_type: string
  created_at: string
  users: { name: string } | null
}

interface AdminDashboardProps {
  creators: Creator[]
  subscriptionStats: SubscriptionStat[]
  recentActivity: PaymentActivity[]
}

export function AdminDashboard({ creators, subscriptionStats, recentActivity }: AdminDashboardProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'creators' | 'payments' | 'disputes'>('overview')

  // Calculate stats
  const totalCreators = creators.length
  const activeCreators = creators.filter(c => c.stripe_onboarding_complete).length
  const pendingSetup = creators.filter(c => c.stripe_account_id && !c.stripe_onboarding_complete).length
  const totalRevenue = recentActivity
    .filter(p => p.status === 'completed')
    .reduce((sum, p) => sum + p.amount, 0)

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'creators', label: 'Creators', icon: '👥' },
    { id: 'payments', label: 'Payments', icon: '💳' },
    { id: 'disputes', label: 'Disputes', icon: '⚠️' }
  ]

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <AdminStats 
        totalCreators={totalCreators}
        activeCreators={activeCreators}
        pendingSetup={pendingSetup}
        totalRevenue={totalRevenue}
      />

      {/* Navigation Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="flex items-center gap-2">
                  <span>{tab.icon}</span>
                  {tab.label}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Platform Overview</h3>
              
              {/* Recent Activity Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Recent Signups</h4>
                  <div className="space-y-2">
                    {creators.slice(0, 5).map((creator) => (
                      <div key={creator.id} className="flex justify-between text-sm">
                        <span>{creator.name}</span>
                        <span className="text-gray-500">
                          {new Date(creator.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Setup Status</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>✅ Fully Setup</span>
                      <span className="font-medium">{activeCreators}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>⏳ Pending Setup</span>
                      <span className="font-medium">{pendingSetup}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>❌ Not Started</span>
                      <span className="font-medium">{totalCreators - activeCreators - pendingSetup}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'creators' && (
            <CreatorsList creators={creators} />
          )}

          {activeTab === 'payments' && (
            <PaymentActivity recentActivity={recentActivity} />
          )}

          {activeTab === 'disputes' && (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">🚧</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Dispute Management</h3>
              <p className="text-gray-600">
                Dispute tracking will be available once Stripe webhooks are configured.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
