'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

interface Creator {
  id: string
  name: string
  email: string
  role: string
  stripe_account_id: string | null
  stripe_onboarding_complete: boolean
  price_monthly: number | null
  created_at: string
  updated_at: string
}

interface CreatorsListProps {
  creators: Creator[]
}

export function CreatorsList({ creators }: CreatorsListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'pending' | 'inactive'>('all')

  const filteredCreators = creators.filter(creator => {
    const matchesSearch = creator.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         creator.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && creator.stripe_onboarding_complete) ||
      (statusFilter === 'pending' && creator.stripe_account_id && !creator.stripe_onboarding_complete) ||
      (statusFilter === 'inactive' && !creator.stripe_account_id)

    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (creator: Creator) => {
    if (creator.stripe_onboarding_complete) {
      return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">✅ Active</span>
    } else if (creator.stripe_account_id) {
      return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">⏳ Pending</span>
    } else {
      return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">❌ Not Setup</span>
    }
  }

  const handleSendNotification = async (creatorId: string, type: 'setup_reminder' | 'compliance_issue') => {
    // TODO: Implement notification sending
    console.log('Sending notification:', { creatorId, type })
    alert(`Notification sent! (Feature coming soon)`)
  }

  const handleSuspendCreator = async (creatorId: string) => {
    if (!confirm('Are you sure you want to suspend this creator?')) return
    
    // TODO: Implement creator suspension
    console.log('Suspending creator:', creatorId)
    alert(`Creator suspended! (Feature coming soon)`)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search creators by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Status Filter */}
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as any)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="pending">Pending Setup</option>
          <option value="inactive">Not Setup</option>
        </select>
      </div>

      {/* Results Count */}
      <div className="text-sm text-gray-600">
        Showing {filteredCreators.length} of {creators.length} creators
      </div>

      {/* Creators Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Creator
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Joined
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCreators.map((creator) => (
                <tr key={creator.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{creator.name}</div>
                      <div className="text-sm text-gray-500">{creator.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(creator)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {creator.price_monthly ? `$${(creator.price_monthly / 100).toFixed(2)}/mo` : 'Not set'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(creator.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    {!creator.stripe_onboarding_complete && (
                      <Button
                        onClick={() => handleSendNotification(creator.id, 'setup_reminder')}
                        size="sm"
                        variant="outline"
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        Remind Setup
                      </Button>
                    )}
                    <Button
                      onClick={() => handleSuspendCreator(creator.id)}
                      size="sm"
                      variant="outline"
                      className="text-red-600 border-red-600 hover:bg-red-50"
                    >
                      Suspend
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredCreators.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No creators found matching your criteria.</div>
          </div>
        )}
      </div>
    </div>
  )
}
