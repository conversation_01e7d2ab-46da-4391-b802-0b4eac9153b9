'use client'

interface AdminStatsProps {
  totalCreators: number
  activeCreators: number
  pendingSetup: number
  totalRevenue: number
}

export function AdminStats({ totalCreators, activeCreators, pendingSetup, totalRevenue }: AdminStatsProps) {
  const formatCurrency = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`
  }

  const stats = [
    {
      label: 'Total Creators',
      value: totalCreators,
      icon: '👥',
      color: 'bg-blue-500',
      change: null
    },
    {
      label: 'Active Creators',
      value: activeCreators,
      icon: '✅',
      color: 'bg-green-500',
      change: totalCreators > 0 ? `${Math.round((activeCreators / totalCreators) * 100)}%` : '0%'
    },
    {
      label: 'Pending Setup',
      value: pendingSetup,
      icon: '⏳',
      color: 'bg-yellow-500',
      change: null
    },
    {
      label: 'Total Revenue',
      value: formatCurrency(totalRevenue),
      icon: '💰',
      color: 'bg-purple-500',
      change: null
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{stat.label}</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
              {stat.change && (
                <p className="text-sm text-gray-500 mt-1">{stat.change} of total</p>
              )}
            </div>
            <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center text-white text-xl`}>
              {stat.icon}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
