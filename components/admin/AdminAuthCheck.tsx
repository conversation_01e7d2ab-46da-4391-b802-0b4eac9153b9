'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

interface AdminAuthCheckProps {
  children: React.ReactNode
}

export function AdminAuthCheck({ children }: AdminAuthCheckProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = () => {
      const adminAuth = sessionStorage.getItem('admin_authenticated')
      const authTime = sessionStorage.getItem('admin_auth_time')
      
      if (!adminAuth || !authTime) {
        router.push('/admin/login')
        return
      }

      // Check if session is expired (24 hours)
      const authTimestamp = parseInt(authTime)
      const now = Date.now()
      const twentyFourHours = 24 * 60 * 60 * 1000
      
      if (now - authTimestamp > twentyFourHours) {
        // Session expired
        sessionStorage.removeItem('admin_authenticated')
        sessionStorage.removeItem('admin_auth_time')
        router.push('/admin/login')
        return
      }

      setIsAuthenticated(true)
      setIsLoading(false)
    }

    checkAuth()
  }, [router])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking admin access...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Will redirect to login
  }

  return (
    <div>
      {/* Admin Header with Logout */}
      <div className="bg-red-600 text-white px-4 py-2">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center gap-2">
            <span className="text-lg">🔒</span>
            <span className="font-medium">Admin Mode</span>
          </div>
          <button
            onClick={() => {
              sessionStorage.removeItem('admin_authenticated')
              sessionStorage.removeItem('admin_auth_time')
              router.push('/dashboard')
            }}
            className="text-sm bg-red-700 hover:bg-red-800 px-3 py-1 rounded transition-colors"
          >
            Exit Admin
          </button>
        </div>
      </div>
      
      {children}
    </div>
  )
}
