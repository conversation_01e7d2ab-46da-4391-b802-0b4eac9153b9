'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import Image from 'next/image'
import { GoldenPenRating } from './GoldenPenRating'

interface RecommendedBook {
  id: string
  title: string
  cover_image_url: string
  author_name: string
  average_rating: number
  review_count: number
  price_amount: number
  genre: string
  users?: {
    name: string
  }
}

interface BookRecommendationsProps {
  currentBookId: string
  currentBookGenre: string
  currentBookTags?: string[]
}

export function BookRecommendations({ 
  currentBookId, 
  currentBookGenre, 
  currentBookTags = [] 
}: BookRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<RecommendedBook[]>([])
  const [loading, setLoading] = useState(true)
  const [currentIndex, setCurrentIndex] = useState(0)

  const booksPerPage = 4
  const maxRecommendations = 12

  useEffect(() => {
    fetchRecommendations()
  }, [currentBookId, currentBookGenre])

  const fetchRecommendations = async () => {
    try {
      const supabase = createSupabaseClient()
      
      // Smart recommendation algorithm:
      // 1. Books in same genre with high ratings
      // 2. Books with similar tags
      // 3. Books by authors that readers of this genre also like
      // 4. Popular books overall as fallback
      
      const { data: books, error } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          cover_image_url,
          author_name,
          average_rating,
          review_count,
          price_amount,
          genre,
          tags,
          users(name)
        `)
        .neq('id', currentBookId)
        .eq('genre', currentBookGenre)
        .eq('is_ebook', true)
        .eq('is_private', false)
        .gte('average_rating', 3.0) // Only recommend well-rated books
        .order('average_rating', { ascending: false })
        .order('review_count', { ascending: false })
        .limit(maxRecommendations)

      if (error) {
        console.error('Error fetching recommendations:', error)
        return
      }

      // If we don't have enough books in the same genre, get popular books from other genres
      if (!books || books.length < 6) {
        const { data: fallbackBooks, error: fallbackError } = await supabase
          .from('projects')
          .select(`
            id,
            title,
            cover_image_url,
            author_name,
            average_rating,
            review_count,
            price_amount,
            genre,
            users(name)
          `)
          .neq('id', currentBookId)
          .neq('genre', currentBookGenre)
          .eq('is_ebook', true)
          .eq('is_private', false)
          .gte('average_rating', 4.0)
          .order('review_count', { ascending: false })
          .limit(maxRecommendations - (books?.length || 0))

        if (!fallbackError && fallbackBooks) {
          const combinedBooks = [...(books || []), ...fallbackBooks]
          setRecommendations(combinedBooks.slice(0, maxRecommendations))
        } else {
          setRecommendations(books || [])
        }
      } else {
        setRecommendations(books)
      }
    } catch (error) {
      console.error('Error in fetchRecommendations:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const nextSlide = () => {
    const maxIndex = Math.max(0, recommendations.length - booksPerPage)
    setCurrentIndex(prev => Math.min(prev + booksPerPage, maxIndex))
  }

  const prevSlide = () => {
    setCurrentIndex(prev => Math.max(prev - booksPerPage, 0))
  }

  const canGoNext = currentIndex + booksPerPage < recommendations.length
  const canGoPrev = currentIndex > 0

  if (loading) {
    return (
      <div className="mb-8">
        <h3 className="text-xl font-bold text-gray-900 mb-4">
          If You Like This Book, You Might Also Like
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="aspect-[3/4] bg-gray-200 rounded-lg mb-2"></div>
              <div className="h-4 bg-gray-200 rounded mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4 mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (recommendations.length === 0) {
    return null // Don't show section if no recommendations
  }

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-gray-900">
          If You Like This Book, You Might Also Like
        </h3>
        
        {/* Carousel Navigation */}
        {recommendations.length > booksPerPage && (
          <div className="flex gap-2">
            <button
              onClick={prevSlide}
              disabled={!canGoPrev}
              className={`p-2 rounded-full border ${
                canGoPrev 
                  ? 'border-gray-300 hover:border-gray-400 text-gray-600 hover:text-gray-800' 
                  : 'border-gray-200 text-gray-300 cursor-not-allowed'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={nextSlide}
              disabled={!canGoNext}
              className={`p-2 rounded-full border ${
                canGoNext 
                  ? 'border-gray-300 hover:border-gray-400 text-gray-600 hover:text-gray-800' 
                  : 'border-gray-200 text-gray-300 cursor-not-allowed'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Books Grid/Carousel */}
      <div className="overflow-hidden">
        <div 
          className="grid grid-cols-2 md:grid-cols-4 gap-4 transition-transform duration-300"
          style={{
            transform: `translateX(-${(currentIndex / booksPerPage) * 100}%)`,
            width: `${Math.ceil(recommendations.length / booksPerPage) * 100}%`
          }}
        >
          {recommendations.map((book) => (
            <Link
              key={book.id}
              href={`/books/${book.id}`}
              className="group block hover:shadow-lg transition-shadow duration-200 rounded-lg overflow-hidden"
            >
              {/* Book Cover */}
              <div className="aspect-[3/4] relative mb-3 rounded-lg overflow-hidden bg-gray-100">
                {book.cover_image_url ? (
                  <Image
                    src={book.cover_image_url}
                    alt={book.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-200"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                    <span className="text-gray-500 text-sm">No Cover</span>
                  </div>
                )}
              </div>

              {/* Book Info */}
              <div className="space-y-1">
                <h4 className="font-semibold text-gray-900 text-sm line-clamp-2 group-hover:text-blue-600 transition-colors">
                  {book.title}
                </h4>
                <p className="text-xs text-gray-600">
                  by {book.author_name || book.users?.name || 'Unknown Author'}
                </p>
                
                {/* Rating */}
                {book.average_rating > 0 && (
                  <div className="flex items-center gap-1">
                    <GoldenPenRating 
                      rating={book.average_rating}
                      size="sm"
                      showText={false}
                    />
                    <span className="text-xs text-gray-500">
                      ({book.review_count})
                    </span>
                  </div>
                )}

                {/* Price */}
                <p className="text-sm font-semibold text-gray-900">
                  {formatPrice(book.price_amount)}
                </p>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Dots Indicator */}
      {recommendations.length > booksPerPage && (
        <div className="flex justify-center mt-4 gap-2">
          {Array.from({ length: Math.ceil(recommendations.length / booksPerPage) }).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index * booksPerPage)}
              className={`w-2 h-2 rounded-full transition-colors ${
                Math.floor(currentIndex / booksPerPage) === index
                  ? 'bg-gray-800'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  )
}
