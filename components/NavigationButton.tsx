"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"

interface NavigationButtonProps {
  href: string
  children: React.ReactNode
  className?: string
  variant?: 'primary' | 'secondary' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
}

export function NavigationButton({
  href,
  children,
  className = "",
  variant = 'primary',
  size = 'md',
  disabled = false
}: NavigationButtonProps) {
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleClick = () => {
    if (disabled || loading) return
    
    setLoading(true)
    router.push(href)
    
    // Auto-reset loading after timeout in case navigation fails
    setTimeout(() => {
      setLoading(false)
    }, 5000)
  }

  // Base styles
  const baseStyles = "inline-flex items-center justify-center gap-2 font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
  
  // Size styles
  const sizeStyles = {
    sm: "px-3 py-2 text-sm rounded-lg",
    md: "px-4 py-2 text-base rounded-lg", 
    lg: "px-8 py-4 text-lg rounded-xl"
  }
  
  // Variant styles
  const variantStyles = {
    primary: "bg-blue-600 text-white hover:bg-blue-700",
    secondary: "bg-gray-100 text-gray-700 hover:bg-gray-200",
    outline: "border-2 border-current hover:bg-current/10"
  }

  const combinedClassName = `${baseStyles} ${sizeStyles[size]} ${variantStyles[variant]} ${className}`

  return (
    <button
      onClick={handleClick}
      disabled={disabled || loading}
      className={combinedClassName}
    >
      {loading ? (
        <>
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
          <span>Loading...</span>
        </>
      ) : (
        children
      )}
    </button>
  )
}
