"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { LoadingSpinner } from "./loading-spinner"

interface NavigationLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  activeClassName?: string
  showLoading?: boolean
}

export function NavigationLink({
  href,
  children,
  className = "",
  activeClassName = "",
  showLoading = true
}: NavigationLinkProps) {
  const pathname = usePathname()
  const router = useRouter()
  const [isLoading, setIsLoading] = React.useState(false)
  const isActive = pathname === href

  // Reset loading when pathname changes
  React.useEffect(() => {
    setIsLoading(false)
  }, [pathname])

  // Auto-reset loading after timeout
  React.useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        setIsLoading(false)
      }, 3000)
      return () => clearTimeout(timeout)
    }
  }, [isLoading])

  const handleClick = (e: React.MouseEvent) => {
    // Don't show loading if already on this page
    if (pathname === href) {
      return
    }

    if (showLoading) {
      e.preventDefault()
      setIsLoading(true)
      router.push(href)
    }
  }

  return (
    <Link
      href={href}
      onClick={handleClick}
      className={cn(
        "inline-flex items-center gap-2",
        className,
        isActive && activeClassName,
        isLoading && "pointer-events-none"
      )}
    >
      {isLoading && showLoading ? (
        <>
          <LoadingSpinner size="sm" />
          <span className="opacity-75">{children}</span>
        </>
      ) : (
        children
      )}
    </Link>
  )
}
