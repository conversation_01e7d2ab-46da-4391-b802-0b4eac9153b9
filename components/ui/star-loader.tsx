"use client"

import { motion } from 'framer-motion'
import { cn } from "@/lib/utils"

interface StarLoaderProps {
  size?: "sm" | "md" | "lg"
  className?: string
}

export function StarLoader({ size = "md", className }: StarLoaderProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6", 
    lg: "w-8 h-8"
  }

  const starVariants = {
    initial: { 
      scale: 0,
      rotate: 0,
      opacity: 0
    },
    animate: { 
      scale: [0, 1.2, 1],
      rotate: [0, 180, 360],
      opacity: [0, 1, 0.8]
    }
  }

  const containerVariants = {
    animate: {
      rotate: 360,
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "linear"
      }
    }
  }

  return (
    <motion.div
      className={cn("relative inline-flex items-center justify-center", sizeClasses[size], className)}
      variants={containerVariants}
      animate="animate"
    >
      {/* Center star */}
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        variants={starVariants}
        initial="initial"
        animate="animate"
        transition={{
          duration: 1.5,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
      >
        <span className="text-yellow-400">⭐</span>
      </motion.div>

      {/* Orbiting stars */}
      {[0, 120, 240].map((angle, index) => (
        <motion.div
          key={index}
          className="absolute"
          style={{
            transformOrigin: "center",
          }}
          animate={{
            rotate: [angle, angle + 360],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "linear",
            delay: index * 0.2
          }}
        >
          <motion.div
            className="relative"
            style={{
              transform: `translateY(-${size === 'sm' ? '12px' : size === 'md' ? '16px' : '20px'})`
            }}
            animate={{
              scale: [0.5, 1, 0.5],
              opacity: [0.3, 1, 0.3]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: index * 0.3
            }}
          >
            <span className="text-blue-400 text-xs">✨</span>
          </motion.div>
        </motion.div>
      ))}
    </motion.div>
  )
}
