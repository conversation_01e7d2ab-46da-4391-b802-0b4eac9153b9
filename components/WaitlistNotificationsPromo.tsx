'use client'

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

interface WaitlistStats {
  totalWaitlistSize: number
  projectsWithWaitlists: number
  recentSignups: number
  topProject: { title: string; count: number } | null
}

interface WaitlistNotificationsPromoProps {
  waitlistStats: WaitlistStats
}

export function WaitlistNotificationsPromo({ waitlistStats }: WaitlistNotificationsPromoProps) {
  const [selectedSize, setSelectedSize] = useState(100)
  
  const calculateCost = (size: number) => {
    return size * 0.05 // $0.05 per credit/notification
  }

  const exampleSizes = [50, 100, 250, 500, 1000, 2500]

  return (
    <div className="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 rounded-3xl p-8 border border-purple-100">
      
      {/* Header */}
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-white text-2xl">🔔</span>
        </div>
        <h2 className="text-3xl font-serif text-gray-800 mb-3">
          Waitlist Push Notifications
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Turn your waitlists into instant sales. Notify readers the moment your project goes live and capture immediate purchases.
        </p>
      </div>

      {/* Your Waitlist Stats */}
      {waitlistStats.totalWaitlistSize > 0 && (
        <div className="bg-white rounded-2xl p-6 mb-8 border border-gray-100">
          <h3 className="text-xl font-serif text-gray-800 mb-4">Your Waitlist Power</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{waitlistStats.totalWaitlistSize}</div>
              <div className="text-sm text-gray-600">Total Waitlist</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{waitlistStats.projectsWithWaitlists}</div>
              <div className="text-sm text-gray-600">Projects</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{waitlistStats.recentSignups}</div>
              <div className="text-sm text-gray-600">Recent Signups</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                ${calculateCost(waitlistStats.totalWaitlistSize).toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Notify All Cost</div>
            </div>
          </div>
          
          {waitlistStats.topProject && (
            <div className="mt-4 p-3 bg-purple-50 rounded-lg">
              <div className="text-sm text-purple-700">
                <strong>Top Project:</strong> &quot;{waitlistStats.topProject.title}&quot; with {waitlistStats.topProject.count} waitlist signups
              </div>
            </div>
          )}
        </div>
      )}

      {/* Pricing Calculator */}
      <div className="bg-white rounded-2xl p-6 mb-8 border border-gray-100">
        <h3 className="text-xl font-serif text-gray-800 mb-4">Pricing Calculator</h3>
        
        <div className="grid md:grid-cols-2 gap-6">
          {/* Size Selector */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Waitlist Size
            </label>
            <div className="grid grid-cols-3 gap-2 mb-4">
              {exampleSizes.map(size => (
                <button
                  key={size}
                  onClick={() => setSelectedSize(size)}
                  className={`p-2 rounded-lg text-sm font-medium transition-all ${
                    selectedSize === size
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
            
            <input
              type="number"
              value={selectedSize}
              onChange={(e) => setSelectedSize(Math.max(1, parseInt(e.target.value) || 1))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Custom size"
              min="1"
            />
          </div>

          {/* Cost Display */}
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 mb-2">
                ${calculateCost(selectedSize).toFixed(2)}
              </div>
              <div className="text-gray-600">
                for {selectedSize} notification credits
              </div>
              <div className="text-sm text-gray-500 mt-1">
                (5¢ per notification)
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Benefits */}
      <div className="grid md:grid-cols-3 gap-6 mb-8">
        <div className="text-center">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <span className="text-green-600 text-xl">⚡</span>
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">Instant Sales</h4>
          <p className="text-sm text-gray-600">
            Readers get notified immediately when your project goes live. No waiting, no delays.
          </p>
        </div>
        
        <div className="text-center">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <span className="text-blue-600 text-xl">📱</span>
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">Push Notifications</h4>
          <p className="text-sm text-gray-600">
            Delivered directly to their devices, even when they're not on OnlyDiary.
          </p>
        </div>
        
        <div className="text-center">
          <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <span className="text-purple-600 text-xl">💰</span>
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">Higher Conversion</h4>
          <p className="text-sm text-gray-600">
            Waitlist subscribers are 10x more likely to purchase than random visitors.
          </p>
        </div>
      </div>

      {/* Pricing Tiers */}
      <div className="bg-white rounded-2xl p-6 mb-8 border border-gray-100">
        <h3 className="text-xl font-serif text-gray-800 mb-4 text-center">Simple, Fair Pricing</h3>
        
        <div className="text-center mb-6">
          <div className="text-3xl font-bold text-purple-600 mb-2">
            $0.50 per 100 people
          </div>
          <div className="text-gray-600">
            Only pay for what you use. No monthly fees, no contracts.
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl p-6 text-white text-center">
          <h3 className="text-2xl font-bold mb-2">Flexible Credit Packages</h3>
          <p className="text-purple-100 mb-4">
            Buy exactly what you need - from 1 to 999 notification credits
          </p>

          <div className="bg-white/10 rounded-lg p-4 mb-4">
            <div className="text-3xl font-bold mb-1">$0.05 per credit</div>
            <div className="text-purple-100 text-sm">
              1 credit = 1 push notification • Minimum 1 credit • Maximum 999 credits
            </div>
          </div>

          <div className="grid grid-cols-3 gap-3 mb-6 text-sm">
            <div className="bg-white/10 rounded-lg p-3">
              <div className="font-semibold">Small Launch</div>
              <div>100 credits = $5.00</div>
            </div>
            <div className="bg-white/20 rounded-lg p-3 border border-white/30">
              <div className="font-semibold">Popular</div>
              <div>500 credits = $25.00</div>
            </div>
            <div className="bg-white/10 rounded-lg p-3">
              <div className="font-semibold">Big Launch</div>
              <div>1000 credits = $50.00</div>
            </div>
          </div>

          <a
            href={process.env.NEXT_PUBLIC_STRIPE_CREDITS_LINK || "https://buy.stripe.com/test_28E9AV0Ig46Ib9I0eFf3a00"}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block bg-white text-purple-600 px-8 py-3 rounded-lg font-bold hover:bg-gray-100 transition-colors text-lg"
          >
            🔔 Buy Notification Credits
          </a>

          <p className="text-purple-200 text-sm mt-3">
            Choose your quantity on the next page (1-999 credits)
          </p>
        </div>
      </div>



      {/* CTA */}
      <div className="text-center">
        {waitlistStats.totalWaitlistSize > 0 ? (
          <div className="space-y-4">
            <Button className="bg-purple-600 text-white hover:bg-purple-700 px-8 py-3 text-lg">
              Send Notifications to {waitlistStats.totalWaitlistSize} People
              <span className="ml-2 text-purple-200">
                (${calculateCost(waitlistStats.totalWaitlistSize).toFixed(2)})
              </span>
            </Button>
            <p className="text-sm text-gray-600">
              Turn your waitlist into instant revenue
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-gray-600 mb-4">
              Start building your waitlist by making some projects private
            </p>
            <Link href="/write" className="inline-block">
              <Button className="bg-purple-600 text-white hover:bg-purple-700 px-8 py-3">
                Create Your First Project
              </Button>
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
