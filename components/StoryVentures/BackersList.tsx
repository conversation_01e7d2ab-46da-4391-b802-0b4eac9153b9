"use client"

import { useState, useEffect } from "react"
// Client-safe price formatting function
function formatPrice(cents: number): string {
  return `$${(cents / 100).toFixed(2)}`
}
import { Heart, MessageCircle, Users } from "lucide-react"

interface BackersListProps {
  entryId: string
  showPublicly?: boolean
  limit?: number
  className?: string
}

interface PublicBacker {
  backer_name: string
  amount_cents: number | null
  message: string | null
  created_at: string
}

export function BackersList({ 
  entryId, 
  showPublicly = true, 
  limit = 10, 
  className = "" 
}: BackersListProps) {
  const [backers, setBackers] = useState<PublicBacker[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchBackers = async () => {
      if (!showPublicly) {
        setLoading(false)
        return
      }

      try {
        const response = await fetch(`/api/story-ventures/backers/${entryId}?limit=${limit}`)
        if (response.ok) {
          const data = await response.json()
          setBackers(data.backers || [])
        } else {
          setError('Failed to load backers')
        }
      } catch (err) {
        console.error('Error fetching backers:', err)
        setError('Failed to load backers')
      } finally {
        setLoading(false)
      }
    }

    if (entryId) {
      fetchBackers()
    }
  }, [entryId, showPublicly, limit])

  if (!showPublicly || (!loading && backers.length === 0)) {
    return null
  }

  if (loading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-5 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    )
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    const diffInWeeks = Math.floor(diffInDays / 7)
    if (diffInWeeks < 4) return `${diffInWeeks}w ago`
    
    return date.toLocaleDateString()
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Users className="w-5 h-5 text-blue-500" />
        <h3 className="text-lg font-serif text-gray-800">
          Community Support
        </h3>
      </div>

      <div className="space-y-4">
        {backers.map((backer, index) => (
          <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
            {/* Avatar */}
            <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
              {backer.backer_name === 'Anonymous' ? '?' : backer.backer_name.charAt(0).toUpperCase()}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-gray-800">
                  {backer.backer_name}
                </span>
                {backer.amount_cents && (
                  <span className="text-sm text-green-600 font-medium">
                    {formatPrice(backer.amount_cents)}
                  </span>
                )}
                <span className="text-xs text-gray-500">
                  {formatTimeAgo(backer.created_at)}
                </span>
              </div>

              {backer.message && (
                <div className="flex items-start gap-2 mt-2">
                  <MessageCircle className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-gray-600 italic">
                    "{backer.message}"
                  </p>
                </div>
              )}

              {!backer.message && (
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Heart className="w-4 h-4 text-red-400" />
                  <span>Supporting this story</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Encouragement for more backers */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <p className="text-center text-sm text-gray-600">
          <strong>Join these supporters</strong> and help bring this creative vision to life
        </p>
      </div>
    </div>
  )
}
