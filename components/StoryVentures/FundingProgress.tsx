"use client"

import { useState, useEffect } from "react"
// Client-safe price formatting function
function formatPrice(cents: number): string {
  return `$${(cents / 100).toFixed(2)}`
}
import { Users, Clock, Target, TrendingUp } from "lucide-react"

interface FundingProgressProps {
  entryId: string
  goalCents?: number
  raisedCents?: number
  backerCount?: number
  deadline?: string
  showPublicly?: boolean
  className?: string
}

interface FundingStats {
  total_raised_cents: number
  backer_count: number
  goal_cents: number
  funding_percentage: number
  days_remaining: number
  is_funded: boolean
  public_backers_count: number
}

export function FundingProgress({
  entryId,
  goalCents,
  raisedCents,
  backerCount,
  deadline,
  showPublicly = true,
  className = ""
}: FundingProgressProps) {
  const [stats, setStats] = useState<FundingStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch(`/api/story-ventures/stats/${entryId}`)
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        }
      } catch (error) {
        console.error('Error fetching funding stats:', error)
      } finally {
        setLoading(false)
      }
    }

    if (entryId) {
      fetchStats()
    }
  }, [entryId])

  // Use props as fallback if API data not available
  const displayStats = stats || {
    total_raised_cents: raisedCents || 0,
    backer_count: backerCount || 0,
    goal_cents: goalCents || 0,
    funding_percentage: goalCents ? ((raisedCents || 0) / goalCents) * 100 : 0,
    days_remaining: deadline ? Math.ceil((new Date(deadline).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : null,
    is_funded: goalCents ? (raisedCents || 0) >= goalCents : false,
    public_backers_count: backerCount || 0
  }

  if (!showPublicly) {
    return null
  }

  if (loading) {
    return (
      <div className={`bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-2 bg-gray-200 rounded mb-4"></div>
          <div className="flex gap-4">
            <div className="h-3 bg-gray-200 rounded w-20"></div>
            <div className="h-3 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>
    )
  }

  const progressPercentage = Math.min(displayStats.funding_percentage, 100)
  const isActive = displayStats.days_remaining === null || displayStats.days_remaining > 0
  const statusColor = displayStats.is_funded ? 'green' : isActive ? 'blue' : 'gray'

  return (
    <div className={`bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-serif text-blue-800 flex items-center gap-2">
          <Target className="w-5 h-5" />
          Story Venture
        </h3>
        {displayStats.is_funded && (
          <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
            ✅ Funded
          </span>
        )}
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">
            {formatPrice(displayStats.total_raised_cents)} raised
          </span>
          {displayStats.goal_cents > 0 && (
            <span className="text-sm text-gray-600">
              of {formatPrice(displayStats.goal_cents)} goal
            </span>
          )}
        </div>
        
        {displayStats.goal_cents > 0 && (
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-500 ${
                statusColor === 'green' ? 'bg-green-500' : 
                statusColor === 'blue' ? 'bg-blue-500' : 'bg-gray-400'
              }`}
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        )}
        
        {displayStats.goal_cents > 0 && (
          <div className="text-right mt-1">
            <span className="text-sm font-medium text-gray-700">
              {displayStats.funding_percentage.toFixed(1)}%
            </span>
          </div>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="flex items-center gap-2 text-gray-700">
          <Users className="w-4 h-4 text-blue-500" />
          <span>
            <strong>{displayStats.backer_count}</strong> {displayStats.backer_count === 1 ? 'backer' : 'backers'}
          </span>
        </div>
        
        {displayStats.days_remaining !== null && (
          <div className="flex items-center gap-2 text-gray-700">
            <Clock className="w-4 h-4 text-purple-500" />
            <span>
              {displayStats.days_remaining > 0 ? (
                <>
                  <strong>{displayStats.days_remaining}</strong> {displayStats.days_remaining === 1 ? 'day' : 'days'} left
                </>
              ) : (
                <span className="text-red-600 font-medium">Campaign ended</span>
              )}
            </span>
          </div>
        )}
      </div>

      {/* Social Proof */}
      {displayStats.public_backers_count > 0 && (
        <div className="mt-4 pt-4 border-t border-blue-200">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <TrendingUp className="w-4 h-4 text-green-500" />
            <span>
              {displayStats.public_backers_count} {displayStats.public_backers_count === 1 ? 'person believes' : 'people believe'} in this story
            </span>
          </div>
        </div>
      )}

      {/* Community Support Message */}
      {displayStats.backer_count > 0 && (
        <div className="mt-3 text-center">
          <p className="text-sm text-blue-700 font-medium">
            Join the community supporting this creative vision
          </p>
        </div>
      )}
    </div>
  )
}
