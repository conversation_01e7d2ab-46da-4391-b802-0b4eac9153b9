"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"

interface StripeConnectButtonProps {
  isConnected: boolean
  onboardingComplete: boolean
}

export function StripeConnectButton({ isConnected, onboardingComplete }: StripeConnectButtonProps) {
  const [loading, setLoading] = useState(false)

  // Check onboarding status when component mounts (in case user returns from Stripe)
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (isConnected && !onboardingComplete) {
        try {
          const response = await fetch('/api/stripe/check-onboarding', {
            method: 'POST',
          })

          if (response.ok) {
            const data = await response.json()
            if (data.updated) {
              // Refresh the page to show updated status
              window.location.reload()
            }
          }
        } catch (error) {
          console.error('Error checking onboarding status:', error)
        }
      }
    }

    // Check status after a short delay (in case user just returned from Stripe)
    const timer = setTimeout(checkOnboardingStatus, 2000)
    return () => clearTimeout(timer)
  }, [isConnected, onboardingComplete])

  const handleConnect = async () => {
    setLoading(true)

    try {
      console.log('Starting Stripe Connect...')
      const response = await fetch('/api/stripe/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        let errorData
        try {
          errorData = await response.json()
        } catch {
          errorData = { error: await response.text() }
        }

        console.error('API Error:', errorData)

        const errorMsg = errorData.details?.message || errorData.error || `HTTP ${response.status}`
        alert(`Stripe Setup Error: ${errorMsg}`)
        return
      }

      const data = await response.json()
      console.log('Response data:', data)

      if (data.url) {
        console.log('Redirecting to:', data.url)
        window.location.href = data.url
      } else {
        console.error('No URL returned from Stripe Connect')
        alert('Error: No redirect URL received from Stripe')
      }
    } catch (error) {
      console.error('Error connecting to Stripe:', error)
      alert(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  if (isConnected && onboardingComplete) {
    return (
      <div className="flex items-center gap-2">
        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        <span className="text-sm text-green-600 font-medium">
          Stripe Connected
        </span>
      </div>
    )
  }

  if (isConnected && !onboardingComplete) {
    return (
      <Button
        onClick={handleConnect}
        isLoading={loading}
        className="bg-blue-600 text-white hover:bg-blue-700"
      >
        Complete Stripe Setup
      </Button>
    )
  }

  return (
    <Button
      onClick={handleConnect}
      isLoading={loading}
      className="bg-blue-600 text-white hover:bg-blue-700"
    >
      Connect Stripe Account
    </Button>
  )
}
