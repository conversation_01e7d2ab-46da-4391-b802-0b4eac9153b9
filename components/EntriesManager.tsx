'use client'

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { LinkButton } from '@/components/ui/link-button'
import { DeleteConfirmModal, useDeleteConfirm } from '@/components/DeleteConfirmModal'

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  is_hidden: boolean
  view_count: number
}

interface EntriesManagerProps {
  initialEntries: DiaryEntry[]
  onEntriesChange?: (entries: DiaryEntry[]) => void
}

export function EntriesManager({ initialEntries, onEntriesChange }: EntriesManagerProps) {
  const [entries, setEntries] = useState(initialEntries)
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set())
  const [entryToDelete, setEntryToDelete] = useState<DiaryEntry | null>(null)
  const [selectedEntries, setSelectedEntries] = useState<Set<string>>(new Set())
  const [bulkDeleting, setBulkDeleting] = useState(false)
  const supabase = createSupabaseClient()
  const deleteConfirm = useDeleteConfirm()

  const formatDate = (dateString: string) => {
    // Use a consistent format that won't cause hydration issues
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = date.toLocaleDateString('en-US', { month: 'short' })
    const day = date.getDate()

    return `${month} ${day}, ${year}`
  }

  // Bulk selection functions
  const toggleSelectEntry = (entryId: string) => {
    setSelectedEntries(prev => {
      const newSet = new Set(prev)
      if (newSet.has(entryId)) {
        newSet.delete(entryId)
      } else {
        newSet.add(entryId)
      }
      return newSet
    })
  }

  const selectAllEntries = () => {
    setSelectedEntries(new Set(entries.map(entry => entry.id)))
  }

  const clearSelection = () => {
    setSelectedEntries(new Set())
  }

  const isAllSelected = selectedEntries.size === entries.length && entries.length > 0

  // Bulk delete function
  const handleBulkDelete = () => {
    if (selectedEntries.size === 0) return

    deleteConfirm.showConfirm(async () => {
      setBulkDeleting(true)
      const entriesToDelete = Array.from(selectedEntries)

      try {
        // Delete all related data for selected entries
        for (const entryId of entriesToDelete) {
          // Delete in the same order as single delete
          await supabase.from('hourly_love_stats').delete().eq('diary_entry_id', entryId)
          await supabase.from('post_reads').delete().eq('diary_entry_id', entryId)
          await supabase.from('loves').delete().eq('diary_entry_id', entryId)
          await supabase.from('diary_photos').delete().eq('diary_entry_id', entryId)
          await supabase.from('videos').delete().eq('post_id', entryId)

          const { error: entryError } = await supabase
            .from('diary_entries')
            .delete()
            .eq('id', entryId)

          if (entryError) {
            throw new Error(`Failed to delete entry ${entryId}: ${entryError.message}`)
          }
        }

        // Remove from local state
        const updatedEntries = entries.filter(entry => !selectedEntries.has(entry.id))
        setEntries(updatedEntries)
        setSelectedEntries(new Set())

        // Notify parent component (if callback provided)
        onEntriesChange?.(updatedEntries)

      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        console.error('Bulk delete error:', error)
        throw new Error(`Failed to delete entries: ${errorMessage}`)
      } finally {
        setBulkDeleting(false)
      }
    })
  }

  const handleDelete = (entry: DiaryEntry) => {
    setEntryToDelete(entry)
    deleteConfirm.showConfirm(async () => {
      setDeletingIds(prev => new Set(prev).add(entry.id))

      try {
        // Delete all related data in the correct order to avoid foreign key constraints

        // 1. Delete hourly love stats
        await supabase
          .from('hourly_love_stats')
          .delete()
          .eq('diary_entry_id', entry.id)

        // 2. Delete post reads
        await supabase
          .from('post_reads')
          .delete()
          .eq('diary_entry_id', entry.id)

        // 3. Delete loves
        await supabase
          .from('loves')
          .delete()
          .eq('diary_entry_id', entry.id)

        // 4. Delete photos
        await supabase
          .from('diary_photos')
          .delete()
          .eq('diary_entry_id', entry.id)

        // 5. Finally delete the entry itself
        const { error: entryError } = await supabase
          .from('diary_entries')
          .delete()
          .eq('id', entry.id)

        if (entryError) {
          throw new Error(entryError.message)
        }

        // Remove from local state
        const updatedEntries = entries.filter(e => e.id !== entry.id)
        setEntries(updatedEntries)

        // Notify parent component (if callback provided)
        onEntriesChange?.(updatedEntries)

      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        console.error('Delete error:', error)
        throw new Error(`Failed to delete entry: ${errorMessage}`)
      } finally {
        setDeletingIds(prev => {
          const newSet = new Set(prev)
          newSet.delete(entry.id)
          return newSet
        })
        setEntryToDelete(null)
      }
    })
  }

  if (entries.length === 0) {
    return (
      <div className="border border-gray-200 rounded-lg p-6 text-center">
        <h3 className="text-lg font-serif text-gray-800 mb-3">
          Start Writing
        </h3>
        <p className="text-gray-600 font-serif mb-4 text-sm">
          Create your first entry to start building your audience.
        </p>
        <LinkButton
          href="/write"
          className="bg-gray-900 text-white hover:bg-gray-800 font-medium px-4 py-2 text-sm"
        >
          Write Your First Story
        </LinkButton>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header with entry count */}
      {entries.length > 0 && (
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            All Entries ({entries.length})
          </h3>
        </div>
      )}

      {/* Bulk Actions Toolbar */}
      {entries.length > 0 && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-3 bg-gray-50 rounded-lg border">
          <div className="flex items-center gap-3">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={isAllSelected}
                onChange={isAllSelected ? clearSelection : selectAllEntries}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">
                {selectedEntries.size > 0
                  ? `${selectedEntries.size} selected`
                  : 'Select all'
                }
              </span>
            </label>

            {selectedEntries.size > 0 && (
              <button
                onClick={clearSelection}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Clear selection
              </button>
            )}
          </div>

          {selectedEntries.size > 0 && (
            <button
              onClick={handleBulkDelete}
              disabled={bulkDeleting || deleteConfirm.isLoading}
              className="w-full sm:w-auto bg-red-600 text-white hover:bg-red-700 disabled:bg-red-400 px-6 py-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2 min-h-[48px]"
            >
              {bulkDeleting ? (
                <>
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Deleting...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Delete {selectedEntries.size} {selectedEntries.size === 1 ? 'entry' : 'entries'}
                </>
              )}
            </button>
          )}
        </div>
      )}

      {/* Entries List */}
      <div className="space-y-3">
        {entries.map((entry) => (
        <div key={entry.id} className={`border rounded-lg p-4 transition-colors ${
          selectedEntries.has(entry.id)
            ? 'border-blue-300 bg-blue-50'
            : 'border-gray-200 hover:border-gray-300'
        }`}>
          <div className="flex flex-col sm:flex-row sm:items-start gap-3">
            {/* Checkbox */}
            <label className="flex items-center cursor-pointer sm:mt-1">
              <input
                type="checkbox"
                checked={selectedEntries.has(entry.id)}
                onChange={() => toggleSelectEntry(entry.id)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
            </label>

            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                <h3 className="font-medium text-gray-900 truncate">{entry.title}</h3>
                <div className="flex gap-2">
                  {entry.is_free && (
                    <span className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded flex-shrink-0">
                      FREE
                    </span>
                  )}
                  {entry.is_hidden && (
                    <span className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded flex-shrink-0">
                      PRIVATE
                    </span>
                  )}
                </div>
              </div>
              <p className="text-gray-600 text-sm mb-2 leading-relaxed">
                {entry.body_md.slice(0, 100)}...
              </p>
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span>{formatDate(entry.created_at)}</span>
                <span className="flex items-center gap-1">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  {entry.view_count || 0} views
                </span>
              </div>
            </div>

            {/* Action Buttons - Mobile responsive */}
            <div className="flex flex-col sm:flex-row gap-2 sm:flex-shrink-0 w-full sm:w-auto">
              <LinkButton
                href={`/d/${entry.id}`}
                variant="secondary"
                size="sm"
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 border-0 text-xs px-3 py-2 w-full sm:w-auto text-center"
              >
                View
              </LinkButton>
              <LinkButton
                href={`/write/diary?edit=${entry.id}`}
                variant="secondary"
                size="sm"
                className="bg-gray-900 text-white hover:bg-gray-800 border-0 text-xs px-3 py-2 w-full sm:w-auto text-center"
              >
                Edit
              </LinkButton>
              <button
                onClick={() => handleDelete(entry)}
                disabled={deletingIds.has(entry.id) || deleteConfirm.isLoading}
                className="bg-red-600 text-white hover:bg-red-700 disabled:bg-red-400 border-0 text-xs px-3 py-2 rounded-md font-medium transition-colors w-full sm:w-auto"
              >
                {deletingIds.has(entry.id) ? (
                  <span className="flex items-center justify-center gap-1">
                    <svg className="w-3 h-3 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Deleting...
                  </span>
                ) : (
                  'Delete'
                )}
              </button>
            </div>
          </div>
        </div>
      ))}
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteConfirm.isOpen}
        onClose={deleteConfirm.handleClose}
        onConfirm={deleteConfirm.handleConfirm}
        isLoading={deleteConfirm.isLoading}
        entryTitle={entryToDelete?.title || (selectedEntries.size > 1 ? `${selectedEntries.size} entries` : '')}
        title={selectedEntries.size > 1 ? "Delete Multiple Entries" : "Delete Entry"}
        message={
          selectedEntries.size > 1
            ? `Are you sure you want to delete ${selectedEntries.size} entries? This action cannot be undone and all associated photos, videos, comments, and statistics will also be deleted.`
            : "Are you sure you want to delete this entry? This action cannot be undone and all associated photos, videos, comments, and statistics will also be deleted."
        }
        confirmText={selectedEntries.size > 1 ? "Delete All Forever" : "Delete Forever"}
      />
    </div>
  )
}
