'use client'

import { useState, useEffect } from "react"
import Link from "next/link"
import { SubscribeButton } from "@/components/SubscribeButton"
import { FollowButton } from "@/components/FollowButton"
import { MailingListButton } from "@/components/MailingListButton"
import { SmartTypographyWrapper } from "@/components/SmartTypographyWrapper"
import { PaywallContent } from "@/components/PaywallContent"
import { createSupabaseClient } from "@/lib/supabase/client"
import { VideoThumbnail } from "@/components/VideoThumbnail"

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  is_free: boolean
  created_at: string
  view_count?: number
  photos?: Array<{ id: string; url: string; alt_text: string }>
  videos?: Array<{ id: string; r2_public_url: string; title: string; view_count: number; custom_thumbnail_url?: string }>
}

interface Project {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  is_complete: boolean
  price_type: 'project' | 'chapters'
  price_amount: number
  total_chapters: number
  total_words: number
  created_at: string
}

interface WriterProfileClientProps {
  writer: any
  diaryEntries: DiaryEntry[]
  projects: Project[]
  hasActiveSubscription: boolean
  isFollowing: boolean
  isOwnProfile: boolean
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Helper function to format view counts
function formatViewCount(count: number): string {
  if (count < 1000) {
    return count.toString()
  } else if (count < 1000000) {
    const k = count / 1000
    return k % 1 === 0 ? `${k}k` : `${k.toFixed(1)}k`
  } else {
    const m = count / 1000000
    return m % 1 === 0 ? `${m}M` : `${m.toFixed(1)}M`
  }
}

export function WriterProfileClient({
  writer,
  diaryEntries,
  projects,
  hasActiveSubscription,
  isFollowing,
  isOwnProfile
}: WriterProfileClientProps) {
  const [activeTab, setActiveTab] = useState<'diary' | 'books'>('diary')
  const [entriesWithPhotos, setEntriesWithPhotos] = useState<DiaryEntry[]>(diaryEntries)

  // Load photos and videos for diary entries
  useEffect(() => {
    const loadMediaData = async () => {
      const supabase = createSupabaseClient()

      const entriesWithMediaData = await Promise.all(
        diaryEntries.map(async (entry) => {
          // Load photos
          const { data: photos } = await supabase
            .from('photos')
            .select('id, url, alt_text')
            .eq('diary_entry_id', entry.id)
            .eq('moderation_status', 'approved')
            .order('created_at', { ascending: true })

          // Load videos
          const { data: videos } = await supabase
            .from('videos')
            .select('id, r2_public_url, title, view_count, custom_thumbnail_url')
            .eq('post_id', entry.id)
            .order('created_at', { ascending: true })

          return {
            ...entry,
            photos: photos || [],
            videos: videos || []
          }
        })
      )

      setEntriesWithPhotos(entriesWithMediaData)
    }

    loadMediaData()
  }, [diaryEntries])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        
        {/* Enhanced Writer Profile Header */}
        <div className="bg-white rounded-3xl p-6 sm:p-8 shadow-xl mb-8 border border-gray-100">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-6">
            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
              {/* Profile Picture */}
              <div className="w-24 h-24 sm:w-28 sm:h-28 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center overflow-hidden shadow-lg">
                {writer.writer_avatar ? (
                  <img
                    src={writer.writer_avatar}
                    alt={writer.writer_name || 'Writer avatar'}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-3xl sm:text-4xl font-serif text-white font-bold">
                    {(writer.writer_name || 'A').charAt(0).toUpperCase()}
                  </span>
                )}
              </div>
              
              <div className="flex-1 text-center sm:text-left">
                <h1 className="text-2xl sm:text-4xl font-serif mb-3 text-gray-800">
                  {writer.writer_name || 'Anonymous Writer'}
                </h1>
                {writer.writer_bio && (
                  <p className="text-gray-600 font-serif mb-4 leading-relaxed text-sm sm:text-base">
                    {writer.writer_bio}
                  </p>
                )}

                {/* Mailing List Link (subtle) */}
                {!isOwnProfile && (
                  <div className="mb-4">
                    <MailingListButton
                      creatorId={writer.writer_id}
                      creatorName={writer.writer_name}
                      customUrl={writer.custom_url}
                      variant="link"
                      className="text-sm"
                    />
                  </div>
                )}

                {/* Stats */}
                <div className="flex flex-wrap justify-center sm:justify-start gap-4 sm:gap-6 mb-4 text-sm">
                  <div className="text-center">
                    <div className="font-bold text-lg text-purple-600">{diaryEntries.length}</div>
                    <div className="text-gray-500">Diary Entries</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg text-blue-600">{projects.length}</div>
                    <div className="text-gray-500">Book Projects</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg text-green-600">
                      {(
                        projects.reduce((sum, p) => sum + p.total_words, 0) +
                        diaryEntries.reduce((sum, entry) => sum + entry.body_md.split(' ').length, 0)
                      ).toLocaleString()}
                    </div>
                    <div className="text-gray-500">Total Words</div>
                  </div>
                </div>

                {/* Subscription Status & CTA */}
                {hasActiveSubscription ? (
                  <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                    <p className="text-green-800 font-medium">✓ You're subscribed to this writer</p>
                    <p className="text-green-600 text-sm">You have access to all content</p>
                  </div>
                ) : writer.writer_price_monthly ? (
                  <div className="flex flex-col sm:flex-row items-center gap-4">
                    <span className="text-lg font-bold text-gray-800">
                      {formatPrice(writer.writer_price_monthly)}/month
                    </span>
                    <SubscribeButton
                      writerId={writer.writer_id}
                      price={writer.writer_price_monthly}
                    />
                  </div>
                ) : (
                  <div className="flex flex-col sm:flex-row items-center gap-4">
                    <p className="text-gray-500 text-sm">Free to follow</p>
                    <FollowButton
                      writerId={writer.writer_id}
                      writerName={writer.writer_name}
                      initialIsFollowing={isFollowing}
                    />
                  </div>
                )}


              </div>
            </div>

            {/* Edit Button (only for own profile) */}
            {isOwnProfile && (
              <Link
                href="/profile/edit"
                className="bg-gray-800 text-white px-6 py-3 rounded-xl font-medium hover:bg-gray-700 transition-colors self-center sm:self-start"
              >
                ⚙️ Edit Profile
              </Link>
            )}
          </div>
        </div>

        {/* Content Type Tabs */}
        <div className="bg-white rounded-2xl p-2 shadow-lg mb-8 border border-gray-100">
          <div className="flex">
            <button
              onClick={() => setActiveTab('diary')}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-300 ${
                activeTab === 'diary'
                  ? 'bg-purple-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <span className="flex items-center justify-center gap-2">
                <span>📔</span>
                <span>Diary Entries</span>
                <span className="text-xs opacity-75">({diaryEntries.length})</span>
              </span>
            </button>
            <button
              onClick={() => setActiveTab('books')}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-300 ${
                activeTab === 'books'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <span className="flex items-center justify-center gap-2">
                <span>📚</span>
                <span>Book Projects</span>
                <span className="text-xs opacity-75">({projects.length})</span>
              </span>
            </button>
          </div>
        </div>

        {/* Content Display */}
        {activeTab === 'diary' ? (
          <DiaryEntriesSection
            entries={entriesWithPhotos}
            hasActiveSubscription={hasActiveSubscription}
            writerName={writer.writer_name}
            writerId={writer.writer_id}
            isOwnProfile={isOwnProfile}
          />
        ) : (
          <BookProjectsSection 
            projects={projects}
            writerName={writer.writer_name}
          />
        )}

        {/* Subscription CTA for non-subscribers */}
        {!hasActiveSubscription && writer.writer_price_monthly && (
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-3xl p-8 shadow-lg mt-12 text-center border border-purple-100">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-white text-2xl">✨</span>
            </div>
            <h3 className="text-2xl font-serif mb-4 text-gray-800">
              Unlock All Content
            </h3>
            <p className="text-gray-600 font-serif mb-6 text-lg max-w-2xl mx-auto">
              Subscribe to access all of {writer.writer_name}'s diary entries, book projects, and future content
            </p>
            <SubscribeButton 
              writerId={writer.writer_id} 
              price={writer.writer_price_monthly} 
            />
          </div>
        )}
      </div>
    </div>
  )
}

// Diary Entries Section Component - Uniform Grid Layout
function DiaryEntriesSection({
  entries,
  hasActiveSubscription,
  writerName,
  writerId,
  isOwnProfile
}: {
  entries: DiaryEntry[]
  hasActiveSubscription: boolean
  writerName: string
  writerId: string
  isOwnProfile: boolean
}) {
  if (entries.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📔</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Diary Entries Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any diary entries yet. Check back soon!
        </p>
      </div>
    )
  }

  // Separate pinned and regular entries (handle case where is_pinned might not exist)
  const pinnedEntries = entries.filter(entry => (entry as any).is_pinned === true)
  const regularEntries = entries.filter(entry => (entry as any).is_pinned !== true)

  return (
    <div className="space-y-8">
      {/* Pinned Entry - Featured */}
      {pinnedEntries.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
            <span className="text-yellow-500">📌</span>
            Start Here
          </h3>
          <DiaryEntryCard
            entry={pinnedEntries[0]}
            hasAccess={pinnedEntries[0].is_free || hasActiveSubscription || isOwnProfile}
            featured={true}
          />
        </div>
      )}

      {/* Regular Entries Grid - Compact & Uniform */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4" style={{ gridAutoRows: '1fr' }}>
        {regularEntries.map((entry) => (
          <DiaryEntryCard
            key={entry.id}
            entry={entry}
            hasAccess={entry.is_free || hasActiveSubscription || isOwnProfile}
            featured={false}
          />
        ))}
      </div>

      {/* Story Map Navigation */}
      {entries.length > 0 && (
        <StoryMapSection entries={entries} writerName={writerName} />
      )}
    </div>
  )
}

// Individual Diary Entry Card Component
function DiaryEntryCard({
  entry,
  hasAccess,
  featured = false
}: {
  entry: DiaryEntry
  hasAccess: boolean
  featured?: boolean
}) {
  // Get preview text (first 120 characters)
  const previewText = entry.body_md
    .replace(/[#*`_~]/g, '') // Remove markdown formatting
    .replace(/\n/g, ' ') // Replace newlines with spaces
    .substring(0, 120)
    .trim()

  const wordCount = entry.body_md.split(' ').length
  const readTime = Math.ceil(wordCount / 200)
  const firstPhoto = entry.photos?.[0]
  const firstVideo = entry.videos?.[0]
  const totalViews = (entry.view_count || 0) + (entry.videos?.reduce((sum, video) => sum + (video.view_count || 0), 0) || 0)

  return (
    <Link href={`/d/${entry.id}`} className="group block h-full">
      <div className={`bg-white rounded-lg sm:rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 group-hover:scale-[1.01] h-full flex flex-col ${
        featured ? 'sm:col-span-2 lg:col-span-3' : ''
      }`}>

        {/* Image/Video Section - Square Aspect Ratio */}
        <div className="relative bg-gradient-to-br from-purple-100 to-blue-100 aspect-square">
          {firstVideo ? (
            <VideoThumbnail
              videoUrl={firstVideo.r2_public_url}
              customThumbnailUrl={firstVideo.custom_thumbnail_url}
              alt={entry.title}
              className="w-full h-full"
              timeInSeconds={1}
              showPlayButton={true}
              playButtonSize="md"
            />
          ) : firstPhoto ? (
            <img
              src={firstPhoto.url}
              alt={firstPhoto.alt_text || entry.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <span className="opacity-50 text-xl">📝</span>
            </div>
          )}

          {/* Status Badges */}
          <div className="absolute top-1.5 right-1.5 flex gap-1">
            {entry.is_free && (
              <span className="bg-green-500/90 text-white text-xs px-1 py-0.5 rounded-full font-medium">
                FREE
              </span>
            )}
            {(entry as any).is_pinned === true && (
              <span className="bg-yellow-500/90 text-white text-xs px-1 py-0.5 rounded-full font-medium">
                📌
              </span>
            )}
          </div>

          {/* Lock Overlay for Paid Content */}
          {!hasAccess && (
            <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
              <div className="bg-white/90 rounded-full p-2">
                <svg className="w-4 h-4 text-gray-700" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          )}
        </div>

        {/* Content Section - Compact */}
        <div className={`p-3 ${featured ? 'sm:p-4' : ''} flex-1 flex flex-col`}>
          {/* Card Header */}
          <div className="flex items-start justify-between mb-1.5">
            <h3 className={`font-serif text-gray-800 line-clamp-1 group-hover:text-purple-600 transition-colors ${
              featured ? 'text-lg sm:text-xl md:text-2xl' : 'text-sm font-semibold'
            }`} title={entry.title}>
              {entry.title}
            </h3>
            <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
              {formatDate(entry.created_at).split(',')[0]}
            </span>
          </div>

          {/* Card Description - Compact */}
          <p className={`text-gray-600 line-clamp-2 mb-auto ${
            featured ? 'text-sm' : 'text-xs'
          }`}>
            {previewText}
            {entry.body_md.length > 120 && '...'}
          </p>

          {/* Stats and Button - Compact Bottom Section */}
          <div className="mt-2">
            {/* Stats Row - Clean and Spacious */}
            <div className="flex items-center gap-3 text-xs text-gray-500 mb-2">
              <span>{wordCount > 1000 ? `${Math.round(wordCount/1000)}k` : wordCount} words</span>

              {/* View Count */}
              {totalViews > 0 && (
                <>
                  <span>•</span>
                  <span className="flex items-center gap-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                      <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                    </svg>
                    <span>{formatViewCount(totalViews)}</span>
                  </span>
                </>
              )}
            </div>

            {/* Read Button - Compact */}
            <button className={`w-full bg-purple-600 text-white py-2 rounded-md font-medium hover:bg-purple-700 transition-colors text-xs ${
              featured ? 'text-sm py-2.5' : ''
            }`}>
              Read Story
            </button>
          </div>
        </div>
      </div>
    </Link>
  )
}



// Story Map Section Component - Mobile-Optimized Timeline
function StoryMapSection({
  entries,
  writerName
}: {
  entries: DiaryEntry[]
  writerName: string
}) {
  return (
    <div className="bg-white rounded-2xl p-4 sm:p-6 shadow-lg border border-gray-100">
      <h3 className="text-lg sm:text-xl font-serif text-gray-800 mb-4 sm:mb-6 flex items-center gap-2">
        <span>🗺️</span>
        <span className="hidden sm:inline">Story Map - Navigate {writerName}'s Journey</span>
        <span className="sm:hidden">{writerName}'s Timeline</span>
      </h3>

      {/* Mobile Timeline View */}
      <div className="block sm:hidden space-y-3">
        {entries.slice(0, 8).map((entry, index) => (
          <Link
            key={entry.id}
            href={`/d/${entry.id}`}
            className="group flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100"
          >
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
              {entries.length - index}
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="text-sm font-medium text-gray-800 truncate group-hover:text-purple-600">
                {entry.title}
              </h4>
              <div className="flex items-center gap-2 mt-1">
                <p className="text-xs text-gray-500">
                  {formatDate(entry.created_at)}
                </p>
                {entry.is_free && (
                  <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">FREE</span>
                )}
                {(entry as any).is_pinned === true && (
                  <span className="text-yellow-500 text-xs">📌</span>
                )}
              </div>
            </div>
            <svg className="w-4 h-4 text-gray-400 group-hover:text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        ))}
      </div>

      {/* Desktop Grid View */}
      <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
        {entries.slice(0, 12).map((entry, index) => (
          <Link
            key={entry.id}
            href={`/d/${entry.id}`}
            className="group flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100"
          >
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
              {entries.length - index}
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="text-sm font-medium text-gray-800 truncate group-hover:text-purple-600">
                {entry.title}
              </h4>
              <p className="text-xs text-gray-500">
                {formatDate(entry.created_at)}
              </p>
            </div>
            {(entry as any).is_pinned === true && (
              <span className="text-yellow-500 text-xs">📌</span>
            )}
          </Link>
        ))}
      </div>

      {entries.length > 8 && (
        <div className="mt-4 text-center">
          <button className="text-sm text-purple-600 hover:text-purple-700 font-medium px-4 py-2 rounded-lg hover:bg-purple-50 transition-colors">
            View All {entries.length} Entries →
          </button>
        </div>
      )}
    </div>
  )
}

// Book Projects Section Component
function BookProjectsSection({
  projects,
  writerName
}: {
  projects: Project[]
  writerName: string
}) {
  if (projects.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📚</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Book Projects Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any book projects yet. Check back soon!
        </p>
      </div>
    )
  }

  return (
    <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4" style={{ gridAutoRows: '1fr' }}>
      {projects.map((project) => (
        <Link
          key={project.id}
          href={`/projects/${project.id}`}
          className="group block h-full"
        >
          <div className="bg-white rounded-lg sm:rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 group-hover:scale-[1.01] h-full flex flex-col">

            {/* Book Cover - Compact 3:4 ratio */}
            <div className="aspect-[3/4] bg-gradient-to-br from-blue-100 to-purple-100 relative overflow-hidden">
              {project.cover_image_url ? (
                <img
                  src={project.cover_image_url}
                  alt={project.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <span className="text-4xl opacity-50">📖</span>
                </div>
              )}

              {/* Status Badge */}
              {project.is_complete && (
                <div className="absolute top-1.5 right-1.5">
                  <span className="bg-green-500/90 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">
                    Complete
                  </span>
                </div>
              )}
            </div>

            {/* Project Info - Compact */}
            <div className="p-3 flex-1 flex flex-col">
              <h3 className="font-serif text-sm font-semibold text-gray-800 mb-1.5 line-clamp-1 group-hover:text-blue-600 transition-colors" title={project.title}>
                {project.title}
              </h3>

              {project.description && (
                <p className="text-xs text-gray-600 mb-auto line-clamp-2">
                  {project.description}
                </p>
              )}

              <div className="mt-2">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                  <span>{project.total_chapters} chapters</span>
                  <span>{project.total_words > 1000 ? `${Math.round(project.total_words/1000)}k` : project.total_words} words</span>
                </div>

                <div className="flex items-center justify-between">
                  {project.genre && (
                    <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded-full">
                      {project.genre}
                    </span>
                  )}

                  {project.price_amount && (
                    <div className="text-xs font-medium text-blue-600">
                      ${(project.price_amount / 100).toFixed(2)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
}
