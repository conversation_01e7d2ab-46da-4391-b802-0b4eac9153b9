interface StructuredDataProps {
  type: 'website' | 'article' | 'profile'
  data: {
    id?: string
    title?: string
    content?: string
    photos?: Array<{ url: string }>
    author?: { id?: string; name?: string; custom_url?: string }
    created_at?: string
    updated_at?: string
    name?: string
    bio?: string
    profile_picture_url?: string
    custom_url?: string
  }
}

export function StructuredData({ type, data }: StructuredDataProps) {
  const generateStructuredData = () => {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://onlydiary.app'
    
    switch (type) {
      case 'website':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "OnlyDiary",
          "description": "Create, share, and monetize your personal diary entries in a beautiful, intimate space designed for authentic storytelling.",
          "url": baseUrl,
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${baseUrl}/search?q={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "OnlyDiary",
            "url": baseUrl,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/logo.png`
            }
          }
        }
      
      case 'article':
        return {
          "@context": "https://schema.org",
          "@type": "Article",
          "headline": data.title,
          "description": data.content?.substring(0, 160) + "...",
          "image": data.photos?.[0]?.url || `${baseUrl}/og-image.jpg`,
          "author": {
            "@type": "Person",
            "name": data.author?.name || "OnlyDiary Writer",
            "url": data.author?.custom_url 
              ? `${baseUrl}/${data.author.custom_url}`
              : `${baseUrl}/u/${data.author?.id}`
          },
          "publisher": {
            "@type": "Organization",
            "name": "OnlyDiary",
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/logo.png`
            }
          },
          "datePublished": data.created_at,
          "dateModified": data.updated_at,
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `${baseUrl}/entry/${data.id}`
          }
        }
      
      case 'profile':
        return {
          "@context": "https://schema.org",
          "@type": "ProfilePage",
          "mainEntity": {
            "@type": "Person",
            "name": data.name,
            "description": data.bio,
            "image": data.profile_picture_url,
            "url": data.custom_url 
              ? `${baseUrl}/${data.custom_url}`
              : `${baseUrl}/u/${data.id}`,
            "knowsAbout": "Personal storytelling, diary writing, creative writing"
          }
        }
      
      default:
        return null
    }
  }

  const structuredData = generateStructuredData()

  if (!structuredData) return null

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
}
