"use client"

import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Link from "next/link"
import { Button } from "@/components/ui/button"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  book_type: string
  price_amount: number
  average_rating: number
  review_count: number
  sales_count: number
  is_ebook: boolean
  is_complete: boolean
  is_private: boolean
  created_at: string
  updated_at: string
}

interface BookManagerProps {
  book: Book
  onUpdate: () => void
}

export function BookManager({ book, onUpdate }: BookManagerProps) {
  const [loading, setLoading] = useState(false)
  const [showSendToStore, setShowSendToStore] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const supabase = createSupabaseClient()

  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const sendToStore = async () => {
    setLoading(true)
    try {
      const { error } = await supabase
        .from('projects')
        .update({
          is_ebook: true,
          is_complete: true,
          is_private: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', book.id)

      if (error) throw error

      setShowSendToStore(false)
      onUpdate()
    } catch (error) {
      console.error('Error sending book to store:', error)
    } finally {
      setLoading(false)
    }
  }

  const removeFromStore = async () => {
    setLoading(true)
    try {
      const { error } = await supabase
        .from('projects')
        .update({
          is_ebook: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', book.id)

      if (error) throw error

      onUpdate()
    } catch (error) {
      console.error('Error removing book from store:', error)
    } finally {
      setLoading(false)
    }
  }

  const togglePrivacy = async () => {
    setLoading(true)
    try {
      const { error } = await supabase
        .from('projects')
        .update({
          is_private: !book.is_private,
          updated_at: new Date().toISOString()
        })
        .eq('id', book.id)

      if (error) throw error

      onUpdate()
    } catch (error) {
      console.error('Error updating privacy:', error)
    } finally {
      setLoading(false)
    }
  }

  const deleteBook = async () => {
    setDeleting(true)
    try {
      // Delete related data first
      await supabase.from('chapters').delete().eq('project_id', book.id)
      await supabase.from('book_purchases').delete().eq('book_id', book.id)
      await supabase.from('book_reviews').delete().eq('book_id', book.id)

      // Delete the book itself
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', book.id)

      if (error) throw error

      setShowDeleteConfirm(false)
      onUpdate()
    } catch (error) {
      console.error('Error deleting book:', error)
    } finally {
      setDeleting(false)
    }
  }

  const isInStore = book.is_ebook && book.is_complete && !book.is_private

  return (
    <div className="border border-gray-100 rounded-2xl p-4 sm:p-5 bg-white shadow-sm hover:shadow-md transition-all duration-200">
      <div className="flex items-start gap-3 sm:gap-4">
        {/* Properly sized cover - no cropping, maintains aspect ratio */}
        {book.cover_image_url ? (
          <div className="w-16 h-24 sm:w-20 sm:h-32 flex-shrink-0">
            <img
              src={book.cover_image_url}
              alt={book.title}
              className="w-full h-full object-contain bg-white rounded-lg shadow-sm border border-gray-100"
            />
          </div>
        ) : (
          <div className="w-16 h-24 sm:w-20 sm:h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center flex-shrink-0 border border-gray-200">
            <span className="text-xl sm:text-3xl">📖</span>
          </div>
        )}

        <div className="flex-1 min-w-0">
          {/* Mobile-optimized header */}
          <div className="mb-3">
            <h3 className="text-lg sm:text-xl font-serif text-gray-900 leading-tight mb-2">{book.title}</h3>
            <div className="flex flex-wrap items-center gap-1.5">
              {isInStore && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border border-green-200">
                  📚 Live
                </span>
              )}
              {book.is_private && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-gray-100 to-slate-100 text-gray-700 border border-gray-200">
                  🔒 Private
                </span>
              )}
              {!book.is_complete && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-700 border border-yellow-200">
                  ✏️ Draft
                </span>
              )}
            </div>
          </div>

          {/* Mobile-optimized description */}
          {book.description && (
            <p className="text-xs sm:text-sm text-gray-600 mb-3 sm:mb-4 line-clamp-2 leading-relaxed">{book.description}</p>
          )}

          {/* Mobile-first stats grid */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 mb-3 sm:mb-4 p-3 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl border border-gray-100">
            <div className="text-center">
              <p className="text-sm sm:text-lg font-semibold text-gray-900">{formatPrice(book.price_amount)}</p>
              <p className="text-xs text-gray-500">Price</p>
            </div>
            <div className="text-center">
              <p className="text-sm sm:text-lg font-semibold text-gray-900">{book.sales_count || 0}</p>
              <p className="text-xs text-gray-500">Sales</p>
            </div>
            <div className="text-center">
              <p className="text-sm sm:text-lg font-semibold text-gray-900">
                {book.average_rating ? `${book.average_rating.toFixed(1)}` : '—'}
              </p>
              <p className="text-xs text-gray-500">Rating</p>
            </div>
            <div className="text-center">
              <p className="text-sm sm:text-lg font-semibold text-gray-900">{book.review_count || 0}</p>
              <p className="text-xs text-gray-500">Reviews</p>
            </div>
          </div>

          {/* Mobile-optimized action buttons */}
          <div className="space-y-2 sm:space-y-0 sm:flex sm:flex-wrap sm:gap-2">
            {/* Primary actions row */}
            <div className="flex gap-2 sm:contents">
              <Link href={`/write/projects/${book.id}`} className="flex-1 sm:flex-none">
                <Button size="sm" variant="outline" className="w-full sm:w-auto font-medium rounded-lg border-2 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200">
                  ✏️ Edit
                </Button>
              </Link>

              {isInStore ? (
                <Link href={`/books/${book.id}`} className="flex-1 sm:flex-none">
                  <Button size="sm" variant="outline" className="w-full sm:w-auto font-medium rounded-lg border-2 hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200">
                    👁️ View in Store
                  </Button>
                </Link>
              ) : (
                <Button
                  size="sm"
                  className="flex-1 sm:flex-none bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
                  onClick={() => setShowSendToStore(true)}
                  disabled={!book.is_complete}
                >
                  📚 Send to Store
                </Button>
              )}
            </div>

            {/* Secondary actions row */}
            <div className="flex gap-2 sm:contents">
              {isInStore && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={removeFromStore}
                  disabled={loading}
                  className="flex-1 sm:flex-none text-orange-600 border-2 border-orange-200 hover:bg-orange-50 rounded-lg transition-all duration-200"
                >
                  {loading ? '⏳ Removing...' : '📤 Remove'}
                </Button>
              )}

              <Button
                size="sm"
                variant="outline"
                onClick={togglePrivacy}
                disabled={loading}
                className="flex-1 sm:flex-none text-gray-600 border-2 border-gray-200 hover:bg-gray-50 rounded-lg transition-all duration-200"
              >
                {loading ? '⏳ Updating...' : (book.is_private ? '🔓 Public' : '🔒 Private')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Send to Store Confirmation Modal */}
      {showSendToStore && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Send Book to Store</h3>
            <p className="text-gray-600 mb-4">
              This will make your book available for purchase in the OnlyDiary Book Store. 
              You'll earn 80% of all sales revenue*.
            </p>
            <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
              <p className="text-sm text-green-800">
                <strong>💰 Author Royalties:</strong> You keep 80% of all sales*
              </p>
              <p className="text-xs text-green-600 mt-1">
                * After Stripe processing fees (2.9% + 30¢ per transaction)
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowSendToStore(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={sendToStore}
                disabled={loading}
                className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
              >
                {loading ? 'Sending...' : 'Send to Store'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
