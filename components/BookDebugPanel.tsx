"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, CheckCircle, RefreshCw, Bug, FileText } from "lucide-react"

interface BookDebugPanelProps {
  bookId: string
  onRepairComplete?: () => void
}

export function BookDebugPanel({ bookId, onRepairComplete }: BookDebugPanelProps) {
  const [debugData, setDebugData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [repairing, setRepairing] = useState(false)

  const fetchDebugInfo = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/debug/book-chapters/${bookId}`)
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch debug info')
      }
      
      setDebugData(data)
    } catch (error) {
      console.error('Error fetching debug info:', error)
      alert(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const repairChapters = async () => {
    setRepairing(true)
    try {
      const response = await fetch(`/api/repair-book-chapters/${bookId}`, {
        method: 'POST'
      })
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to repair chapters')
      }
      
      alert('Chapters repaired successfully! The e-reader should now show proper content.')
      
      // Refresh debug info
      await fetchDebugInfo()
      
      // Notify parent component
      onRepairComplete?.()
      
    } catch (error) {
      console.error('Error repairing chapters:', error)
      alert(`Repair failed: ${error.message}`)
    } finally {
      setRepairing(false)
    }
  }

  const hasDescriptionBug = debugData?.chapters?.some(chapter => 
    chapter.is_description_match
  )

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          Book Debug Panel
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={fetchDebugInfo} 
            disabled={loading}
            variant="outline"
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4 mr-2" />
                Check Book Status
              </>
            )}
          </Button>
          
          {debugData && hasDescriptionBug && (
            <Button 
              onClick={repairChapters} 
              disabled={repairing}
              variant="destructive"
            >
              {repairing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Repairing...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Fix Chapters
                </>
              )}
            </Button>
          )}
        </div>

        {debugData && (
          <div className="space-y-4">
            {/* Book Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Book Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div><strong>Title:</strong> {debugData.book.title}</div>
                  <div><strong>Processing Status:</strong> 
                    <Badge variant={debugData.book.processing_status === 'completed' ? 'default' : 'destructive'}>
                      {debugData.book.processing_status}
                    </Badge>
                  </div>
                  <div><strong>Is E-book:</strong> {debugData.book.is_ebook ? 'Yes' : 'No'}</div>
                  <div><strong>Total Chapters (Book):</strong> {debugData.book.total_chapters}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Database Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div><strong>Chapters in DB:</strong> {debugData.debug_info.total_chapters_in_db}</div>
                  <div><strong>Published Chapters:</strong> {debugData.debug_info.chapters_published}</div>
                  <div><strong>Processing Jobs:</strong> {debugData.processing_jobs.length}</div>
                </CardContent>
              </Card>
            </div>

            {/* Issue Detection */}
            {hasDescriptionBug && (
              <Card className="border-red-200 bg-red-50">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-red-700 flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    Issue Detected
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-red-700">
                    <strong>Problem:</strong> Some chapters contain the book description instead of actual chapter content. 
                    This is why the e-reader shows your synopsis instead of the first chapter.
                  </p>
                  <p className="text-red-600 mt-2">
                    <strong>Solution:</strong> Click "Fix Chapters" to re-process your EPUB file and extract the proper chapter content.
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Chapters Preview */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Chapters Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {debugData.chapters.map((chapter, index) => (
                    <div key={chapter.id} className="border rounded p-2 text-sm">
                      <div className="flex items-center justify-between mb-1">
                        <strong>Chapter {chapter.chapter_number}: {chapter.title}</strong>
                        <div className="flex gap-2">
                          <Badge variant={chapter.is_published ? 'default' : 'secondary'}>
                            {chapter.is_published ? 'Published' : 'Draft'}
                          </Badge>
                          {chapter.is_description_match && (
                            <Badge variant="destructive">Description Bug</Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-gray-600">
                        Length: {chapter.content_length} characters | Words: {chapter.word_count}
                      </div>
                      <div className="text-gray-500 text-xs mt-1">
                        Content preview: {chapter.content_preview}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Content Comparison */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Content Comparison</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <strong>Book Description starts with:</strong>
                  <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded mt-1">
                    {debugData.debug_info.book_description_starts_with}
                  </div>
                </div>
                <div>
                  <strong>First Chapter starts with:</strong>
                  <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded mt-1">
                    {debugData.debug_info.first_chapter_content_starts_with}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
