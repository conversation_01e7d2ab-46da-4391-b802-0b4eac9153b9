"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface CreditsDisplayProps {
  writerId: string
  writerName: string
}

export function CreditsDisplay({ writerId, writerName }: CreditsDisplayProps) {
  const [credits, setCredits] = useState<number | null>(null)
  const [loading, setLoading] = useState(true)
  
  const supabase = createSupabaseClient()

  useEffect(() => {
    async function loadCredits() {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user) {
          setLoading(false)
          return
        }

        const { data: creditData } = await supabase
          .from("post_credits")
          .select("credits_remaining")
          .eq("user_id", user.id)
          .eq("writer_id", writerId)
          .single()

        setCredits(creditData?.credits_remaining || 0)
      } catch (error) {
        console.error("Error loading credits:", error)
        setCredits(0)
      } finally {
        setLoading(false)
      }
    }

    loadCredits()
  }, [writerId, supabase])

  if (loading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-blue-200 rounded w-32 mb-2"></div>
          <div className="h-3 bg-blue-200 rounded w-48"></div>
        </div>
      </div>
    )
  }

  if (credits === null || credits === 0) {
    return null // Don't show anything if no credits
  }

  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-medium text-green-800">
            You have {credits} post credits
          </h4>
          <p className="text-sm text-green-600">
            for {writerName}&apos;s content
          </p>
        </div>
        <div className="text-2xl text-green-600">
          📚
        </div>
      </div>
      <p className="text-xs text-green-600 mt-2">
        You can read any {credits} posts from this writer in any order
      </p>
    </div>
  )
}
