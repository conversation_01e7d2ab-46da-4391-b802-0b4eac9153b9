"use client"

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface BlockedUser {
  id: string
  blocked_id: string
  users: {
    id: string
    name: string
    profile_picture_url?: string
  }
}

export function BlockedUsersManager() {
  const [blockedUsers, setBlockedUsers] = useState<BlockedUser[]>([])
  const [loading, setLoading] = useState(true)
  const [unblocking, setUnblocking] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    loadBlockedUsers()
  }, [])

  const loadBlockedUsers = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { data, error } = await supabase
        .from('blocks')
        .select(`
          id,
          blocked_id,
          users!blocks_blocked_id_fkey (
            id,
            name,
            profile_picture_url
          )
        `)
        .eq('blocker_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading blocked users:', error)
      } else {
        setBlockedUsers(data || [])
      }
    } catch (error) {
      console.error('Error loading blocked users:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUnblock = async (blockId: string, userName: string) => {
    if (!confirm(`Unblock ${userName}? You'll see their posts again.`)) {
      return
    }

    setUnblocking(blockId)
    try {
      const { error } = await supabase
        .from('blocks')
        .delete()
        .eq('id', blockId)

      if (error) {
        console.error('Error unblocking user:', error)
        alert('Failed to unblock user. Please try again.')
      } else {
        // Remove from local state
        setBlockedUsers(prev => prev.filter(block => block.id !== blockId))
      }
    } catch (error) {
      console.error('Error unblocking user:', error)
      alert('Failed to unblock user. Please try again.')
    } finally {
      setUnblocking(null)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Blocked Users</h3>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-12 bg-gray-200 rounded"></div>
            <div className="h-12 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Blocked Users</h3>
      
      {blockedUsers.length === 0 ? (
        <p className="text-gray-500 text-sm">You haven't blocked any users yet.</p>
      ) : (
        <div className="space-y-3">
          {blockedUsers.map((block) => (
            <div key={block.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  {block.users?.profile_picture_url ? (
                    <img
                      src={block.users.profile_picture_url}
                      alt={block.users.name || 'User'}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-sm font-serif text-gray-500">
                      {block.users?.name?.charAt(0).toUpperCase() || '?'}
                    </span>
                  )}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{block.users?.name || 'Unknown User'}</p>
                  <p className="text-xs text-gray-500">Blocked</p>
                </div>
              </div>
              
              <button
                onClick={() => handleUnblock(block.id, block.users?.name || 'Unknown User')}
                disabled={unblocking === block.id}
                className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 disabled:opacity-50"
              >
                {unblocking === block.id ? 'Unblocking...' : 'Unblock'}
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
