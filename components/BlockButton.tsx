"use client"

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface BlockButtonProps {
  userId: string
  userName: string
  onBlock?: () => void
}

export function BlockButton({ userId, userName, onBlock }: BlockButtonProps) {
  const [isBlocking, setIsBlocking] = useState(false)
  const [showConfirm, setShowConfirm] = useState(false)
  const supabase = createSupabaseClient()

  const handleBlock = async () => {
    if (!showConfirm) {
      setShowConfirm(true)
      return
    }

    setIsBlocking(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { error } = await supabase
        .from('blocks')
        .insert({
          blocker_id: user.id,
          blocked_id: userId
        })

      if (error) {
        console.error('Error blocking user:', error)
        alert('Failed to block user. Please try again.')
      } else {
        console.log('User blocked successfully')
        onBlock?.() // Callback to refresh timeline
        setShowConfirm(false)
      }
    } catch (error) {
      console.error('Error blocking user:', error)
      alert('Failed to block user. Please try again.')
    } finally {
      setIsBlocking(false)
    }
  }

  const handleCancel = () => {
    setShowConfirm(false)
  }

  if (showConfirm) {
    return (
      <div className="flex items-center gap-2">
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <p className="text-red-700 text-sm mb-2">
            Block <strong>{userName}</strong>? You won't see their posts anymore.
          </p>
          <div className="flex gap-2">
            <button
              onClick={handleBlock}
              disabled={isBlocking}
              className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 disabled:opacity-50"
            >
              {isBlocking ? 'Blocking...' : 'Yes, Block'}
            </button>
            <button
              onClick={handleCancel}
              className="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-400"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <button
      onClick={handleBlock}
      className="text-gray-400 hover:text-red-500 transition-colors p-1"
      title={`Block ${userName}`}
    >
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
      </svg>
    </button>
  )
}
