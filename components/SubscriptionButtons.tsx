"use client"

import { useState, useTransition } from "react"
import { Button } from "@/components/ui/button"

interface SubscriptionButtonsProps {
  writerName: string
  writerPrice: number
  writerId: string
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

export function SubscriptionButtons({ writerName, writerPrice, writerId }: SubscriptionButtonsProps) {
  const [isPending, startTransition] = useTransition()
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const handlePurchaseCredits = (credits: number = 30) => {
    startTransition(async () => {
      setError("")
      setSuccess("")

      try {
        const response = await fetch('/api/purchase-credits', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            writerId,
            credits
          })
        })

        const data = await response.json()

        if (!response.ok) {
          setError(data.error || 'Failed to purchase credits')
        } else {
          setSuccess(`Successfully purchased ${credits} post credits! You can now read any ${credits} posts from ${writerName}.`)
          // Refresh the page to update the UI
          setTimeout(() => {
            window.location.reload()
          }, 2000)
        }
      } catch {
        setError('An unexpected error occurred')
      }
    })
  }

  const creditPrice = formatPrice(writerPrice) // $9.99 for 30 posts
  const singlePostPrice = formatPrice(Math.floor(writerPrice / 30)) // ~$0.33 per post

  return (
    <div className="space-y-4">
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {success && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-600 text-sm">{success}</p>
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-3">
        <Button
          onClick={() => handlePurchaseCredits(30)}
          isLoading={isPending}
          className="bg-gray-800 text-white hover:bg-gray-700"
        >
          Buy 30 Posts for {creditPrice}
        </Button>

        <Button
          onClick={() => handlePurchaseCredits(10)}
          isLoading={isPending}
          variant="secondary"
          className="bg-gray-600 text-white hover:bg-gray-500"
        >
          Buy 10 Posts for {formatPrice(Math.floor(writerPrice / 3))}
        </Button>
      </div>

      <div className="text-xs text-gray-500 space-y-1">
        <p>• Read any posts in any order</p>
        <p>• Posts never expire</p>
        <p>• Approximately {singlePostPrice} per post</p>
        <p>• Support {writerName} directly</p>
      </div>
    </div>
  )
}
