"use client"

import { useEffect, useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { LinkButton } from "@/components/ui/link-button"

interface PostNavigationProps {
  currentEntryId: string
  writerId: string
  writerName: string
}

interface NavigationEntry {
  id: string
  title: string
  created_at: string
}

export function PostNavigation({ currentEntryId, writerId, writerName }: PostNavigationProps) {
  const [nextEntry, setNextEntry] = useState<NavigationEntry | null>(null)
  const [previousEntry, setPreviousEntry] = useState<NavigationEntry | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    loadNavigationEntries()
  }, [currentEntryId, writerId])

  const loadNavigationEntries = async () => {
    try {
      // Get next entry
      const { data: nextData } = await supabase
        .rpc('get_next_entry', {
          p_writer_id: writerId,
          p_current_entry_id: currentEntryId
        })

      // Get previous entry
      const { data: previousData } = await supabase
        .rpc('get_previous_entry', {
          p_writer_id: writerId,
          p_current_entry_id: currentEntryId
        })

      setNextEntry(nextData?.[0] || null)
      setPreviousEntry(previousData?.[0] || null)
    } catch (error) {
      console.error('Error loading navigation entries:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-between items-center py-6 border-t border-gray-200">
        <div className="w-32 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
        <div className="w-32 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
      </div>
    )
  }

  if (!nextEntry && !previousEntry) {
    return null
  }

  return (
    <div className="border-t border-gray-200 pt-6 mt-8">
      <div className="flex justify-between items-center">
        {/* Previous Entry */}
        <div className="flex-1">
          {previousEntry ? (
            <LinkButton
              href={`/d/${previousEntry.id}`}
              variant="outline"
              className="flex items-center gap-2 text-left max-w-xs"
            >
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <div className="min-w-0">
                <div className="text-xs text-gray-500 uppercase tracking-wide">Previous</div>
                <div className="font-medium truncate">{previousEntry.title}</div>
              </div>
            </LinkButton>
          ) : (
            <div className="text-gray-400 text-sm">
              ← First entry
            </div>
          )}
        </div>

        {/* Center - Writer Info */}
        <div className="text-center px-4">
          <div className="text-sm text-gray-500">Continue reading</div>
          <div className="font-medium text-gray-900">{writerName}</div>
        </div>

        {/* Next Entry */}
        <div className="flex-1 flex justify-end">
          {nextEntry ? (
            <LinkButton
              href={`/d/${nextEntry.id}`}
              variant="outline"
              className="flex items-center gap-2 text-right max-w-xs"
            >
              <div className="min-w-0">
                <div className="text-xs text-gray-500 uppercase tracking-wide">Next</div>
                <div className="font-medium truncate">{nextEntry.title}</div>
              </div>
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </LinkButton>
          ) : (
            <div className="text-gray-400 text-sm">
              Latest entry →
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Component for tracking reading position
export function ReadingPositionTracker({ 
  entryId, 
  writerId, 
  userId 
}: { 
  entryId: string
  writerId: string
  userId?: string 
}) {
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!userId || !entryId || !writerId) return

    // Update reading position when component mounts
    const updatePosition = async () => {
      try {
        await supabase.rpc('update_reading_position', {
          p_user_id: userId,
          p_writer_id: writerId,
          p_entry_id: entryId
        })
      } catch (error) {
        console.error('Error updating reading position:', error)
      }
    }

    // Delay to ensure user actually started reading
    const timer = setTimeout(updatePosition, 3000)

    return () => clearTimeout(timer)
  }, [entryId, writerId, userId, supabase])

  return null // This component doesn't render anything
}
