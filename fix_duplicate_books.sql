-- Fix duplicate book logic and add better awareness

-- 1. Add status column to track book states
ALTER TABLE projects ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'draft' 
CHECK (status IN ('draft', 'processing', 'published', 'archived'));

-- 2. Update existing books based on their current state
UPDATE projects 
SET status = CASE 
    WHEN is_ebook = true AND total_chapters > 0 AND is_complete = true THEN 'published'
    WHEN is_ebook = true AND total_chapters = 0 THEN 'processing'
    WHEN is_ebook = false AND total_chapters > 0 THEN 'published'
    ELSE 'draft'
END;

-- 3. Find and show duplicate books by title and author
SELECT 
    title,
    author_name,
    COUNT(*) as duplicate_count,
    STRING_AGG(id::text, ', ') as book_ids,
    STRING_AGG(status, ', ') as statuses,
    STRING_AGG(COALESCE(total_chapters::text, '0'), ', ') as chapter_counts
FROM projects 
WHERE is_ebook = true
GROUP BY title, author_name
HAVING COUNT(*) > 1
ORDER BY title;

-- 4. For "Broken Crayons Still Color" specifically - keep the one with chapters, remove the empty one
DELETE FROM projects 
WHERE title = 'Broken Crayons Still Color' 
AND is_ebook = true 
AND (total_chapters = 0 OR total_chapters IS NULL);

-- 5. Create unique constraint to prevent future duplicates
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_ebook_title_author 
ON projects (title, COALESCE(author_name, '')) 
WHERE is_ebook = true AND status != 'archived';

-- 6. Verify the cleanup worked
SELECT 
    title,
    author_name,
    status,
    total_chapters,
    total_words,
    is_complete,
    created_at
FROM projects 
WHERE is_ebook = true
ORDER BY title, created_at;
