// FIXED: Process the complete EPUB book "Broken Crayons Still Color" with real chapters
// Database schema is now fixed, this will extract all 32 real chapters from the EPUB
// Run this in your browser console while logged in as admin

async function processCompleteEbook() {
  const bookId = '62898999-f28b-4112-8954-9d7b516638c2';

  try {
    console.log('🔄 Processing EPUB with REAL chapters (not samples)');
    console.log('📚 Book ID:', bookId);

    // Use the fixed process-ebook API
    const response = await fetch('/api/process-ebook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId: bookId,
        fileUrl: 'https://inzwekkutrwkcynvbyyu.supabase.co/storage/v1/object/public/ebooks/1751430635086-broken-crayons-still-color.epub',
        fileType: 'epub'
      })
    });

    const result = await response.json();
    console.log('API Response:', result);

    if (response.ok) {
      console.log('✅ REAL EPUB CHAPTERS CREATED!', result);
      console.log(`📖 Created ${result.data?.chaptersCreated || 0} REAL chapters`);
      console.log(`📊 Total words: ${result.data?.totalWords || 0}`);
      console.log(`⏱️ Reading time: ${result.data?.readingTime || 0} minutes`);

      console.log('🎉 E-reader now has REAL content! Refreshing...');
      setTimeout(() => window.location.reload(), 1000);
    } else {
      console.error('❌ API Error:', result);
      console.log('🔧 Trying direct database approach...');

      // If API fails, show the user what to do
      console.log('📋 Manual fix needed:');
      console.log('1. Run the SQL to fix database schema');
      console.log('2. Check server logs for EPUB processing errors');
      console.log('3. Verify file permissions on Supabase storage');
    }
  } catch (error) {
    console.error('❌ Processing failed:', error);
    console.log('🔧 Check the database schema was fixed first');
  }
}

// Fallback repair function
async function repairBookChapters(bookId) {
  try {
    const response = await fetch(`/api/repair-book-chapters/${bookId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const result = await response.json();

    if (response.ok) {
      console.log('✅ Book chapters repaired successfully!', result);
      console.log(`Created ${result.data?.chaptersCreated || 0} chapters`);
      window.location.reload();
    } else {
      console.error('❌ Repair also failed:', result);
    }
  } catch (error) {
    console.error('❌ Repair error:', error);
  }
}

// Call the main function
processCompleteEbook();
