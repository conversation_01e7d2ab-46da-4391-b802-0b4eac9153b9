import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

Deno.serve(async (req) => {
  // This is needed if you're planning to invoke your function from a browser.
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    const { data: { user } } = await supabaseClient.auth.getUser()
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401,
      })
    }

    const { data: followed, error: followedError } = await supabaseClient
      .from('follows')
      .select('writer:writer_id (*)')
      .eq('follower_id', user.id)

    const { data: subscribed, error: subscribedError } = await supabaseClient
      .from('subscriptions')
      .select('writer:writer_id (*)')
      .eq('subscriber_id', user.id)

    if (followedError || subscribedError) {
      throw followedError || subscribedError
    }

    const { data: recommendations, error: recommendationsError } = await supabaseClient
      .from('favorite_creators')
      .select('writer_id')
      .eq('user_id', user.id)

    if (recommendationsError) {
      throw recommendationsError
    }

    const recommendedIds = new Set(recommendations?.map(r => r.writer_id) || [])

    const followedWriters = followed?.map(f => f.writer).filter(Boolean) || []
    const subscribedWriters = subscribed?.map(s => s.writer).filter(Boolean) || []

    const allWriters = [...followedWriters, ...subscribedWriters]
    const uniqueWriters = Array.from(new Map(allWriters.map(w => [w.id, w])).values())

    const creatorsWithRecommendationStatus = uniqueWriters.map(writer => ({
      ...writer,
      is_recommended: recommendedIds.has(writer.id),
    }))

    return new Response(JSON.stringify(creatorsWithRecommendationStatus), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    })
  }
})
