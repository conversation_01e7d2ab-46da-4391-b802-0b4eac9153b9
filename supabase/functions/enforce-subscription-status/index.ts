import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Starting subscription status enforcement...')

    // Find expired subscriptions
    const { data: expiredSubs, error: fetchError } = await supabaseClient
      .from('subscriptions')
      .select('*')
      .lt('active_until', new Date().toISOString())

    if (fetchError) {
      throw fetchError
    }

    console.log(`Found ${expiredSubs?.length || 0} expired subscriptions`)

    if (expiredSubs && expiredSubs.length > 0) {
      // Delete expired subscriptions
      const { error: deleteError } = await supabaseClient
        .from('subscriptions')
        .delete()
        .lt('active_until', new Date().toISOString())

      if (deleteError) {
        throw deleteError
      }

      console.log(`Cleaned up ${expiredSubs.length} expired subscriptions`)
    }

    // Check for entries that should be auto-hidden due to flags
    const { data: settings } = await supabaseClient
      .from('settings')
      .select('value')
      .eq('key', 'auto_hide_flag_threshold')
      .single()

    const flagThreshold = settings?.value ? parseInt(settings.value as string) : 5

    // Find entries with too many unresolved flags
    const { data: flaggedEntries, error: flagsError } = await supabaseClient
      .from('flags')
      .select(`
        diary_entry_id,
        count(*) as flag_count
      `)
      .eq('resolved', false)
      .group('diary_entry_id')
      .having('count(*) >= $1', [flagThreshold])

    if (flagsError) {
      throw flagsError
    }

    if (flaggedEntries && flaggedEntries.length > 0) {
      const entryIds = flaggedEntries.map(f => f.diary_entry_id)
      
      // Hide the flagged entries
      const { error: hideError } = await supabaseClient
        .from('diary_entries')
        .update({ is_hidden: true })
        .in('id', entryIds)

      if (hideError) {
        throw hideError
      }

      console.log(`Auto-hidden ${entryIds.length} entries due to excessive flags`)
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        expiredSubscriptions: expiredSubs?.length || 0,
        hiddenEntries: flaggedEntries?.length || 0
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in enforce-subscription-status:', error)
    
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})