import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { photoId, imageUrl } = await req.json()

    if (!photoId || !imageUrl) {
      throw new Error('Missing required parameters: photoId and imageUrl')
    }

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log(`Starting image moderation for photo ${photoId}`)

    // AWS Rekognition configuration
    const AWS_ACCESS_KEY = Deno.env.get('AWS_ACCESS_KEY_ID')
    const AWS_SECRET_KEY = Deno.env.get('AWS_SECRET_ACCESS_KEY')
    const AWS_REGION = Deno.env.get('AWS_REGION') || 'us-east-1'

    if (!AWS_ACCESS_KEY || !AWS_SECRET_KEY) {
      throw new Error('AWS credentials not configured')
    }

    // Download the image
    const imageResponse = await fetch(imageUrl)
    if (!imageResponse.ok) {
      throw new Error(`Failed to fetch image: ${imageResponse.statusText}`)
    }

    const imageBuffer = await imageResponse.arrayBuffer()
    const imageBytes = new Uint8Array(imageBuffer)

    // Prepare AWS Rekognition request
    const rekognitionEndpoint = `https://rekognition.${AWS_REGION}.amazonaws.com/`
    
    // Create AWS signature (simplified version - in production use proper AWS SDK)
    const payload = {
      Image: {
        Bytes: Array.from(imageBytes)
      },
      MinConfidence: 80 // Confidence threshold for moderation labels
    }

    // Call AWS Rekognition DetectModerationLabels
    // Note: This is a simplified implementation. In production, use AWS SDK for proper authentication
    const rekognitionResponse = await fetch(rekognitionEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-amz-json-1.1',
        'X-Amz-Target': 'RekognitionService.DetectModerationLabels',
        // Note: In production, add proper AWS signature headers
      },
      body: JSON.stringify(payload)
    })

    // For this demo, we'll simulate the moderation result
    // In a real implementation, you'd process the actual AWS Rekognition response
    const simulatedModerationResult = {
      ModerationLabels: [
        // Example of what AWS Rekognition might return
        // {
        //   Name: "Explicit Nudity",
        //   ParentName: "Nudity",
        //   Confidence: 99.5
        // }
      ]
    }

    console.log('Moderation analysis completed')

    // Analyze moderation results
    let moderationStatus = 'approved'
    const flaggedLabels = []
    
    // Define problematic categories that should be blocked
    const blockedCategories = [
      'Explicit Nudity',
      'Graphic Violence',
      'Hate Symbols',
      'Self Injury'
    ]

    // Define categories that should be flagged for review
    const flaggedCategories = [
      'Suggestive',
      'Partial Nudity',
      'Violence',
      'Weapons'
    ]

    for (const label of simulatedModerationResult.ModerationLabels) {
      if (blockedCategories.includes(label.Name)) {
        moderationStatus = 'rejected'
        flaggedLabels.push(label)
        break
      } else if (flaggedCategories.includes(label.Name)) {
        moderationStatus = 'flagged'
        flaggedLabels.push(label)
      }
    }

    // Update photo record with moderation results
    const { error: updateError } = await supabaseClient
      .from('photos')
      .update({
        moderation_status: moderationStatus,
        rekognition_labels: simulatedModerationResult.ModerationLabels
      })
      .eq('id', photoId)

    if (updateError) {
      throw updateError
    }

    console.log(`Photo ${photoId} moderation completed with status: ${moderationStatus}`)

    // If flagged for admin review, could send notification here
    if (moderationStatus === 'flagged') {
      console.log(`Photo ${photoId} flagged for admin review`)
      // TODO: Send notification to admin (email, Slack, etc.)
    }

    // If rejected, could notify the user
    if (moderationStatus === 'rejected') {
      console.log(`Photo ${photoId} rejected due to policy violation`)
      // TODO: Send notification to user about rejection
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        photoId,
        moderationStatus,
        flaggedLabels: flaggedLabels.map(l => l.Name),
        confidence: flaggedLabels.length > 0 ? Math.max(...flaggedLabels.map(l => l.Confidence)) : null
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in moderate-image:', error)
    
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})