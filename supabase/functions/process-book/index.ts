import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Book processing service optimized for the enhanced e-reader
class BookProcessor {
  private supabase: any
  
  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey)
  }

  async processBook(bookId: string, filePath: string, format: string) {
    try {
      // Create processing job record
      const { data: job } = await this.supabase
        .from('book_processing_jobs')
        .insert({
          book_id: bookId,
          status: 'extracting',
          format: format,
          started_at: new Date().toISOString()
        })
        .select()
        .single()

      // Download file from storage
      const { data: fileData } = await this.supabase.storage
        .from('book-uploads')
        .download(filePath)

      if (!fileData) throw new Error('Failed to download file')

      // Process based on format
      let chapters: any[] = []
      switch (format.toLowerCase()) {
        case 'pdf':
          chapters = await this.processPDF(fileData, bookId, job.id)
          break
        case 'epub':
          chapters = await this.processEPUB(fileData, bookId, job.id)
          break
        case 'txt':
          chapters = await this.processTXT(fileData, bookId, job.id)
          break
        default:
          throw new Error(`Unsupported format: ${format}`)
      }

      // Update job with total chapters
      await this.supabase
        .from('book_processing_jobs')
        .update({
          total_chapters: chapters.length,
          status: 'structuring'
        })
        .eq('id', job.id)

      // Process each chapter for e-reader optimization
      for (let i = 0; i < chapters.length; i++) {
        const chapter = chapters[i]
        
        // Create chapter record
        const { data: chapterRecord } = await this.supabase
          .from('chapters')
          .insert({
            project_id: bookId,
            title: chapter.title || `Chapter ${i + 1}`,
            content: chapter.content,
            chapter_number: i + 1,
            word_count: this.countWords(chapter.content),
            character_count: chapter.content.length,
            is_published: true
          })
          .select()
          .single()

        // Create text mapping for precise highlighting
        await this.createTextMapping(bookId, chapterRecord.id, chapter.content)

        // Update progress
        await this.supabase
          .from('book_processing_jobs')
          .update({
            processed_chapters: i + 1,
            status: i === chapters.length - 1 ? 'indexing' : 'structuring'
          })
          .eq('id', job.id)
      }

      // Final optimization step
      await this.supabase
        .from('book_processing_jobs')
        .update({
          status: 'optimizing'
        })
        .eq('id', job.id)

      // Create search indexes and optimize for reader
      await this.optimizeForReader(bookId)

      // Mark as completed
      await this.supabase
        .from('book_processing_jobs')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', job.id)

      await this.supabase
        .from('projects')
        .update({ processing_status: 'completed' })
        .eq('id', bookId)

      return { success: true, jobId: job.id, chaptersCreated: chapters.length }

    } catch (error) {
      console.error('Processing failed:', error)
      
      // Mark job as failed
      await this.supabase
        .from('book_processing_jobs')
        .update({
          status: 'failed',
          error_message: error.message,
          completed_at: new Date().toISOString()
        })
        .eq('book_id', bookId)

      throw error
    }
  }

  private async processTXT(fileData: Blob, bookId: string, jobId: string): Promise<any[]> {
    const text = await fileData.text()
    const chapters: any[] = []
    
    // Split by chapter markers or every ~5000 words
    const chapterMarkers = text.match(/^(Chapter|CHAPTER)\s+\d+/gm)
    
    if (chapterMarkers && chapterMarkers.length > 1) {
      // Split by detected chapters
      const parts = text.split(/^(Chapter|CHAPTER)\s+\d+/m)
      
      for (let i = 1; i < parts.length; i += 2) {
        const title = parts[i] + parts[i + 1]?.split('\n')[0] || ''
        const content = this.formatTextAsHTML(parts[i + 1] || '')
        
        chapters.push({
          title: title.trim(),
          content: content
        })
      }
    } else {
      // Split by word count (~5000 words per chapter)
      const words = text.split(/\s+/)
      const wordsPerChapter = 5000
      
      for (let i = 0; i < words.length; i += wordsPerChapter) {
        const chapterWords = words.slice(i, i + wordsPerChapter)
        const content = this.formatTextAsHTML(chapterWords.join(' '))
        
        chapters.push({
          title: `Chapter ${Math.floor(i / wordsPerChapter) + 1}`,
          content: content
        })
      }
    }
    
    return chapters
  }

  private formatTextAsHTML(text: string): string {
    // Convert plain text to structured HTML for e-reader
    return text
      .split('\n\n')
      .filter(para => para.trim())
      .map(para => `<p>${para.trim().replace(/\n/g, ' ')}</p>`)
      .join('\n')
  }

  private async createTextMapping(bookId: string, chapterId: string, content: string) {
    // Create character-level mapping for precise highlighting
    const textMap = this.indexTextContent(content)
    
    await this.supabase
      .from('chapter_text_maps')
      .insert({
        book_id: bookId,
        chapter_id: chapterId,
        text_mapping: textMap
      })
  }

  private indexTextContent(htmlContent: string): any {
    // Remove HTML tags for character counting
    const textContent = htmlContent.replace(/<[^>]*>/g, '')
    const paragraphs = htmlContent.split('</p>')
    
    let globalCharIndex = 0
    const mapping = []
    
    paragraphs.forEach((para, pIndex) => {
      if (!para.trim()) return
      
      const text = para.replace(/<[^>]*>/g, '').trim()
      const words = text.split(/\s+/).filter(w => w)
      
      let localCharIndex = 0
      const wordPositions = []
      
      words.forEach((word, wIndex) => {
        const startPos = globalCharIndex + localCharIndex
        const endPos = startPos + word.length
        
        wordPositions.push({
          word,
          startChar: startPos,
          endChar: endPos,
          wordIndex: wIndex
        })
        
        localCharIndex += word.length + 1 // +1 for space
      })
      
      mapping.push({
        paragraphIndex: pIndex,
        text,
        startChar: globalCharIndex,
        endChar: globalCharIndex + text.length,
        wordPositions
      })
      
      globalCharIndex += text.length + 1
    })
    
    return mapping
  }

  private async optimizeForReader(bookId: string) {
    // Create indexes for fast highlighting and commenting
    await this.supabase.rpc('create_book_indexes', { book_id: bookId })
    
    // Pre-calculate reading statistics
    const { data: chapters } = await this.supabase
      .from('chapters')
      .select('word_count')
      .eq('project_id', bookId)
    
    const totalWords = chapters?.reduce((sum, ch) => sum + ch.word_count, 0) || 0
    const estimatedPages = Math.ceil(totalWords / 250) // 250 words per page
    
    await this.supabase
      .from('projects')
      .update({
        total_chapters: chapters?.length || 0,
        estimated_pages: estimatedPages,
        total_words: totalWords
      })
      .eq('id', bookId)
  }

  private countWords(text: string): number {
    return text.replace(/<[^>]*>/g, '').split(/\s+/).filter(w => w.trim()).length
  }
}

serve(async (req) => {
  try {
    const { bookId, filePath, format, metadata } = await req.json()
    
    const processor = new BookProcessor(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    )
    
    const result = await processor.processBook(bookId, filePath, format)
    
    return new Response(JSON.stringify(result), {
      headers: { 'Content-Type': 'application/json' },
      status: 200
    })
    
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500
    })
  }
})
