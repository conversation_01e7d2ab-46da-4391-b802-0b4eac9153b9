-- Supabase cron job configuration
-- These should be executed in the Supabase dashboard SQL editor to set up scheduled functions

-- Schedule enforce-subscription-status to run every 15 minutes
SELECT cron.schedule(
  'enforce-subscription-status',
  '*/15 * * * *', -- Every 15 minutes
  $$
  SELECT net.http_post(
    url := 'https://your-project-ref.supabase.co/functions/v1/enforce-subscription-status',
    headers := '{"Content-Type": "application/json", "Authorization": "Bearer ' || current_setting('app.service_role_key') || '"}'::jsonb,
    body := '{}'::jsonb
  );
  $$
);

-- Schedule daily digest emails to run at 00:05 UTC every day
SELECT cron.schedule(
  'send-daily-digest',
  '5 0 * * *', -- 00:05 UTC daily
  $$
  SELECT net.http_post(
    url := 'https://your-project-ref.supabase.co/functions/v1/send-daily-digest',
    headers := '{"Content-Type": "application/json", "Authorization": "Bearer ' || current_setting('app.service_role_key') || '"}'::jsonb,
    body := '{}'::jsonb
  );
  $$
);

-- Schedule weekly backup to run at 03:00 UTC every Sunday
SELECT cron.schedule(
  'weekly-backup',
  '0 3 * * 0', -- 03:00 UTC every Sunday
  $$
  SELECT net.http_post(
    url := 'https://your-project-ref.supabase.co/functions/v1/weekly-backup',
    headers := '{"Content-Type": "application/json", "Authorization": "Bearer ' || current_setting('app.service_role_key') || '"}'::jsonb,
    body := '{}'::jsonb
  );
  $$
);

-- To view scheduled jobs:
-- SELECT * FROM cron.job;

-- To unschedule a job:
-- SELECT cron.unschedule('job-name');

-- To check job execution history:
-- SELECT * FROM cron.job_run_details ORDER BY start_time DESC LIMIT 10;