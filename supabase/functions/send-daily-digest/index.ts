import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Starting daily digest email sending...')

    // Get all active subscribers
    const { data: activeSubscriptions, error: subsError } = await supabaseClient
      .from('subscriptions')
      .select(`
        subscriber_id,
        writer_id,
        users!subscriptions_subscriber_id_fkey(email, name),
        writers:users!subscriptions_writer_id_fkey(name)
      `)
      .gt('active_until', new Date().toISOString())

    if (subsError) {
      throw subsError
    }

    console.log(`Found ${activeSubscriptions?.length || 0} active subscriptions`)

    if (!activeSubscriptions || activeSubscriptions.length === 0) {
      return new Response(
        JSON.stringify({ success: true, emailsSent: 0 }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Get new diary entries from the last 24 hours
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)

    const { data: newEntries, error: entriesError } = await supabaseClient
      .from('diary_entries')
      .select(`
        id,
        title,
        user_id,
        created_at,
        users(name)
      `)
      .gte('created_at', yesterday.toISOString())
      .eq('is_hidden', false)

    if (entriesError) {
      throw entriesError
    }

    console.log(`Found ${newEntries?.length || 0} new entries from last 24 hours`)

    // Group subscriptions by subscriber
    const subscriberGroups = new Map()
    
    activeSubscriptions.forEach(sub => {
      if (!subscriberGroups.has(sub.subscriber_id)) {
        subscriberGroups.set(sub.subscriber_id, {
          email: sub.users.email,
          name: sub.users.name,
          writers: []
        })
      }
      subscriberGroups.get(sub.subscriber_id).writers.push({
        id: sub.writer_id,
        name: sub.writers.name
      })
    })

    let emailsSent = 0

    // For each subscriber, check if any of their subscribed writers posted
    for (const [subscriberId, subscriberData] of subscriberGroups) {
      const relevantEntries = newEntries?.filter(entry => 
        subscriberData.writers.some(writer => writer.id === entry.user_id)
      ) || []

      if (relevantEntries.length > 0) {
        // In a real implementation, you would send actual emails here
        // For now, we'll just log what would be sent
        console.log(`Would send digest to ${subscriberData.email} with ${relevantEntries.length} new entries`)
        
        // You could integrate with services like:
        // - Resend (https://resend.com/)
        // - SendGrid
        // - AWS SES
        // - Mailgun
        
        // Example email content structure:
        const emailContent = {
          to: subscriberData.email,
          subject: `Your daily OnlyDiary digest - ${relevantEntries.length} new ${relevantEntries.length === 1 ? 'entry' : 'entries'}`,
          content: relevantEntries.map(entry => ({
            title: entry.title,
            author: entry.users?.name,
            url: `${Deno.env.get('SITE_URL')}/d/${entry.id}`,
            publishedAt: entry.created_at
          }))
        }
        
        // TODO: Implement actual email sending
        // await sendEmail(emailContent)
        
        emailsSent++
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        emailsSent,
        newEntries: newEntries?.length || 0,
        activeSubscriptions: activeSubscriptions.length
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in send-daily-digest:', error)
    
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})