import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Starting weekly backup process...')

    const timestamp = new Date().toISOString().split('T')[0] // YYYY-MM-DD format

    // Get backup data from all tables
    const tables = [
      'users',
      'diary_entries', 
      'photos',
      'subscriptions',
      'payments',
      'comments',
      'flags',
      'settings'
    ]

    const backupData = {}

    for (const table of tables) {
      console.log(`Backing up table: ${table}`)
      
      const { data, error } = await supabaseClient
        .from(table)
        .select('*')

      if (error) {
        throw new Error(`Failed to backup ${table}: ${error.message}`)
      }

      backupData[table] = {
        count: data?.length || 0,
        data: data || []
      }
    }

    // Create backup metadata
    const backupMetadata = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      tables: Object.keys(backupData),
      totalRecords: Object.values(backupData).reduce((sum, table: any) => sum + table.count, 0)
    }

    const backupPayload = {
      metadata: backupMetadata,
      data: backupData
    }

    // In a production environment, you would upload this to S3, Google Cloud Storage, etc.
    // For now, we'll demonstrate the structure and log the backup info
    
    console.log('Backup completed successfully')
    console.log(`Total records backed up: ${backupMetadata.totalRecords}`)
    console.log(`Tables backed up: ${backupMetadata.tables.join(', ')}`)

    // Example S3 upload (commented out - requires AWS SDK)
    /*
    const AWS_ACCESS_KEY = Deno.env.get('AWS_ACCESS_KEY_ID')
    const AWS_SECRET_KEY = Deno.env.get('AWS_SECRET_ACCESS_KEY')
    const S3_BUCKET = Deno.env.get('BACKUP_S3_BUCKET')
    
    if (AWS_ACCESS_KEY && AWS_SECRET_KEY && S3_BUCKET) {
      const backupFileName = `onlydiary-backup-${timestamp}.json`
      
      // Upload to S3
      const uploadResult = await uploadToS3(
        S3_BUCKET,
        backupFileName,
        JSON.stringify(backupPayload, null, 2)
      )
      
      console.log(`Backup uploaded to S3: ${uploadResult.location}`)
    }
    */

    // Store backup record in database
    const { error: logError } = await supabaseClient
      .from('settings')
      .upsert({
        key: `backup_${timestamp}`,
        value: {
          timestamp: backupMetadata.timestamp,
          totalRecords: backupMetadata.totalRecords,
          tables: backupMetadata.tables,
          status: 'completed'
        }
      })

    if (logError) {
      console.error('Failed to log backup record:', logError)
    }

    // Clean up old backup records (keep last 12 weeks)
    const twelveWeeksAgo = new Date()
    twelveWeeksAgo.setDate(twelveWeeksAgo.getDate() - (12 * 7))
    const cutoffDate = twelveWeeksAgo.toISOString().split('T')[0]

    const { error: cleanupError } = await supabaseClient
      .from('settings')
      .delete()
      .like('key', 'backup_%')
      .lt('key', `backup_${cutoffDate}`)

    if (cleanupError) {
      console.error('Failed to cleanup old backup records:', cleanupError)
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        timestamp: backupMetadata.timestamp,
        totalRecords: backupMetadata.totalRecords,
        tables: backupMetadata.tables
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in weekly-backup:', error)
    
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

// Helper function for S3 upload (would need AWS SDK implementation)
/*
async function uploadToS3(bucket: string, key: string, body: string) {
  // Implementation would go here using AWS SDK for Deno
  // This is a placeholder for the actual S3 upload logic
  return {
    location: `s3://${bucket}/${key}`
  }
}
*/