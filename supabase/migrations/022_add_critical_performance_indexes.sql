-- MINIMAL SAFE performance indexes for scaling
-- Only adds indexes for core tables that definitely exist
-- Zero risk of breaking anything

-- 1. Comments sorting by entry (most critical for UX)
CREATE INDEX IF NOT EXISTS idx_comments_entry_created 
ON comments(diary_entry_id, created_at DESC);

-- 2. Comments by user for profiles
CREATE INDEX IF NOT EXISTS idx_comments_user_created 
ON comments(user_id, created_at DESC);

-- 3. Diary entries by user for timelines
CREATE INDEX IF NOT EXISTS idx_diary_entries_user_timeline 
ON diary_entries(user_id, created_at DESC) 
WHERE is_hidden = false;

-- 4. Diary entries for public discovery
CREATE INDEX IF NOT EXISTS idx_diary_entries_public_timeline 
ON diary_entries(created_at DESC) 
WHERE is_hidden = false;

-- 5. Photos by diary entry
CREATE INDEX IF NOT EXISTS idx_photos_diary_entry 
ON photos(diary_entry_id);

-- That's it - just the absolutely essential indexes for core functionality
-- More indexes can be added later once we verify table structures