-- Safe RLS performance optimization - manually fix critical policies only
-- Replaces auth.uid() with (select auth.uid()) for better performance

-- Fix comments table policies (most critical)
DROP POLICY IF EXISTS "Users can comment on accessible entries" ON comments;
CREATE POLICY "Users can comment on accessible entries" ON comments
    FOR INSERT WITH CHECK (
        (select auth.uid()) = user_id
    );

DROP POLICY IF EXISTS "Users can update their own comments" ON comments;
CREATE POLICY "Users can update their own comments" ON comments
    FOR UPDATE USING ((select auth.uid()) = user_id)
    WITH CHECK ((select auth.uid()) = user_id);

-- Fix bookmarks table policies
DROP POLICY IF EXISTS "Users can manage bookmarks" ON bookmarks;
CREATE POLICY "Users can manage bookmarks" ON bookmarks
    FOR ALL USING (
        (select auth.uid()) = reader_id OR (select auth.uid()) = creator_id
    );

-- Fix follows table policies  
DROP POLICY IF EXISTS "Users can manage their follows" ON follows;
CREATE POLICY "Users can manage their follows" ON follows
    FOR ALL USING ((select auth.uid()) = follower_id);

-- Fix flowers table policies
DROP POLICY IF EXISTS "Users can manage flowers" ON flowers;
CREATE POLICY "Users can manage flowers" ON flowers
    FOR ALL USING ((select auth.uid()) = user_id);

-- Fix subscriptions table policies
DROP POLICY IF EXISTS "Users can view their subscriptions" ON subscriptions;
CREATE POLICY "Users can view their subscriptions" ON subscriptions
    FOR SELECT USING ((select auth.uid()) = subscriber_id);

DROP POLICY IF EXISTS "Users can manage their subscriptions" ON subscriptions;  
CREATE POLICY "Users can manage their subscriptions" ON subscriptions
    FOR INSERT WITH CHECK ((select auth.uid()) = subscriber_id);