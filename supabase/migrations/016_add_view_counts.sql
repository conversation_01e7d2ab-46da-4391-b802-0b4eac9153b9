-- Add view count to diary_entries table
ALTER TABLE diary_entries ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;

-- Function to update view count when a post is read
CREATE OR REPLACE FUNCTION update_view_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Increment view count for the diary entry
    UPDATE diary_entries
    SET view_count = view_count + 1
    WHERE id = NEW.diary_entry_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update view count when post_reads are inserted
DROP TRIGGER IF EXISTS update_view_count_on_read ON post_reads;
CREATE TRIGGER update_view_count_on_read
    AFTER INSERT ON post_reads
    FOR EACH ROW EXECUTE FUNCTION update_view_count();

-- Initialize view counts for existing entries based on current post_reads
UPDATE diary_entries 
SET view_count = (
    SELECT COUNT(*) 
    FROM post_reads 
    WHERE post_reads.diary_entry_id = diary_entries.id
)
WHERE view_count = 0;
