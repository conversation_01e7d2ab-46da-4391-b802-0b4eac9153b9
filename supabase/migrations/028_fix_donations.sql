-- Fix donations table to properly connect to content
-- Add missing fields and improve the donation system

-- Add missing fields to donations table if they don't exist
DO $$
BEGIN
    -- Add target type to clarify what the donation is for
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'donations' AND column_name = 'target_type'
    ) THEN
        ALTER TABLE donations ADD COLUMN target_type TEXT DEFAULT 'creator' 
            CHECK (target_type IN ('diary_entry', 'chapter', 'project', 'creator'));
    END IF;
    
    -- Add diary entry reference
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'donations' AND column_name = 'diary_entry_id'
    ) THEN
        ALTER TABLE donations ADD COLUMN diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE SET NULL;
    END IF;
    
    -- Add project reference
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'donations' AND column_name = 'project_id'
    ) THEN
        ALTER TABLE donations ADD COLUMN project_id UUID REFERENCES projects(id) ON DELETE SET NULL;
    END IF;
    
    -- Add chapter reference
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'donations' AND column_name = 'chapter_id'
    ) THEN
        ALTER TABLE donations ADD COLUMN chapter_id UUID REFERENCES chapters(id) ON DELETE SET NULL;
    END IF;
    
    -- Add stripe payment ID if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'donations' AND column_name = 'stripe_payment_id'
    ) THEN
        ALTER TABLE donations ADD COLUMN stripe_payment_id TEXT;
    END IF;
    
    -- Ensure we have recipient_id (might be called writer_id in some versions)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'donations' AND column_name = 'recipient_id'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'donations' AND column_name = 'writer_id'
    ) THEN
        ALTER TABLE donations RENAME COLUMN writer_id TO recipient_id;
    END IF;
    
    -- Add recipient_id if it doesn't exist at all
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'donations' AND column_name = 'recipient_id'
    ) THEN
        ALTER TABLE donations ADD COLUMN recipient_id UUID REFERENCES users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Update existing donations to have proper target_type if they're all 'creator' currently
UPDATE donations 
SET target_type = CASE 
    WHEN diary_entry_id IS NOT NULL THEN 'diary_entry'
    WHEN chapter_id IS NOT NULL THEN 'chapter'
    WHEN project_id IS NOT NULL THEN 'project'
    ELSE 'creator'
END
WHERE target_type = 'creator';

-- Create indexes for donations
CREATE INDEX IF NOT EXISTS idx_donations_donor_id ON donations(donor_id, created_at);
CREATE INDEX IF NOT EXISTS idx_donations_recipient_id ON donations(recipient_id, created_at);
CREATE INDEX IF NOT EXISTS idx_donations_diary_entry_id ON donations(diary_entry_id) WHERE diary_entry_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_donations_project_id ON donations(project_id) WHERE project_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_donations_chapter_id ON donations(chapter_id) WHERE chapter_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_donations_stripe_id ON donations(stripe_payment_id) WHERE stripe_payment_id IS NOT NULL;

-- Success message
SELECT 'Donation system improved successfully!' as status;
