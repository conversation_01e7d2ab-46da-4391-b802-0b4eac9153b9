-- Add comment notifications system

-- Add parent_comment_id for replies
ALTER TABLE comments ADD COLUMN parent_comment_id UUID REFERENCES comments(id) ON DELETE CASCADE;

-- Add index for replies
CREATE INDEX idx_comments_parent_id ON comments(parent_comment_id);

-- Function to send comment notifications
CREATE OR REPLACE FUNCTION notify_comment_created()
RETURNS TRIGGER AS $$
DECLARE
    entry_author_id UUID;
    entry_title TEXT;
    commenter_name TEXT;
    parent_comment_author_id UUID;
    parent_commenter_name TEXT;
BEGIN
    -- Get the diary entry author and title
    SELECT de.user_id, de.title INTO entry_author_id, entry_title
    FROM diary_entries de 
    WHERE de.id = NEW.diary_entry_id;
    
    -- Get commenter name
    SELECT name INTO commenter_name
    FROM users 
    WHERE id = NEW.user_id;
    
    -- If this is a reply to another comment
    IF NEW.parent_comment_id IS NOT NULL THEN
        -- Get the parent comment author
        SELECT c.user_id INTO parent_comment_author_id
        FROM comments c 
        WHERE c.id = NEW.parent_comment_id;
        
        -- Get parent commenter name
        SELECT name INTO parent_commenter_name
        FROM users 
        WHERE id = parent_comment_author_id;
        
        -- Notify the parent comment author (if different from current commenter)
        IF parent_comment_author_id != NEW.user_id THEN
            INSERT INTO notifications (user_id, type, title, body, data)
            VALUES (
                parent_comment_author_id,
                'reply',
                'New reply to your comment',
                commenter_name || ' replied to your comment',
                jsonb_build_object(
                    'comment_id', NEW.id,
                    'diary_entry_id', NEW.diary_entry_id,
                    'diary_title', entry_title,
                    'commenter_name', commenter_name,
                    'url', '/d/' || NEW.diary_entry_id
                )
            );
        END IF;
    END IF;
    
    -- Always notify the diary entry author (if different from commenter)
    IF entry_author_id != NEW.user_id THEN
        INSERT INTO notifications (user_id, type, title, body, data)
        VALUES (
            entry_author_id,
            'comment',
            'New comment on your diary',
            commenter_name || ' commented on "' || entry_title || '"',
            jsonb_build_object(
                'comment_id', NEW.id,
                'diary_entry_id', NEW.diary_entry_id,
                'diary_title', entry_title,
                'commenter_name', commenter_name,
                'url', '/d/' || NEW.diary_entry_id
            )
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to send notifications when comments are created
CREATE TRIGGER notify_comment_created_trigger
    AFTER INSERT ON comments
    FOR EACH ROW 
    EXECUTE FUNCTION notify_comment_created();

-- Function to send push notifications (called by external service)
CREATE OR REPLACE FUNCTION send_push_notification(
    notification_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    notification_record RECORD;
    subscription_record RECORD;
    push_payload JSONB;
BEGIN
    -- Get the notification details
    SELECT * INTO notification_record
    FROM notifications 
    WHERE id = notification_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Build push notification payload
    push_payload := jsonb_build_object(
        'title', notification_record.title,
        'body', notification_record.body,
        'data', notification_record.data,
        'tag', 'comment-' || notification_record.id,
        'requireInteraction', true
    );
    
    -- Get all push subscriptions for this user
    FOR subscription_record IN 
        SELECT subscription 
        FROM push_subscriptions 
        WHERE user_id = notification_record.user_id
    LOOP
        -- Here you would call your push notification service
        -- For now, we'll just log it
        RAISE NOTICE 'Would send push notification: % to subscription: %', 
            push_payload, subscription_record.subscription;
    END LOOP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to mark notifications as read
CREATE OR REPLACE FUNCTION mark_notification_read(notification_id UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE notifications 
    SET read_at = NOW()
    WHERE id = notification_id AND user_id = user_uuid AND read_at IS NULL;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to mark notifications as clicked
CREATE OR REPLACE FUNCTION mark_notification_clicked(notification_id UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE notifications 
    SET clicked_at = NOW()
    WHERE id = notification_id AND user_id = user_uuid AND clicked_at IS NULL;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Add RLS policy for notifications
CREATE POLICY "Users can mark their notifications as read/clicked" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);
