-- Helper function to get public writer data and entries for the writer page
CREATE OR REPLACE FUNCTION get_writer_public_data(writer_uuid UUID)
RETURNS TABLE(
    writer_id UUID,
    writer_name TEXT,
    writer_avatar TEXT,
    writer_bio TEXT,
    writer_price_monthly INTEGER,
    free_entry_id UUID,
    free_entry_title TEXT,
    free_entry_body_md TEXT,
    free_entry_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id as writer_id,
        u.name as writer_name,
        u.avatar as writer_avatar,
        u.bio as writer_bio,
        u.price_monthly as writer_price_monthly,
        de.id as free_entry_id,
        de.title as free_entry_title,
        de.body_md as free_entry_body_md,
        de.created_at as free_entry_created_at
    FROM users u
    LEFT JOIN diary_entries de ON (
        de.user_id = u.id 
        AND de.is_free = TRUE 
        AND NOT de.is_hidden
    )
    WHERE u.id = writer_uuid
    AND u.role IN ('writer', 'admin')
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get locked entries for a writer (teasers)
CREATE OR REPLACE FUNCTION get_writer_locked_entries(writer_uuid UUID)
RETURNS TABLE(
    entry_id UUID,
    title TEXT,
    body_teaser TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        de.id as entry_id,
        de.title,
        LEFT(de.body_md, 120) || '...' as body_teaser,
        de.created_at
    FROM diary_entries de
    WHERE de.user_id = writer_uuid
    AND NOT de.is_free
    AND NOT de.is_hidden
    ORDER BY de.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;