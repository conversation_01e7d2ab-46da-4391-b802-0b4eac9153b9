-- Create donations table to track donation details
CREATE TABLE donations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    donor_id UUID REFERENCES users(id) ON DELETE SET NULL,
    writer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    payment_id UUID REFERENCES payments(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL CHECK (amount >= 100), -- Minimum $1.00 in cents
    message TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_donations_donor_id ON donations(donor_id);
CREATE INDEX idx_donations_writer_id ON donations(writer_id);
CREATE INDEX idx_donations_payment_id ON donations(payment_id);
CREATE INDEX idx_donations_created_at ON donations(created_at DESC);

-- Enable RLS
ALTER TABLE donations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Donors can view their own donations
CREATE POLICY "Donors can view their own donations" ON donations
    FOR SELECT USING (auth.uid() = donor_id);

-- Writers can view donations they received
CREATE POLICY "Writers can view their donations" ON donations
    FOR SELECT USING (auth.uid() = writer_id);

-- System can insert donations (via webhooks)
CREATE POLICY "System can insert donations" ON donations
    FOR INSERT WITH CHECK (true);

-- Admins can view all donations
CREATE POLICY "Admins can view all donations" ON donations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
