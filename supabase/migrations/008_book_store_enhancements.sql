-- Book Store Enhancements Migration
-- Adds fields needed for the enhanced book store functionality

-- Add new fields to projects table for enhanced ebook functionality
ALTER TABLE projects ADD COLUMN IF NOT EXISTS is_ebook BOOLEAN DEFAULT FALSE;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS ebook_file_url TEXT;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS ebook_file_type TEXT CHECK (ebook_file_type IN ('pdf', 'epub'));
ALTER TABLE projects ADD COLUMN IF NOT EXISTS preview_chapters INTEGER DEFAULT 1;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS isbn TEXT;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS publication_date DATE;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS book_type TEXT;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS tags TEXT[];
ALTER TABLE projects ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,2) DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS review_count INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS sales_count INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS slug TEXT;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS meta_description TEXT;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS social_image_url TEXT;

-- Add unique constraint on slug (but allow nulls)
CREATE UNIQUE INDEX IF NOT EXISTS projects_slug_unique ON projects(slug) WHERE slug IS NOT NULL;

-- Create storage bucket for ebook files if it doesn't exist
INSERT INTO storage.buckets (id, name, public) 
VALUES ('ebooks', 'ebooks', true) 
ON CONFLICT (id) DO NOTHING;

-- Set up RLS policies for ebook storage
DO $$
BEGIN
    -- Check if policies exist before creating them
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'storage' 
        AND tablename = 'objects' 
        AND policyname = 'Anyone can view ebook files'
    ) THEN
        CREATE POLICY "Anyone can view ebook files" 
        ON storage.objects FOR SELECT 
        USING (bucket_id = 'ebooks');
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'storage' 
        AND tablename = 'objects' 
        AND policyname = 'Authenticated users can upload ebook files'
    ) THEN
        CREATE POLICY "Authenticated users can upload ebook files" 
        ON storage.objects FOR INSERT 
        WITH CHECK (bucket_id = 'ebooks' AND auth.role() = 'authenticated');
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'storage' 
        AND tablename = 'objects' 
        AND policyname = 'Users can update their own ebook files'
    ) THEN
        CREATE POLICY "Users can update their own ebook files" 
        ON storage.objects FOR UPDATE 
        USING (bucket_id = 'ebooks' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'storage' 
        AND tablename = 'objects' 
        AND policyname = 'Users can delete their own ebook files'
    ) THEN
        CREATE POLICY "Users can delete their own ebook files" 
        ON storage.objects FOR DELETE 
        USING (bucket_id = 'ebooks' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;
END $$;

-- Update existing complete projects to be ebooks if they have a price
UPDATE projects 
SET is_ebook = true, 
    book_type = COALESCE(genre, 'fiction'),
    slug = LOWER(REGEXP_REPLACE(REGEXP_REPLACE(title, '[^a-zA-Z0-9\s]', '', 'g'), '\s+', '-', 'g'))
WHERE is_complete = true 
  AND price_amount IS NOT NULL 
  AND is_ebook IS NOT TRUE;

-- Create book_reviews table if it doesn't exist
CREATE TABLE IF NOT EXISTS book_reviews (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 10),
    review_text TEXT,
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, user_id)
);

-- Create book_purchases table if it doesn't exist  
CREATE TABLE IF NOT EXISTS book_purchases (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    stripe_payment_intent_id TEXT,
    amount_paid INTEGER, -- in cents
    currency TEXT DEFAULT 'usd',
    status TEXT DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, user_id)
);

-- Enable RLS on new tables
ALTER TABLE book_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_purchases ENABLE ROW LEVEL SECURITY;

-- RLS policies for book_reviews
CREATE POLICY "Anyone can view reviews" ON book_reviews FOR SELECT USING (true);
CREATE POLICY "Users can create reviews" ON book_reviews FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own reviews" ON book_reviews FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own reviews" ON book_reviews FOR DELETE USING (auth.uid() = user_id);

-- RLS policies for book_purchases  
CREATE POLICY "Users can view their own purchases" ON book_purchases FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own purchases" ON book_purchases FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to update project ratings when reviews change
CREATE OR REPLACE FUNCTION update_project_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE projects 
    SET 
        average_rating = (
            SELECT COALESCE(AVG(rating), 0) 
            FROM book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        ),
        review_count = (
            SELECT COUNT(*) 
            FROM book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update ratings when reviews change
DROP TRIGGER IF EXISTS update_project_rating_trigger ON book_reviews;
CREATE TRIGGER update_project_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON book_reviews
    FOR EACH ROW EXECUTE FUNCTION update_project_rating();

-- Function to update sales count when purchases change
CREATE OR REPLACE FUNCTION update_project_sales()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE projects 
    SET sales_count = (
        SELECT COUNT(*) 
        FROM book_purchases 
        WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
          AND status = 'completed'
    )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update sales count when purchases change
DROP TRIGGER IF EXISTS update_project_sales_trigger ON book_purchases;
CREATE TRIGGER update_project_sales_trigger
    AFTER INSERT OR UPDATE OR DELETE ON book_purchases
    FOR EACH ROW EXECUTE FUNCTION update_project_sales();
