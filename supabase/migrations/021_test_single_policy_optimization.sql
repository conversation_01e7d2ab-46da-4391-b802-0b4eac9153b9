-- SAFE SMART OPTIMIZATION: Only optimize policies that actually need it
-- This prevents double-wrapping already optimized auth calls

DO $$
DECLARE
    policy_record RECORD;
    optimized_qual TEXT;
    optimized_with_check TEXT;
    policy_cmd TEXT;
    policies_optimized INTEGER := 0;
BEGIN
    RAISE NOTICE 'Starting smart RLS optimization (avoiding double-wrapping)...';
    
    -- Only process policies that have bare auth.uid() calls (not already wrapped)
    FOR policy_record IN
        SELECT 
            schemaname,
            tablename, 
            policyname,
            cmd,
            qual,
            with_check
        FROM pg_policies 
        WHERE schemaname = 'public'
        AND (
            -- Find bare auth calls that are NOT already wrapped
            (qual ~ 'auth\.uid\(\)' AND qual !~ '\(select auth\.uid\(\)\)') OR
            (qual ~ 'auth\.role\(\)' AND qual !~ '\(select auth\.role\(\)\)') OR
            (with_check ~ 'auth\.uid\(\)' AND with_check !~ '\(select auth\.uid\(\)\)') OR
            (with_check ~ 'auth\.role\(\)' AND with_check !~ '\(select auth\.role\(\)\)')
        )
        ORDER BY tablename, policyname
    LOOP
        RAISE NOTICE 'Optimizing policy "%" on table "%"', policy_record.policyname, policy_record.tablename;
        
        -- Smart replacement: only replace bare calls, not already wrapped ones
        optimized_qual := policy_record.qual;
        IF optimized_qual IS NOT NULL THEN
            -- Only replace if not already wrapped
            IF optimized_qual ~ 'auth\.uid\(\)' AND optimized_qual !~ '\(select auth\.uid\(\)\)' THEN
                optimized_qual := regexp_replace(optimized_qual, 'auth\.uid\(\)', '(select auth.uid())', 'g');
            END IF;
            IF optimized_qual ~ 'auth\.role\(\)' AND optimized_qual !~ '\(select auth\.role\(\)\)' THEN
                optimized_qual := regexp_replace(optimized_qual, 'auth\.role\(\)', '(select auth.role())', 'g');
            END IF;
        END IF;
        
        optimized_with_check := policy_record.with_check;
        IF optimized_with_check IS NOT NULL THEN
            -- Only replace if not already wrapped
            IF optimized_with_check ~ 'auth\.uid\(\)' AND optimized_with_check !~ '\(select auth\.uid\(\)\)' THEN
                optimized_with_check := regexp_replace(optimized_with_check, 'auth\.uid\(\)', '(select auth.uid())', 'g');
            END IF;
            IF optimized_with_check ~ 'auth\.role\(\)' AND optimized_with_check !~ '\(select auth\.role\(\)\)' THEN
                optimized_with_check := regexp_replace(optimized_with_check, 'auth\.role\(\)', '(select auth.role())', 'g');
            END IF;
        END IF;
        
        -- Determine policy command type
        policy_cmd := CASE 
            WHEN policy_record.cmd = 'r' THEN 'SELECT'
            WHEN policy_record.cmd = 'a' THEN 'INSERT' 
            WHEN policy_record.cmd = 'w' THEN 'UPDATE'
            WHEN policy_record.cmd = 'd' THEN 'DELETE'
            WHEN policy_record.cmd = '*' THEN 'ALL'
            ELSE 'ALL'
        END;
        
        BEGIN
            -- Drop and recreate policy
            EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', 
                policy_record.policyname, 
                policy_record.schemaname, 
                policy_record.tablename
            );
            
            IF optimized_with_check IS NOT NULL AND optimized_with_check != '' THEN
                EXECUTE format(
                    'CREATE POLICY %I ON %I.%I FOR %s USING (%s) WITH CHECK (%s)',
                    policy_record.policyname,
                    policy_record.schemaname,
                    policy_record.tablename,
                    policy_cmd,
                    optimized_qual,
                    optimized_with_check
                );
            ELSE
                EXECUTE format(
                    'CREATE POLICY %I ON %I.%I FOR %s USING (%s)',
                    policy_record.policyname,
                    policy_record.schemaname,
                    policy_record.tablename,
                    policy_cmd,
                    optimized_qual
                );
            END IF;
            
            policies_optimized := policies_optimized + 1;
            RAISE NOTICE 'Successfully optimized policy "%" on table "%"', policy_record.policyname, policy_record.tablename;
            
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Failed to optimize policy "%" on table "%": %', 
                policy_record.policyname, policy_record.tablename, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE 'Smart RLS optimization complete. Policies optimized: %', policies_optimized;
END $$;