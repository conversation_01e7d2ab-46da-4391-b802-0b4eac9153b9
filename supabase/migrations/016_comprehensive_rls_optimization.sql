-- COMPREHENSIVE RLS OPTIMIZATION MIGRATION
-- This migration systematically fixes ALL RLS performance issues:
-- 1. Auth function re-evaluation (auth.uid() -> (select auth.uid()))
-- 2. Multiple permissive policies (consolidate overlapping policies)

-- =============================================================================
-- PART 1: FIX AUTH FUNCTION RE-EVALUATION ISSUES
-- =============================================================================

-- Get all tables with RLS enabled and fix their policies systematically
DO $$
DECLARE
    table_record RECORD;
    policy_record RECORD;
BEGIN
    -- Loop through all tables that have RLS enabled
    FOR table_record IN 
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename IN (
            SELECT tablename 
            FROM pg_policies 
            WHERE schemaname = 'public'
        )
    LOOP
        RAISE NOTICE 'Processing table: %', table_record.tablename;
        
        -- Loop through all policies for this table
        FOR policy_record IN
            SELECT policyname, cmd, qual, with_check
            FROM pg_policies 
            WHERE schemaname = 'public' 
            AND tablename = table_record.tablename
        LOOP
            -- Drop and recreate each policy with optimized auth calls
            EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', 
                policy_record.policyname, 
                table_record.schemaname, 
                table_record.tablename
            );
            
            -- Recreate policy with optimized auth calls
            -- Replace auth.uid() with (select auth.uid()) in the policy definition
            DECLARE
                optimized_qual TEXT;
                optimized_with_check TEXT;
                policy_cmd TEXT;
            BEGIN
                -- Optimize the USING clause
                optimized_qual := REPLACE(
                    REPLACE(policy_record.qual, 'auth.uid()', '(select auth.uid())'),
                    'auth.role()', '(select auth.role())'
                );
                
                -- Optimize the WITH CHECK clause
                optimized_with_check := REPLACE(
                    REPLACE(policy_record.with_check, 'auth.uid()', '(select auth.uid())'),
                    'auth.role()', '(select auth.role())'
                );
                
                -- Determine policy command
                policy_cmd := CASE 
                    WHEN policy_record.cmd = 'r' THEN 'SELECT'
                    WHEN policy_record.cmd = 'a' THEN 'INSERT' 
                    WHEN policy_record.cmd = 'w' THEN 'UPDATE'
                    WHEN policy_record.cmd = 'd' THEN 'DELETE'
                    WHEN policy_record.cmd = '*' THEN 'ALL'
                    ELSE 'ALL'
                END;
                
                -- Recreate the policy
                IF optimized_with_check IS NOT NULL AND optimized_with_check != '' THEN
                    EXECUTE format(
                        'CREATE POLICY %I ON %I.%I FOR %s USING (%s) WITH CHECK (%s)',
                        policy_record.policyname,
                        table_record.schemaname,
                        table_record.tablename,
                        policy_cmd,
                        optimized_qual,
                        optimized_with_check
                    );
                ELSE
                    EXECUTE format(
                        'CREATE POLICY %I ON %I.%I FOR %s USING (%s)',
                        policy_record.policyname,
                        table_record.schemaname,
                        table_record.tablename,
                        policy_cmd,
                        optimized_qual
                    );
                END IF;
                
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Error processing policy % on table %: %', 
                    policy_record.policyname, table_record.tablename, SQLERRM;
                CONTINUE;
            END;
        END LOOP;
    END LOOP;
END $$;

-- =============================================================================
-- PART 2: FIX MULTIPLE PERMISSIVE POLICIES ISSUES
-- =============================================================================

-- Consolidate multiple permissive policies into single optimized policies
DO $$
DECLARE
    table_record RECORD;
    policy_count INTEGER;
BEGIN
    -- Check each table for multiple permissive policies
    FOR table_record IN
        SELECT schemaname, tablename, cmd, COUNT(*) as policy_count
        FROM pg_policies
        WHERE schemaname = 'public'
        AND permissive = true  -- Only permissive policies
        GROUP BY schemaname, tablename, cmd
        HAVING COUNT(*) > 1
    LOOP
        RAISE NOTICE 'Consolidating % permissive policies for % on table %',
            table_record.policy_count,
            CASE
                WHEN table_record.cmd = 'r' THEN 'SELECT'
                WHEN table_record.cmd = 'a' THEN 'INSERT'
                WHEN table_record.cmd = 'w' THEN 'UPDATE'
                WHEN table_record.cmd = 'd' THEN 'DELETE'
                WHEN table_record.cmd = '*' THEN 'ALL'
            END,
            table_record.tablename;

        -- For each table with multiple permissive policies, create consolidated policies
        -- This is a simplified approach - you may need to customize based on your specific policies

        -- Example consolidation for common patterns:
        IF table_record.tablename = 'bookmarks' THEN
            -- Drop all existing bookmark policies
            DROP POLICY IF EXISTS "Creators can view their bookmarks" ON bookmarks;
            DROP POLICY IF EXISTS "Readers can manage their bookmarks" ON bookmarks;
            DROP POLICY IF EXISTS "Users can manage bookmarks" ON bookmarks;

            -- Create single consolidated policy
            CREATE POLICY "Users can manage bookmarks" ON bookmarks
                FOR ALL USING (
                    (select auth.uid()) = reader_id OR (select auth.uid()) = creator_id
                );
        END IF;

        -- Add more table-specific consolidations as needed
        -- You can extend this pattern for other tables with multiple permissive policies

    END LOOP;
END $$;
