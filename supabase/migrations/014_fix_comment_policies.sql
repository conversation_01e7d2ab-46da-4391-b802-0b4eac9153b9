-- Fix comment policies to allow commenting on free entries and accessible content
-- This uses the same logic as the get_entry_preview function

DROP POLICY IF EXISTS "Subscribers can comment on entries they're subscribed to" ON comments;

-- Create a function to check if user can comment (same logic as reading)
CREATE OR REPLACE FUNCTION user_can_comment_on_entry(
    user_uuid UUID,
    entry_uuid UUID
) RETURNS BOOLEAN AS $$
DECLARE
    entry_record RECORD;
BEGIN
    -- Get entry details
    SELECT user_id, is_free, is_hidden 
    INTO entry_record
    FROM diary_entries 
    WHERE id = entry_uuid;
    
    -- Entry doesn't exist or is hidden
    IF NOT FOUND OR entry_record.is_hidden THEN
        RETURN FALSE;
    END IF;
    
    -- Entry is free - anyone can comment
    IF entry_record.is_free THEN
        RETURN TRUE;
    END IF;
    
    -- User is the writer - can always comment on own posts
    IF user_uuid = entry_record.user_id THEN
        RETURN TRUE;
    END IF;
    
    -- Check if user has credits for this writer
    IF EXISTS(
        SELECT 1 FROM post_credits pc 
        WHERE pc.user_id = user_uuid 
        AND pc.writer_id = entry_record.user_id 
        AND pc.credits_remaining > 0
    ) THEN
        RETURN TRUE;
    END IF;
    
    -- Check if user has already read this post
    IF EXISTS(
        SELECT 1 FROM post_reads pr 
        WHERE pr.user_id = user_uuid 
        AND pr.diary_entry_id = entry_uuid
    ) THEN
        RETURN TRUE;
    END IF;
    
    -- Default: cannot comment
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Updated policy using the function
CREATE POLICY "Users can comment on accessible entries" ON comments
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        user_can_comment_on_entry(auth.uid(), diary_entry_id)
    );