-- Drop the old subscriptions table and create a new post credits system
DROP TABLE IF EXISTS subscriptions CASCAD<PERSON>;

-- Create post_credits table to track user's available post credits
CREATE TABLE post_credits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    writer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    credits_remaining INTEGER NOT NULL DEFAULT 0,
    total_credits_purchased INTEGER NOT NULL DEFAULT 0,
    last_purchase_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one record per user-writer pair
    UNIQUE(user_id, writer_id)
);

-- Create post_reads table to track which posts users have read
CREATE TABLE post_reads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    writer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one read record per user-post pair
    UNIQUE(user_id, diary_entry_id)
);

-- Update payments table to track credit purchases
ALTER TABLE payments 
ADD COLUMN credits_purchased INTEGER DEFAULT 0,
ADD COLUMN credits_used_for_entry_id UUID REFERENCES diary_entries(id);

-- Add RLS policies for post_credits
ALTER TABLE post_credits ENABLE ROW LEVEL SECURITY;

-- Users can view their own credits
CREATE POLICY "Users can view their own credits" ON post_credits
    FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own credits (for purchasing)
CREATE POLICY "Users can manage their own credits" ON post_credits
    FOR ALL USING (auth.uid() = user_id);

-- Writers can view credits for their content
CREATE POLICY "Writers can view their credits" ON post_credits
    FOR SELECT USING (auth.uid() = writer_id);

-- Add RLS policies for post_reads
ALTER TABLE post_reads ENABLE ROW LEVEL SECURITY;

-- Users can view their own reads
CREATE POLICY "Users can view their own reads" ON post_reads
    FOR SELECT USING (auth.uid() = user_id);

-- Users can create read records
CREATE POLICY "Users can create read records" ON post_reads
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Writers can view reads of their content
CREATE POLICY "Writers can view reads of their content" ON post_reads
    FOR SELECT USING (auth.uid() = writer_id);

-- Add indexes for performance
CREATE INDEX idx_post_credits_user_writer ON post_credits(user_id, writer_id);
CREATE INDEX idx_post_reads_user_entry ON post_reads(user_id, diary_entry_id);
CREATE INDEX idx_post_reads_writer ON post_reads(writer_id);
CREATE INDEX idx_payments_credits ON payments(credits_purchased) WHERE credits_purchased > 0;

-- Add triggers to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_post_credits_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_post_credits_updated_at_trigger
    BEFORE UPDATE ON post_credits
    FOR EACH ROW EXECUTE FUNCTION update_post_credits_updated_at();

-- Function to check if user can read a post
CREATE OR REPLACE FUNCTION user_can_read_post(
    reader_uuid UUID,
    entry_uuid UUID
) RETURNS BOOLEAN AS $$
DECLARE
    entry_writer_id UUID;
    entry_is_free BOOLEAN;
    user_credits INTEGER;
    already_read BOOLEAN;
BEGIN
    -- Get entry details
    SELECT user_id, is_free INTO entry_writer_id, entry_is_free
    FROM diary_entries 
    WHERE id = entry_uuid;
    
    -- If entry is free, anyone can read
    IF entry_is_free THEN
        RETURN TRUE;
    END IF;
    
    -- If user is the writer, they can read their own posts
    IF reader_uuid = entry_writer_id THEN
        RETURN TRUE;
    END IF;
    
    -- Check if user has already read this post
    SELECT EXISTS(
        SELECT 1 FROM post_reads 
        WHERE user_id = reader_uuid AND diary_entry_id = entry_uuid
    ) INTO already_read;
    
    -- If already read, they can read again
    IF already_read THEN
        RETURN TRUE;
    END IF;
    
    -- Check if user has credits for this writer
    SELECT COALESCE(credits_remaining, 0) INTO user_credits
    FROM post_credits 
    WHERE user_id = reader_uuid AND writer_id = entry_writer_id;
    
    -- Return true if user has credits
    RETURN user_credits > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to consume a credit when reading a post
CREATE OR REPLACE FUNCTION consume_credit_for_post(
    reader_uuid UUID,
    entry_uuid UUID
) RETURNS BOOLEAN AS $$
DECLARE
    entry_writer_id UUID;
    entry_is_free BOOLEAN;
    user_credits INTEGER;
    already_read BOOLEAN;
BEGIN
    -- Get entry details
    SELECT user_id, is_free INTO entry_writer_id, entry_is_free
    FROM diary_entries 
    WHERE id = entry_uuid;
    
    -- If entry is free or user is writer, no credit needed
    IF entry_is_free OR reader_uuid = entry_writer_id THEN
        RETURN TRUE;
    END IF;
    
    -- Check if already read
    SELECT EXISTS(
        SELECT 1 FROM post_reads 
        WHERE user_id = reader_uuid AND diary_entry_id = entry_uuid
    ) INTO already_read;
    
    -- If already read, no credit needed
    IF already_read THEN
        RETURN TRUE;
    END IF;
    
    -- Check and consume credit
    UPDATE post_credits 
    SET credits_remaining = credits_remaining - 1,
        updated_at = NOW()
    WHERE user_id = reader_uuid 
      AND writer_id = entry_writer_id 
      AND credits_remaining > 0;
    
    -- Check if update was successful
    IF FOUND THEN
        -- Record the read
        INSERT INTO post_reads (user_id, writer_id, diary_entry_id)
        VALUES (reader_uuid, entry_writer_id, entry_uuid)
        ON CONFLICT (user_id, diary_entry_id) DO NOTHING;
        
        RETURN TRUE;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
