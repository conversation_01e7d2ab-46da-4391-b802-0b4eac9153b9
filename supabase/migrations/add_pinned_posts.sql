-- Add pinned field to diary_entries table
ALTER TABLE diary_entries ADD COLUMN is_pinned BOOLEAN DEFAULT FALSE;

-- Add index for pinned posts
CREATE INDEX idx_diary_entries_pinned ON diary_entries(writer_id, is_pinned, created_at DESC);

-- Add reading position tracking table
CREATE TABLE reading_positions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  writer_id uuid REFERENCES users(id) ON DELETE CASCADE,
  last_read_entry_id uuid REFERENCES diary_entries(id) ON DELETE CASCADE,
  last_read_at timestamp DEFAULT now(),
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now(),
  
  -- Ensure one reading position per user per writer
  UNIQUE(user_id, writer_id)
);

-- Enable RLS
ALTER TABLE reading_positions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for reading positions
CREATE POLICY "Users can view their own reading positions"
ON reading_positions
FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own reading positions"
ON reading_positions
FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own reading positions"
ON reading_positions
FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own reading positions"
ON reading_positions
FOR DELETE
USING (user_id = auth.uid());

-- Add indexes for performance
CREATE INDEX idx_reading_positions_user_id ON reading_positions(user_id);
CREATE INDEX idx_reading_positions_writer_id ON reading_positions(writer_id);
CREATE INDEX idx_reading_positions_last_read ON reading_positions(last_read_at);

-- Function to update reading position
CREATE OR REPLACE FUNCTION update_reading_position(
  p_user_id uuid,
  p_writer_id uuid,
  p_entry_id uuid
)
RETURNS void AS $$
BEGIN
  INSERT INTO reading_positions (user_id, writer_id, last_read_entry_id, last_read_at)
  VALUES (p_user_id, p_writer_id, p_entry_id, now())
  ON CONFLICT (user_id, writer_id)
  DO UPDATE SET
    last_read_entry_id = p_entry_id,
    last_read_at = now(),
    updated_at = now();
END;
$$ LANGUAGE plpgsql;

-- Function to get next entry for reading
CREATE OR REPLACE FUNCTION get_next_entry(
  p_writer_id uuid,
  p_current_entry_id uuid
)
RETURNS TABLE(
  id uuid,
  title text,
  created_at timestamp
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    de.id,
    de.title,
    de.created_at
  FROM diary_entries de
  WHERE de.writer_id = p_writer_id
    AND de.is_hidden = false
    AND de.created_at > (
      SELECT created_at 
      FROM diary_entries 
      WHERE id = p_current_entry_id
    )
  ORDER BY de.created_at ASC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to get previous entry for reading
CREATE OR REPLACE FUNCTION get_previous_entry(
  p_writer_id uuid,
  p_current_entry_id uuid
)
RETURNS TABLE(
  id uuid,
  title text,
  created_at timestamp
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    de.id,
    de.title,
    de.created_at
  FROM diary_entries de
  WHERE de.writer_id = p_writer_id
    AND de.is_hidden = false
    AND de.created_at < (
      SELECT created_at 
      FROM diary_entries 
      WHERE id = p_current_entry_id
    )
  ORDER BY de.created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;
