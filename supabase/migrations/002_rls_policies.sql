-- Enable Row Level Security on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE diary_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view public profiles" ON users
    FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Diary entries policies
CREATE POLICY "Anyone can view non-hidden diary entries" ON diary_entries
    FOR SELECT USING (NOT is_hidden);

CREATE POLICY "Writers can manage their own entries" ON diary_entries
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all entries" ON diary_entries
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Photos policies
CREATE POLICY "Anyone can view approved photos" ON photos
    FOR SELECT USING (
        moderation_status = 'approved' AND
        EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id AND NOT is_hidden
        )
    );

CREATE POLICY "Writers can manage photos for their entries" ON photos
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all photos" ON photos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Subscriptions policies
CREATE POLICY "Users can view their own subscriptions" ON subscriptions
    FOR SELECT USING (
        auth.uid() = subscriber_id OR auth.uid() = writer_id
    );

CREATE POLICY "Users can create subscriptions as subscriber" ON subscriptions
    FOR INSERT WITH CHECK (auth.uid() = subscriber_id);

CREATE POLICY "Writers can view their subscribers" ON subscriptions
    FOR SELECT USING (auth.uid() = writer_id);

CREATE POLICY "Admins can view all subscriptions" ON subscriptions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Payments policies
CREATE POLICY "Users can view their payment history" ON payments
    FOR SELECT USING (
        auth.uid() = payer_id OR auth.uid() = writer_id
    );

CREATE POLICY "System can insert payments" ON payments
    FOR INSERT WITH CHECK (true); -- Payments created via webhooks

CREATE POLICY "Admins can view all payments" ON payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Comments policies
CREATE POLICY "Anyone can view non-deleted comments" ON comments
    FOR SELECT USING (NOT is_deleted);

CREATE POLICY "Subscribers can comment on entries they're subscribed to" ON comments
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM subscriptions s
            JOIN diary_entries de ON de.user_id = s.writer_id
            WHERE s.subscriber_id = auth.uid() 
            AND de.id = diary_entry_id
            AND s.active_until > NOW()
        )
    );

CREATE POLICY "Users can update their own comments" ON comments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Writers can moderate comments on their entries" ON comments
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can moderate all comments" ON comments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Flags policies
CREATE POLICY "Authenticated users can create flags" ON flags
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

CREATE POLICY "Users can view their own flags" ON flags
    FOR SELECT USING (auth.uid() = reporter_id);

CREATE POLICY "Admins can view and manage all flags" ON flags
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Settings policies
CREATE POLICY "Anyone can read settings" ON settings
    FOR SELECT USING (true);

CREATE POLICY "Only admins can modify settings" ON settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Create helper functions for paywall logic
CREATE OR REPLACE FUNCTION user_has_active_subscription(subscriber_uuid UUID, writer_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM subscriptions
        WHERE subscriber_id = subscriber_uuid 
        AND writer_id = writer_uuid
        AND active_until > NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_entry_preview(entry_id UUID, viewer_id UUID DEFAULT NULL)
RETURNS TABLE(
    id UUID,
    title TEXT,
    body_md TEXT,
    is_free BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    writer_name TEXT,
    writer_price INTEGER,
    can_read_full BOOLEAN
) AS $$
DECLARE
    entry_record RECORD;
    writer_record RECORD;
    can_read BOOLEAN := FALSE;
BEGIN
    -- Get the entry and writer info
    SELECT de.*, u.name as writer_name, u.price_monthly
    INTO entry_record
    FROM diary_entries de
    JOIN users u ON u.id = de.user_id
    WHERE de.id = entry_id AND NOT de.is_hidden;
    
    IF NOT FOUND THEN
        RETURN;
    END IF;
    
    -- Check if user can read full content
    IF entry_record.is_free THEN
        can_read := TRUE;
    ELSIF viewer_id IS NOT NULL THEN
        can_read := user_has_active_subscription(viewer_id, entry_record.user_id);
    END IF;
    
    -- Return appropriate content
    RETURN QUERY SELECT
        entry_record.id,
        entry_record.title,
        CASE 
            WHEN can_read THEN entry_record.body_md
            ELSE LEFT(entry_record.body_md, 120) || '...'
        END as body_md,
        entry_record.is_free,
        entry_record.created_at,
        entry_record.writer_name,
        entry_record.price_monthly,
        can_read;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;