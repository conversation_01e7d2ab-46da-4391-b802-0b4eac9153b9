-- Book Store System for OnlyDiary
-- Extends existing projects/chapters system for ebook functionality

-- Add book-specific fields to existing projects table
ALTER TABLE projects ADD COLUMN IF NOT EXISTS is_ebook BOOLEAN DEFAULT FALSE;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS ebook_file_url TEXT; -- PDF/EPUB storage URL
ALTER TABLE projects ADD COLUMN IF NOT EXISTS ebook_file_type TEXT CHECK (ebook_file_type IN ('pdf', 'epub'));
ALTER TABLE projects ADD COLUMN IF NOT EXISTS author_name TEXT; -- Custom author name (different from user profile name)
ALTER TABLE projects ADD COLUMN IF NOT EXISTS preview_chapters INTEGER DEFAULT 1; -- How many chapters are free
ALTER TABLE projects ADD COLUMN IF NOT EXISTS reading_time_minutes INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}';
ALTER TABLE projects ADD COLUMN IF NOT EXISTS sales_count INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS daily_sales INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS bestseller_rank INTEGER;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS featured_until TIMESTAMP WITH TIME ZONE;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,1) DEFAULT 0; -- 0.0 to 10.0 (10-pen system)
ALTER TABLE projects ADD COLUMN IF NOT EXISTS review_count INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS book_type TEXT CHECK (book_type IN ('fiction', 'non_fiction', 'memoir', 'poetry', 'other')) DEFAULT 'other';

-- Book purchases table (separate from diary subscriptions)
CREATE TABLE IF NOT EXISTS book_purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    purchase_price_cents INTEGER NOT NULL,
    stripe_payment_id TEXT,
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, project_id) -- One purchase per user per book
);

-- 10-pen review system (verified purchases only)
CREATE TABLE IF NOT EXISTS book_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    purchase_id UUID REFERENCES book_purchases(id) ON DELETE CASCADE,
    pen_rating INTEGER CHECK (pen_rating >= 1 AND pen_rating <= 10) NOT NULL,
    review_title TEXT,
    review_text TEXT,
    is_verified BOOLEAN DEFAULT TRUE, -- Always true since tied to purchase
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, project_id) -- One review per user per book
);

-- Review helpfulness votes
CREATE TABLE IF NOT EXISTS review_votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    review_id UUID REFERENCES book_reviews(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    is_helpful BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(review_id, user_id) -- One vote per user per review
);

-- Reading progress tracking
CREATE TABLE IF NOT EXISTS reading_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
    progress_percentage DECIMAL(5,2) DEFAULT 0, -- 0.00 to 100.00
    last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reading_time_seconds INTEGER DEFAULT 0,
    
    UNIQUE(user_id, project_id, chapter_id)
);

-- Bookmarks for ebook reading
CREATE TABLE IF NOT EXISTS bookmarks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
    position_in_chapter INTEGER DEFAULT 0, -- Character position or paragraph number
    bookmark_note TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Daily bestsellers tracking
CREATE TABLE IF NOT EXISTS daily_bestsellers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    sales_count INTEGER NOT NULL DEFAULT 0,
    rank INTEGER NOT NULL,
    book_type TEXT,
    
    UNIQUE(date, project_id)
);

-- User's book library (purchased + free books)
CREATE TABLE IF NOT EXISTS user_library (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    access_type TEXT CHECK (access_type IN ('purchased', 'free', 'preview')) NOT NULL,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, project_id)
);
