-- Add pricing for waitlist notifications
CREATE TABLE waitlist_notification_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    campaign_name TEXT NOT NULL,
    recipient_count INTEGER NOT NULL,
    cost_cents INTEGER NOT NULL, -- Cost in cents (50 cents per 100 people)
    stripe_payment_intent_id TEXT,
    payment_status TEXT CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')) DEFAULT 'pending',
    notification_status TEXT CHECK (notification_status IN ('draft', 'scheduled', 'sending', 'sent', 'failed')) DEFAULT 'draft',
    scheduled_for TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    notifications_sent INTEGER DEFAULT 0,
    notifications_failed INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Track individual notification sends for analytics
CREATE TABLE waitlist_notification_sends (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID REFERENCES waitlist_notification_campaigns(id) ON DELETE CASCADE,
    waitlist_entry_id UUID REFERENCES project_waitlist(id) ON DELETE CASCADE,
    recipient_email TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivery_status TEXT CHECK (delivery_status IN ('sent', 'delivered', 'failed', 'bounced')) DEFAULT 'sent',
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE
);

-- Add notification credits to users table (prepaid credits system)
ALTER TABLE users ADD COLUMN notification_credits INTEGER DEFAULT 0;

-- Add indexes
CREATE INDEX idx_waitlist_campaigns_user_id ON waitlist_notification_campaigns(user_id);
CREATE INDEX idx_waitlist_campaigns_project_id ON waitlist_notification_campaigns(project_id);
CREATE INDEX idx_waitlist_campaigns_payment_status ON waitlist_notification_campaigns(payment_status);
CREATE INDEX idx_waitlist_campaigns_notification_status ON waitlist_notification_campaigns(notification_status);
CREATE INDEX idx_waitlist_sends_campaign_id ON waitlist_notification_sends(campaign_id);
CREATE INDEX idx_waitlist_sends_recipient_email ON waitlist_notification_sends(recipient_email);

-- Enable RLS
ALTER TABLE waitlist_notification_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE waitlist_notification_sends ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can manage their own notification campaigns" ON waitlist_notification_campaigns
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own notification sends" ON waitlist_notification_sends
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM waitlist_notification_campaigns 
            WHERE id = waitlist_notification_sends.campaign_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert notification sends" ON waitlist_notification_sends
    FOR INSERT WITH CHECK (true);

-- Admins can view all campaigns and sends
CREATE POLICY "Admins can view all notification campaigns" ON waitlist_notification_campaigns
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can view all notification sends" ON waitlist_notification_sends
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Function to calculate notification cost
CREATE OR REPLACE FUNCTION calculate_notification_cost(recipient_count INTEGER)
RETURNS INTEGER AS $$
BEGIN
    -- $0.50 per 100 people, minimum $0.50
    -- 50 cents = 50 cents, so cost in cents = CEIL(recipient_count / 100.0) * 50
    RETURN GREATEST(50, CEIL(recipient_count / 100.0) * 50);
END;
$$ LANGUAGE plpgsql;

-- Function to check if user has enough credits
CREATE OR REPLACE FUNCTION has_sufficient_credits(user_uuid UUID, required_credits INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    current_credits INTEGER;
BEGIN
    SELECT notification_credits INTO current_credits
    FROM users 
    WHERE id = user_uuid;
    
    RETURN COALESCE(current_credits, 0) >= required_credits;
END;
$$ LANGUAGE plpgsql;

-- Function to deduct credits when campaign is sent
CREATE OR REPLACE FUNCTION deduct_notification_credits()
RETURNS TRIGGER AS $$
BEGIN
    -- When campaign status changes to 'sent', deduct credits
    IF OLD.notification_status != 'sent' AND NEW.notification_status = 'sent' THEN
        UPDATE users 
        SET notification_credits = notification_credits - NEW.cost_cents
        WHERE id = NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to deduct credits when campaign is sent
CREATE TRIGGER deduct_credits_on_campaign_sent
    AFTER UPDATE ON waitlist_notification_campaigns
    FOR EACH ROW 
    EXECUTE FUNCTION deduct_notification_credits();

-- Add updated_at trigger
CREATE TRIGGER update_waitlist_campaigns_updated_at BEFORE UPDATE ON waitlist_notification_campaigns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
