-- Create reactions table
CREATE TABLE reactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  diary_entry_id uuid REFERENCES diary_entries(id) ON DELETE CASCADE,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  reaction_type VARCHAR(20) NOT NULL,
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now(),
  
  -- Ensure one reaction per user per entry
  UNIQUE(diary_entry_id, user_id)
);

-- Enable RLS
ALTER TABLE reactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Anyone can view reactions"
ON reactions
FOR SELECT USING (true);

CREATE POLICY "Users can insert their own reactions"
ON reactions
FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own reactions"
ON reactions
FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own reactions"
ON reactions
FOR DELETE
USING (user_id = auth.uid());

-- Add indexes for performance
CREATE INDEX idx_reactions_diary_entry_id ON reactions(diary_entry_id);
CREATE INDEX idx_reactions_user_id ON reactions(user_id);
CREATE INDEX idx_reactions_type ON reactions(reaction_type);

-- Add function to get reaction counts
CREATE OR REPLACE FUNCTION get_reaction_counts(entry_id uuid)
RETURNS TABLE(reaction_type text, count bigint) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    r.reaction_type::text,
    COUNT(*)::bigint
  FROM reactions r
  WHERE r.diary_entry_id = entry_id
  GROUP BY r.reaction_type;
END;
$$ LANGUAGE plpgsql;
