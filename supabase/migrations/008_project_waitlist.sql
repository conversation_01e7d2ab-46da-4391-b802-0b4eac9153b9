-- Create project waitlist table
CREATE TABLE project_waitlist (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_email TEXT NOT NULL,
    user_name TEXT,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- Optional if user is registered
    notification_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one entry per email per project
    UNIQUE(project_id, user_email)
);

-- Add indexes for performance
CREATE INDEX idx_project_waitlist_project_id ON project_waitlist(project_id);
CREATE INDEX idx_project_waitlist_user_email ON project_waitlist(user_email);
CREATE INDEX idx_project_waitlist_user_id ON project_waitlist(user_id);
CREATE INDEX idx_project_waitlist_notification_sent ON project_waitlist(notification_sent);

-- Enable RLS
ALTER TABLE project_waitlist ENABLE ROW LEVEL SECURITY;

-- RLS Policies for project waitlist
-- Anyone can join a waitlist (for public signup)
CREATE POLICY "Anyone can join project waitlist" ON project_waitlist
    FOR INSERT WITH CHECK (true);

-- Users can view their own waitlist entries
CREATE POLICY "Users can view their own waitlist entries" ON project_waitlist
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.jwt() ->> 'email' = user_email
    );

-- Project owners can view their project waitlists
CREATE POLICY "Project owners can view their waitlists" ON project_waitlist
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE id = project_waitlist.project_id AND user_id = auth.uid()
        )
    );

-- Admins can view all waitlist entries
CREATE POLICY "Admins can view all waitlist entries" ON project_waitlist
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Project owners and admins can update notification status
CREATE POLICY "Project owners can update waitlist notifications" ON project_waitlist
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE id = project_waitlist.project_id AND user_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Function to notify waitlist when project becomes public
CREATE OR REPLACE FUNCTION notify_project_waitlist()
RETURNS TRIGGER AS $$
BEGIN
    -- If project changed from private to public, mark waitlist for notification
    IF OLD.is_private = true AND NEW.is_private = false THEN
        UPDATE project_waitlist 
        SET notification_sent = false 
        WHERE project_id = NEW.id AND notification_sent = false;
        
        -- Here you could trigger external notifications
        -- For now, we'll just mark them as ready to be notified
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to notify waitlist when project becomes public
CREATE TRIGGER notify_waitlist_on_project_public
    AFTER UPDATE ON projects
    FOR EACH ROW 
    WHEN (OLD.is_private IS DISTINCT FROM NEW.is_private)
    EXECUTE FUNCTION notify_project_waitlist();
