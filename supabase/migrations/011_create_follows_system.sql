-- Create follows table for tracking follower relationships
CREATE TABLE IF NOT EXISTS follows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    writer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(follower_id, writer_id)
);

-- Enable RLS
ALTER TABLE follows ENABLE ROW LEVEL SECURITY;

-- RLS Policies for follows table
CREATE POLICY "Users can manage their own follows" 
ON follows FOR ALL 
USING (auth.uid() = follower_id);

CREATE POLICY "Writers can view their followers" 
ON follows FOR SELECT 
USING (auth.uid() = writer_id);

-- Function to check if user follows a writer
CREATE OR REPLACE FUNCTION is_following(p_writer_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM follows 
        WHERE follower_id = auth.uid() 
        AND writer_id = p_writer_id
    );
END;
$$;

-- Function to get follower count for a writer
CREATE OR REPLACE FUNCTION get_follower_count(p_writer_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN (
        SELECT COUNT(*) 
        FROM follows 
        WHERE writer_id = p_writer_id
    );
END;
$$;