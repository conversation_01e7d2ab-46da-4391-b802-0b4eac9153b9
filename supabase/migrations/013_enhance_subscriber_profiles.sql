-- Enhance subscriber profiles with additional features

-- Add columns to users table for enhanced subscriber profiles
ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_picture_url TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS social_twitter TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS social_instagram TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS social_website TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS custom_url TEXT UNIQUE;

-- Create table for favorite creators (subscribers can showcase their favorite writers)
CREATE TABLE IF NOT EXISTS subscriber_favorites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  subscriber_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  writer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent duplicate favorites
  CONSTRAINT unique_subscriber_favorite UNIQUE (subscriber_id, writer_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscriber_favorites_subscriber_id ON subscriber_favorites(subscriber_id);
CREATE INDEX IF NOT EXISTS idx_subscriber_favorites_writer_id ON subscriber_favorites(writer_id);

-- Add RLS policies for subscriber_favorites
ALTER TABLE subscriber_favorites ENABLE ROW LEVEL SECURITY;

-- Users can view all favorites (public information)
DROP POLICY IF EXISTS "Anyone can view favorites" ON subscriber_favorites;
CREATE POLICY "Anyone can view favorites" ON subscriber_favorites
  FOR SELECT USING (true);

-- Users can manage their own favorites
DROP POLICY IF EXISTS "Users can manage their own favorites" ON subscriber_favorites;
CREATE POLICY "Users can manage their own favorites" ON subscriber_favorites
  FOR ALL USING (auth.uid() = subscriber_id);

-- Create indexes for the new columns (using IF NOT EXISTS equivalent)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE indexname = 'idx_users_custom_url'
  ) THEN
    CREATE INDEX idx_users_custom_url ON users(custom_url) WHERE custom_url IS NOT NULL;
  END IF;
END $$;