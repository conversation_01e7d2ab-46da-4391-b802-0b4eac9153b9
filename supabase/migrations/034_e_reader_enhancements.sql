-- E-Reader Enhancements for OnlyDiary
-- Adds passage popularity tracking and helper functions

-- Create passage popularity table if it doesn't exist
CREATE TABLE IF NOT EXISTS passage_popularity (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
    passage_hash VARCHAR(64) NOT NULL,
    highlight_count INTEGER DEFAULT 1,
    passage_text TEXT NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(project_id, chapter_id, passage_hash)
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_passage_popularity_count 
ON passage_popularity(project_id, chapter_id, highlight_count DESC);

-- Function to increment passage popularity
CREATE OR REPLACE FUNCTION increment_passage_popularity(
    p_project_id UUID,
    p_chapter_id UUID,
    p_passage_text TEXT
)
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    p_hash VARCHAR(64);
BEGIN
    -- Create a simple hash of the passage text
    p_hash := encode(digest(p_passage_text, 'sha256'), 'hex');
    
    -- Insert or update passage popularity
    INSERT INTO passage_popularity (project_id, chapter_id, passage_hash, passage_text, highlight_count)
    VALUES (p_project_id, p_chapter_id, p_hash, p_passage_text, 1)
    ON CONFLICT (project_id, chapter_id, passage_hash)
    DO UPDATE SET 
        highlight_count = passage_popularity.highlight_count + 1,
        updated_at = NOW();
END;
$$;

-- Function to get popular passages for a chapter
CREATE OR REPLACE FUNCTION get_popular_passages(
    p_project_id UUID,
    p_chapter_id UUID,
    min_highlights INTEGER DEFAULT 3
)
RETURNS TABLE (
    passage_text TEXT,
    highlight_count INTEGER,
    passage_hash VARCHAR(64)
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pp.passage_text,
        pp.highlight_count,
        pp.passage_hash
    FROM passage_popularity pp
    WHERE pp.project_id = p_project_id 
      AND pp.chapter_id = p_chapter_id
      AND pp.highlight_count >= min_highlights
    ORDER BY pp.highlight_count DESC, pp.updated_at DESC
    LIMIT 10;
END;
$$;

-- Add RLS policies for passage_popularity
ALTER TABLE passage_popularity ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to read popular passages
CREATE POLICY "Users can view popular passages" ON passage_popularity
    FOR SELECT USING (auth.role() = 'authenticated');

-- Allow the increment function to work (system level)
CREATE POLICY "System can update passage popularity" ON passage_popularity
    FOR ALL USING (true);

-- Add indexes for better performance on existing tables
CREATE INDEX IF NOT EXISTS idx_highlights_chapter_user 
ON highlights(chapter_id, user_id);

CREATE INDEX IF NOT EXISTS idx_highlights_project_chapter 
ON highlights(project_id, chapter_id);

CREATE INDEX IF NOT EXISTS idx_margin_comments_chapter_user 
ON margin_comments(chapter_id, user_id);

CREATE INDEX IF NOT EXISTS idx_margin_comments_project_chapter 
ON margin_comments(project_id, chapter_id);

-- Update reading progress tracking with better indexing
CREATE INDEX IF NOT EXISTS idx_reading_progress_user_project 
ON reading_progress(user_id, project_id);

-- Book processing and upload system tables
CREATE TABLE IF NOT EXISTS book_processing_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    book_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'pending', -- pending, extracting, structuring, indexing, optimizing, completed, failed
    format VARCHAR(10) NOT NULL, -- pdf, epub, mobi, txt
    total_chapters INTEGER DEFAULT 0,
    processed_chapters INTEGER DEFAULT 0,
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Text mapping for precise highlighting
CREATE TABLE IF NOT EXISTS chapter_text_maps (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    book_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
    text_mapping JSONB NOT NULL, -- Character-level mapping for highlights
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add processing fields to projects table
ALTER TABLE projects
ADD COLUMN IF NOT EXISTS processing_status VARCHAR(50) DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS original_format VARCHAR(10),
ADD COLUMN IF NOT EXISTS file_path TEXT,
ADD COLUMN IF NOT EXISTS total_words INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS estimated_pages INTEGER DEFAULT 0;

-- Indexes for processing system
CREATE INDEX IF NOT EXISTS idx_processing_jobs_book_status
ON book_processing_jobs(book_id, status);

CREATE INDEX IF NOT EXISTS idx_text_maps_chapter
ON chapter_text_maps(chapter_id);

-- Function to create book-specific indexes after processing
CREATE OR REPLACE FUNCTION create_book_indexes(book_id UUID)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Create indexes for this book's highlights and comments
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_highlights_book_%s ON highlights(project_id, chapter_id) WHERE project_id = %L',
                   replace(book_id::text, '-', '_'), book_id);

    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_comments_book_%s ON margin_comments(project_id, chapter_id) WHERE project_id = %L',
                   replace(book_id::text, '-', '_'), book_id);
END;
$$;

-- Add a function to get reading statistics
CREATE OR REPLACE FUNCTION get_reading_stats(p_user_id UUID, p_project_id UUID)
RETURNS JSON
LANGUAGE plpgsql
AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_highlights', COALESCE(h.highlight_count, 0),
        'total_comments', COALESCE(c.comment_count, 0),
        'chapters_read', COALESCE(r.chapters_read, 0),
        'total_reading_time', COALESCE(r.total_time, 0),
        'last_read_chapter', r.last_chapter_id,
        'book_progress', COALESCE(r.book_progress, 0)
    ) INTO result
    FROM (
        SELECT COUNT(*) as highlight_count
        FROM highlights
        WHERE user_id = p_user_id AND project_id = p_project_id
    ) h
    CROSS JOIN (
        SELECT COUNT(*) as comment_count
        FROM margin_comments
        WHERE user_id = p_user_id AND project_id = p_project_id
    ) c
    CROSS JOIN (
        SELECT
            COUNT(DISTINCT chapter_id) as chapters_read,
            SUM(reading_time_seconds) as total_time,
            AVG(progress_percentage) as book_progress,
            (SELECT chapter_id FROM reading_progress
             WHERE user_id = p_user_id AND project_id = p_project_id
             ORDER BY last_read_at DESC LIMIT 1) as last_chapter_id
        FROM reading_progress
        WHERE user_id = p_user_id AND project_id = p_project_id
    ) r;

    RETURN result;
END;
$$;

-- Function to get chapter text mapping for highlighting
CREATE OR REPLACE FUNCTION get_chapter_text_map(p_chapter_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    mapping JSONB;
BEGIN
    SELECT text_mapping INTO mapping
    FROM chapter_text_maps
    WHERE chapter_id = p_chapter_id;

    RETURN COALESCE(mapping, '{}'::jsonb);
END;
$$;

-- RLS policies for new tables
ALTER TABLE book_processing_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapter_text_maps ENABLE ROW LEVEL SECURITY;

-- Allow users to view processing status of their books
CREATE POLICY "Users can view their book processing jobs" ON book_processing_jobs
    FOR SELECT USING (
        book_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
        )
    );

-- Allow system to manage processing jobs
CREATE POLICY "System can manage processing jobs" ON book_processing_jobs
    FOR ALL USING (true);

-- Allow authenticated users to read text maps
CREATE POLICY "Users can read text maps" ON chapter_text_maps
    FOR SELECT USING (auth.role() = 'authenticated');

-- Allow system to manage text maps
CREATE POLICY "System can manage text maps" ON chapter_text_maps
    FOR ALL USING (true);
