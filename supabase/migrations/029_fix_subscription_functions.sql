-- Fix subscription checking functions to use correct column names
-- This addresses the bug where paid content shows as free

-- Create a simple working subscription function
-- Based on your current schema: reader_id + active_until
CREATE OR REPLACE FUNCTION user_has_active_subscription(subscriber_uuid UUID, writer_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.subscriptions
        WHERE reader_id = subscriber_uuid
        AND writer_id = writer_uuid
        AND status = 'active'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Grant permissions
GRANT EXECUTE ON FUNCTION user_has_active_subscription(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_active_subscription(UUID, UUID) TO anon;

-- Drop and recreate the get_entry_preview function to fix return type
DROP FUNCTION IF EXISTS get_entry_preview(UUID, UUID);

-- Also update the get_entry_preview function to use the correct subscription check
CREATE OR REPLACE FUNCTION get_entry_preview(
    entry_id UUID,
    viewer_id UUID DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    title TEXT,
    body_md TEXT,
    is_free BOOLEAN,
    is_hidden BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    user_id UUID,
    writer_id UUID,
    writer_name TEXT,
    writer_price INTEGER,
    can_read_full BOOLEAN
) AS $$
DECLARE
    entry_record RECORD;
    can_read BOOLEAN := FALSE;
BEGIN
    -- Debug logging
    RAISE NOTICE 'get_entry_preview called with entry_id=%, viewer_id=%', entry_id, viewer_id;

    -- Get the entry and writer info
    SELECT 
        de.id,
        de.title,
        de.body_md,
        de.is_free,
        de.is_hidden,
        de.created_at,
        de.user_id,
        u.name as writer_name,
        u.price_monthly
    INTO entry_record
    FROM public.diary_entries de
    JOIN public.users u ON u.id = de.user_id
    WHERE de.id = entry_id;
    
    IF NOT FOUND THEN
        RETURN;
    END IF;
    
    -- Check if user can read full content
    IF entry_record.is_free THEN
        can_read := TRUE;
    ELSIF viewer_id IS NOT NULL THEN
        -- Check if viewer is the author
        IF viewer_id = entry_record.user_id THEN
            can_read := TRUE;
        ELSE
            -- Check subscription using the updated function
            can_read := user_has_active_subscription(viewer_id, entry_record.user_id);
        END IF;
    END IF;
    
    -- Return the entry data
    RETURN QUERY SELECT
        entry_record.id,
        entry_record.title,
        CASE 
            WHEN can_read THEN entry_record.body_md
            ELSE LEFT(entry_record.body_md, 120) || '...'
        END as body_md,
        entry_record.is_free,
        entry_record.is_hidden,
        entry_record.created_at,
        entry_record.user_id,
        entry_record.user_id as writer_id,
        entry_record.writer_name,
        COALESCE(entry_record.price_monthly, 999) as writer_price,
        can_read;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Grant permissions
GRANT EXECUTE ON FUNCTION user_has_active_subscription(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_active_subscription(UUID, UUID) TO anon;
GRANT EXECUTE ON FUNCTION get_entry_preview(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_entry_preview(UUID, UUID) TO anon;

-- Test the function (this will show in logs)
DO $$
DECLARE
    test_result BOOLEAN;
BEGIN
    -- This will help us see if the function is working
    RAISE NOTICE 'Subscription function fix completed. Test your paid content access now.';
END $$;

SELECT 'Subscription access functions updated successfully!' as status;
