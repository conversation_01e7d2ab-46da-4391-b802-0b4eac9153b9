-- Create flowers table for readers to receive flowers from creators

CREATE TABLE IF NOT EXISTS flowers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  receiver_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  giver_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_flowers_receiver_id ON flowers(receiver_id);
CREATE INDEX idx_flowers_giver_id ON flowers(giver_id);
CREATE INDEX idx_flowers_created_at ON flowers(created_at);

-- Allow multiple flowers per day but prevent spam (optional constraint)
-- CREATE UNIQUE INDEX unique_daily_flower ON flowers (receiver_id, giver_id, (created_at::date));

-- Add RLS policies
ALTER TABLE flowers ENABLE ROW LEVEL SECURITY;

-- Users can view flowers they received or gave
CREATE POLICY "Users can view their flowers" ON flowers
  FOR SELECT USING (
    auth.uid() = receiver_id OR 
    auth.uid() = giver_id
  );

-- Users can give flowers to others (but not themselves)
CREATE POLICY "Users can give flowers" ON flowers
  FOR INSERT WITH CHECK (
    auth.uid() = giver_id AND 
    auth.uid() != receiver_id
  );

-- Users can delete flowers they gave
CREATE POLICY "Users can delete flowers they gave" ON flowers
  FOR DELETE USING (auth.uid() = giver_id);

-- Add flower count to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS flower_count INTEGER DEFAULT 0;

-- Function to update flower count
CREATE OR REPLACE FUNCTION update_flower_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE users 
    SET flower_count = flower_count + 1 
    WHERE id = NEW.receiver_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE users 
    SET flower_count = GREATEST(flower_count - 1, 0) 
    WHERE id = OLD.receiver_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update flower count
CREATE TRIGGER update_flower_count_trigger
  AFTER INSERT OR DELETE ON flowers
  FOR EACH ROW
  EXECUTE FUNCTION update_flower_count();

-- Initialize flower counts for existing users
UPDATE users SET flower_count = (
  SELECT COUNT(*) FROM flowers WHERE receiver_id = users.id
);