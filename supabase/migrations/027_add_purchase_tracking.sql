-- Add purchase tracking for book projects and chapters
-- This enables one-time purchases separate from subscriptions

-- Create purchases table if it doesn't exist
CREATE TABLE IF NOT EXISTS purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    buyer_id UUID REFERENCES users(id) ON DELETE SET NULL,
    seller_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- What was purchased
    purchase_type TEXT NOT NULL CHECK (purchase_type IN ('chapter', 'full_project')),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES chapters(id) ON DELETE SET NULL, -- NULL for full project purchases
    
    -- Payment details
    amount_cents INTEGER NOT NULL CHECK (amount_cents > 0),
    stripe_payment_id TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate purchases
    UNIQUE(buyer_id, project_id, chapter_id)
);

-- Add pricing fields to projects if they don't exist
DO $$
BEGIN
    -- Add pricing type to projects
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'pricing_type'
    ) THEN
        ALTER TABLE projects ADD COLUMN pricing_type TEXT DEFAULT 'per_chapter' 
            CHECK (pricing_type IN ('free', 'per_chapter', 'full_project', 'donation_based'));
    END IF;
    
    -- Add project price
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'project_price'
    ) THEN
        ALTER TABLE projects ADD COLUMN project_price INTEGER CHECK (project_price >= 0);
    END IF;
    
    -- Add default chapter price
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'default_chapter_price'
    ) THEN
        ALTER TABLE projects ADD COLUMN default_chapter_price INTEGER CHECK (default_chapter_price >= 0);
    END IF;
    
    -- Add donation settings
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'allow_donations'
    ) THEN
        ALTER TABLE projects ADD COLUMN allow_donations BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Add pricing fields to chapters if they don't exist
DO $$
BEGIN
    -- Add chapter-specific pricing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'chapters' AND column_name = 'is_free'
    ) THEN
        ALTER TABLE chapters ADD COLUMN is_free BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'chapters' AND column_name = 'chapter_price'
    ) THEN
        ALTER TABLE chapters ADD COLUMN chapter_price INTEGER CHECK (chapter_price >= 0);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'chapters' AND column_name = 'allow_donations'
    ) THEN
        ALTER TABLE chapters ADD COLUMN allow_donations BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Add donation settings to diary entries if missing
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'diary_entries' AND column_name = 'allow_donations'
    ) THEN
        ALTER TABLE diary_entries ADD COLUMN allow_donations BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Create indexes for purchases
CREATE INDEX IF NOT EXISTS idx_purchases_buyer_id ON purchases(buyer_id, created_at);
CREATE INDEX IF NOT EXISTS idx_purchases_seller_id ON purchases(seller_id, created_at);
CREATE INDEX IF NOT EXISTS idx_purchases_project_id ON purchases(project_id);
CREATE INDEX IF NOT EXISTS idx_purchases_stripe_id ON purchases(stripe_payment_id) WHERE stripe_payment_id IS NOT NULL;

-- Enable RLS on purchases
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;

-- RLS Policies for purchases
-- Buyers can view their own purchases
CREATE POLICY "Buyers can view their purchases" ON purchases
    FOR SELECT USING (auth.uid() = buyer_id);

-- Sellers can view purchases of their content
CREATE POLICY "Sellers can view their sales" ON purchases
    FOR SELECT USING (auth.uid() = seller_id);

-- Admins can view all purchases
CREATE POLICY "Admins can view all purchases" ON purchases
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Success message
SELECT 'Purchase tracking added successfully!' as status;
