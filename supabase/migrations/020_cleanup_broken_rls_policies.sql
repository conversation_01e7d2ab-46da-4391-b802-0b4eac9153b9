-- EMERGENCY CLEANUP: Fix broken RLS policies created by the dynamic migration
-- This will remove all policies and recreate them with proper syntax

-- First, disable <PERSON><PERSON> temporarily to avoid access issues during cleanup
ALTER TABLE public.comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookmarks DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.follows DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.flowers DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_reads DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.donations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapters DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_purchases DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_loves DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_waitlist DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings DISABLE ROW LEVEL SECURITY;

-- Drop ALL policies from affected tables to start clean
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN
        SELECT schemaname, tablename, policyname
        FROM pg_policies 
        WHERE schemaname = 'public'
        AND tablename IN (
            'comments', 'bookmarks', 'follows', 'flowers', 'subscriptions',
            'notifications', 'post_reads', 'donations', 'chapters',
            'project_subscriptions', 'project_purchases', 'chapter_comments',
            'chapter_loves', 'project_waitlist', 'settings'
        )
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', 
            policy_record.policyname, 
            policy_record.schemaname, 
            policy_record.tablename
        );
    END LOOP;
END $$;

-- Re-enable RLS
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookmarks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.flowers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_reads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.donations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_loves ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_waitlist ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Recreate essential policies with optimized auth calls
-- Comments policies
CREATE POLICY "Users can view comments" ON comments
    FOR SELECT USING (true);

CREATE POLICY "Users can comment on accessible entries" ON comments
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Users can update their own comments" ON comments
    FOR UPDATE USING ((select auth.uid()) = user_id)
    WITH CHECK ((select auth.uid()) = user_id);

-- Bookmarks policies
CREATE POLICY "Users can manage bookmarks" ON bookmarks
    FOR ALL USING ((select auth.uid()) = reader_id);

-- Follows policies
CREATE POLICY "Users can manage their follows" ON follows
    FOR ALL USING ((select auth.uid()) = follower_id);

-- Flowers policies
CREATE POLICY "Users can manage flowers" ON flowers
    FOR ALL USING ((select auth.uid()) = user_id);

-- Subscriptions policies
CREATE POLICY "Users can manage their subscriptions" ON subscriptions
    FOR ALL USING ((select auth.uid()) = subscriber_id);

-- Settings policies
CREATE POLICY "Users can manage their settings" ON settings
    FOR ALL USING ((select auth.uid()) = user_id);