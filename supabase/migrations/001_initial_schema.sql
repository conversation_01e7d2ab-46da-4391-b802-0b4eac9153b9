-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum for user roles
CREATE TYPE user_role AS ENUM ('visitor', 'subscriber', 'writer', 'admin');

-- Create enum for moderation status
CREATE TYPE moderation_status AS ENUM ('pending', 'approved', 'flagged', 'rejected');

-- Create enum for payment kinds
CREATE TYPE payment_kind AS ENUM ('sub', 'donation');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    role user_role DEFAULT 'visitor',
    name TEXT,
    avatar TEXT,
    bio TEXT,
    price_monthly INTEGER CHECK (price_monthly >= 299 AND price_monthly <= 5000), -- $2.99-$50 in cents
    stripe_account_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Diary entries table
CREATE TABLE diary_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    body_md TEXT NOT NULL CHECK (LENGTH(body_md) <= 20000), -- 20k char limit
    is_free BOOLEAN DEFAULT FALSE,
    is_hidden BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Photos table
CREATE TABLE photos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    alt_text TEXT NOT NULL, -- Required for accessibility
    moderation_status moderation_status DEFAULT 'pending',
    rekognition_labels JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscriber_id UUID REFERENCES users(id) ON DELETE CASCADE,
    writer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    active_until TIMESTAMP WITH TIME ZONE NOT NULL,
    stripe_subscription_id TEXT UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(subscriber_id, writer_id)
);

-- Payments table
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payer_id UUID REFERENCES users(id) ON DELETE SET NULL,
    writer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    kind payment_kind NOT NULL,
    amount_cents INTEGER NOT NULL CHECK (amount_cents > 0),
    stripe_payment_id TEXT UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Comments table
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    body TEXT NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Flags table
CREATE TABLE flags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
    reporter_id UUID REFERENCES users(id) ON DELETE SET NULL,
    reason TEXT NOT NULL,
    resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Settings table
CREATE TABLE settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key TEXT UNIQUE NOT NULL,
    value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default settings
INSERT INTO settings (key, value) VALUES 
    ('global_fee_percent', '20'),
    ('auto_hide_flag_threshold', '5'),
    ('max_photos_per_entry', '20');

-- Create indexes for performance
CREATE INDEX idx_diary_entries_user_id ON diary_entries(user_id);
CREATE INDEX idx_diary_entries_created_at ON diary_entries(created_at DESC);
CREATE INDEX idx_photos_diary_entry_id ON photos(diary_entry_id);
CREATE INDEX idx_subscriptions_subscriber_writer ON subscriptions(subscriber_id, writer_id);
CREATE INDEX idx_subscriptions_active_until ON subscriptions(active_until);
CREATE INDEX idx_payments_writer_id ON payments(writer_id);
CREATE INDEX idx_comments_diary_entry_id ON comments(diary_entry_id);
CREATE INDEX idx_flags_diary_entry_id ON flags(diary_entry_id);
CREATE INDEX idx_flags_resolved ON flags(resolved);

-- Create trigger function to update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_diary_entries_updated_at BEFORE UPDATE ON diary_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add constraint to ensure only one free entry per writer
CREATE UNIQUE INDEX idx_one_free_entry_per_writer ON diary_entries(user_id) 
WHERE is_free = TRUE;

-- Add constraint to limit photos per diary entry
CREATE OR REPLACE FUNCTION check_photo_limit()
RETURNS TRIGGER AS $$
BEGIN
    IF (SELECT COUNT(*) FROM photos WHERE diary_entry_id = NEW.diary_entry_id) >= 20 THEN
        RAISE EXCEPTION 'Maximum 20 photos per diary entry';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_photo_limit_trigger
    BEFORE INSERT ON photos
    FOR EACH ROW EXECUTE FUNCTION check_photo_limit();