-- Fix subscription table schema inconsistencies
-- Standardize on reader_id and status columns

-- Check current subscription table structure and fix if needed
DO $$
BEGIN
    -- If we have the old subscriber_id column, rename it to reader_id
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscriptions' AND column_name = 'subscriber_id'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscriptions' AND column_name = 'reader_id'
    ) THEN
        -- Rename subscriber_id to reader_id
        ALTER TABLE subscriptions RENAME COLUMN subscriber_id TO reader_id;
        
        -- Update any existing indexes
        DROP INDEX IF EXISTS idx_subscriptions_subscriber_writer;
        DROP INDEX IF EXISTS idx_subscriptions_subscriber_active;
        CREATE INDEX idx_subscriptions_reader_writer ON subscriptions(reader_id, writer_id);
    END IF;
    
    -- If we have active_until but no status column, add status
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscriptions' AND column_name = 'active_until'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscriptions' AND column_name = 'status'
    ) THEN
        -- Add status column
        ALTER TABLE subscriptions ADD COLUMN status TEXT DEFAULT 'active' 
            CHECK (status IN ('active', 'cancelled', 'expired'));
        
        -- Update status based on active_until
        UPDATE subscriptions 
        SET status = CASE 
            WHEN active_until > NOW() THEN 'active'
            ELSE 'expired'
        END;
    END IF;
    
    -- Add current_period columns if they don't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscriptions' AND column_name = 'current_period_start'
    ) THEN
        ALTER TABLE subscriptions ADD COLUMN current_period_start TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        ALTER TABLE subscriptions ADD COLUMN current_period_end TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '30 days';
        
        -- Populate from active_until if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'subscriptions' AND column_name = 'active_until'
        ) THEN
            UPDATE subscriptions 
            SET current_period_end = active_until,
                current_period_start = active_until - INTERVAL '30 days';
        END IF;
    END IF;
    
END $$;

-- Ensure we have the right constraints
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS unique_subscriber_writer_subscription;
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS unique_reader_writer_subscription;
ALTER TABLE subscriptions ADD CONSTRAINT unique_reader_writer_subscription UNIQUE (reader_id, writer_id);

-- Success message
SELECT 'Subscription schema standardized successfully!' as status;
