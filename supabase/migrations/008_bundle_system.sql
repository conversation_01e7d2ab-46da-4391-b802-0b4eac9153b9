-- Add bundle support to diary_entries table
ALTER TABLE diary_entries 
ADD COLUMN bundle_id UUID,
ADD COLUMN bundle_order INTEGER DEFAULT 1;

-- Create index for bundle queries
CREATE INDEX idx_diary_entries_bundle_id ON diary_entries(bundle_id);
CREATE INDEX idx_diary_entries_bundle_order ON diary_entries(bundle_id, bundle_order);

-- Function to create a bundle from multiple posts
CREATE OR REPLACE FUNCTION create_post_bundle(
    post_ids UUID[],
    user_uuid UUID
) RETURNS UUID AS $$
DECLARE
    bundle_uuid UUID;
    post_id UUID;
    order_counter INTEGER := 1;
BEGIN
    -- Generate new bundle ID
    bundle_uuid := uuid_generate_v4();
    
    -- Update all posts to be part of this bundle
    FOREACH post_id IN ARRAY post_ids
    LOOP
        UPDATE diary_entries 
        SET 
            bundle_id = bundle_uuid,
            bundle_order = order_counter,
            bundle_count = array_length(post_ids, 1)
        WHERE id = post_id 
          AND user_id = user_uuid;
        
        order_counter := order_counter + 1;
    END LOOP;
    
    RETURN bundle_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove post from bundle
CREATE OR REPLACE FUNCTION remove_from_bundle(
    post_id UUID,
    user_uuid UUID
) RETURNS BOOLEAN AS $$
DECLARE
    old_bundle_id UUID;
    remaining_count INTEGER;
BEGIN
    -- Get the current bundle ID
    SELECT bundle_id INTO old_bundle_id
    FROM diary_entries 
    WHERE id = post_id AND user_id = user_uuid;
    
    -- Remove from bundle
    UPDATE diary_entries 
    SET 
        bundle_id = NULL,
        bundle_order = 1,
        bundle_count = 1
    WHERE id = post_id AND user_id = user_uuid;
    
    -- Check if bundle still has posts
    IF old_bundle_id IS NOT NULL THEN
        SELECT COUNT(*) INTO remaining_count
        FROM diary_entries 
        WHERE bundle_id = old_bundle_id;
        
        -- If only one post left, remove it from bundle too
        IF remaining_count = 1 THEN
            UPDATE diary_entries 
            SET 
                bundle_id = NULL,
                bundle_order = 1,
                bundle_count = 1
            WHERE bundle_id = old_bundle_id;
        ELSE
            -- Update bundle_count for remaining posts
            UPDATE diary_entries 
            SET bundle_count = remaining_count
            WHERE bundle_id = old_bundle_id;
        END IF;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get bundle posts in order
CREATE OR REPLACE FUNCTION get_bundle_posts(bundle_uuid UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    body_md TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    bundle_order INTEGER,
    is_free BOOLEAN,
    is_hidden BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        de.id,
        de.title,
        de.body_md,
        de.created_at,
        de.bundle_order,
        de.is_free,
        de.is_hidden
    FROM diary_entries de
    WHERE de.bundle_id = bundle_uuid
    ORDER BY de.bundle_order ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the credit consumption function to handle bundles
CREATE OR REPLACE FUNCTION consume_post_credit(
    reader_uuid UUID,
    entry_uuid UUID
) RETURNS BOOLEAN AS $$
DECLARE
    entry_writer_id UUID;
    entry_bundle_id UUID;
    user_credits INTEGER;
    already_read BOOLEAN;
    bundle_already_read BOOLEAN;
BEGIN
    -- Get entry details
    SELECT user_id, bundle_id INTO entry_writer_id, entry_bundle_id
    FROM diary_entries 
    WHERE id = entry_uuid;
    
    -- Check if already read this specific entry
    SELECT EXISTS(
        SELECT 1 FROM post_reads 
        WHERE user_id = reader_uuid AND diary_entry_id = entry_uuid
    ) INTO already_read;
    
    -- If already read, they can read again
    IF already_read THEN
        RETURN TRUE;
    END IF;
    
    -- If part of a bundle, check if any post in bundle was read
    IF entry_bundle_id IS NOT NULL THEN
        SELECT EXISTS(
            SELECT 1 FROM post_reads pr
            JOIN diary_entries de ON pr.diary_entry_id = de.id
            WHERE pr.user_id = reader_uuid 
              AND de.bundle_id = entry_bundle_id
        ) INTO bundle_already_read;
        
        -- If any post in bundle was read, they can read all posts in bundle
        IF bundle_already_read THEN
            -- Record this read without consuming credit
            INSERT INTO post_reads (user_id, writer_id, diary_entry_id)
            VALUES (reader_uuid, entry_writer_id, entry_uuid)
            ON CONFLICT (user_id, diary_entry_id) DO NOTHING;
            
            RETURN TRUE;
        END IF;
    END IF;
    
    -- Check if user has credits for this writer
    SELECT COALESCE(credits_remaining, 0) INTO user_credits
    FROM post_credits 
    WHERE user_id = reader_uuid AND writer_id = entry_writer_id;
    
    -- Return true if user has credits
    RETURN user_credits > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
