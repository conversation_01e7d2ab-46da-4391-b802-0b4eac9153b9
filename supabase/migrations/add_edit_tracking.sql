-- Add edit tracking to diary_entries table
ALTER TABLE diary_entries ADD COLUMN edited_at TIMESTAMP WITH TIME ZONE;

-- Add comment to explain the field
COMMENT ON COLUMN diary_entries.edited_at IS 'Timestamp when the content was last edited (different from updated_at which tracks any field changes)';

-- Create function to automatically set edited_at when content changes
CREATE OR REPLACE FUNCTION update_edited_at()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update edited_at if title or body_md actually changed
  IF OLD.title IS DISTINCT FROM NEW.title OR OLD.body_md IS DISTINCT FROM NEW.body_md THEN
    NEW.edited_at = NOW();
  END IF;
  
  -- Always update updated_at
  NEW.updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update edited_at when content changes
DROP TRIGGER IF EXISTS trigger_update_edited_at ON diary_entries;
CREATE TRIGGER trigger_update_edited_at
  BEFORE UPDATE ON diary_entries
  FOR EACH ROW
  EXECUTE FUNCTION update_edited_at();
