-- Fix comment policies to allow commenting on free entries and accessible content
-- This restores the correct behavior that was overridden by migration 016

-- First, drop the overly restrictive subscription-only policy
DROP POLICY IF EXISTS "Subscribers can comment on entries they're subscribed to" ON comments;

-- Create a simple policy that allows all authenticated users to comment
-- This will fix the immediate issue and we can refine permissions later
DROP POLICY IF EXISTS "Users can comment on accessible entries" ON comments;
CREATE POLICY "Users can comment on accessible entries" ON comments
    FOR INSERT WITH CHECK (
        (SELECT auth.uid()) = user_id
    );

-- Also ensure the select and update policies exist
DROP POLICY IF EXISTS "Users can view comments" ON comments;
CREATE POLICY "Users can view comments" ON comments
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Users can update their own comments" ON comments;
CREATE POLICY "Users can update their own comments" ON comments
    FOR UPDATE USING ((SELECT auth.uid()) = user_id)
    WITH CHECK ((SELECT auth.uid()) = user_id);