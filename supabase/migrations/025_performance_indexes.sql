-- Performance Optimization Indexes for OnlyDiary
-- This migration adds critical indexes to speed up common queries
-- Run AFTER schema fixes (migrations 026-028)

-- ============================================================================
-- USER LOOKUPS (Login, Profile Access)
-- ============================================================================

-- Email lookups (login, password reset)
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Custom URL lookups (onlydiary.app/username)
CREATE INDEX IF NOT EXISTS idx_users_custom_url ON users(custom_url) WHERE custom_url IS NOT NULL;

-- Role-based queries (writers, subscribers, admins)
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- ============================================================================
-- SUBSCRIPTION SYSTEM (Critical for Content Access)
-- ============================================================================
-- Note: Only create if subscriptions table exists with current schema

DO $$
BEGIN
    -- Check if subscriptions table exists and has reader_id column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'subscriptions' AND column_name = 'reader_id'
    ) THEN
        -- Reader-writer subscription lookups
        CREATE INDEX IF NOT EXISTS idx_subscriptions_reader_writer ON subscriptions(reader_id, writer_id);

        -- Active subscription checks
        CREATE INDEX IF NOT EXISTS idx_subscriptions_active_status ON subscriptions(status) WHERE status = 'active';

        -- Writer's subscriber list
        CREATE INDEX IF NOT EXISTS idx_subscriptions_writer_active ON subscriptions(writer_id, status) WHERE status = 'active';

        -- Reader's subscription list
        CREATE INDEX IF NOT EXISTS idx_subscriptions_reader_active ON subscriptions(reader_id, status) WHERE status = 'active';

        -- Stripe subscription ID lookups
        CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_id ON subscriptions(stripe_subscription_id) WHERE stripe_subscription_id IS NOT NULL;
    END IF;

    -- Handle legacy schema if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'subscriptions' AND column_name = 'subscriber_id'
    ) THEN
        -- Legacy subscriber-writer lookups
        CREATE INDEX IF NOT EXISTS idx_subscriptions_subscriber_writer ON subscriptions(subscriber_id, writer_id);

        -- Legacy active subscription checks
        CREATE INDEX IF NOT EXISTS idx_subscriptions_active_until ON subscriptions(active_until) WHERE active_until > NOW();
    END IF;
END $$;

-- ============================================================================
-- DIARY ENTRIES (Content Discovery & Access)
-- ============================================================================

-- Writer's entries (dashboard, profile pages)
CREATE INDEX IF NOT EXISTS idx_diary_entries_user_id ON diary_entries(user_id);

-- Public content discovery (timeline, trending)
CREATE INDEX IF NOT EXISTS idx_diary_entries_public ON diary_entries(created_at DESC, is_hidden) WHERE is_hidden = false;

-- Free content discovery
CREATE INDEX IF NOT EXISTS idx_diary_entries_free ON diary_entries(created_at DESC, is_free, is_hidden) WHERE is_free = true AND is_hidden = false;

-- Entry ID lookups (individual post access)
CREATE INDEX IF NOT EXISTS idx_diary_entries_id ON diary_entries(id);

-- View count sorting (trending content)
CREATE INDEX IF NOT EXISTS idx_diary_entries_view_count ON diary_entries(view_count DESC) WHERE is_hidden = false;

-- ============================================================================
-- COMMENTS SYSTEM
-- ============================================================================

-- Comments on specific diary entries (most common)
CREATE INDEX IF NOT EXISTS idx_comments_diary_entry_id ON comments(diary_entry_id, created_at);

-- User's comment history
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments(user_id, created_at);

-- Non-deleted comments only
CREATE INDEX IF NOT EXISTS idx_comments_active ON comments(diary_entry_id, created_at) WHERE is_deleted = false;

-- ============================================================================
-- PAYMENTS & EARNINGS
-- ============================================================================

-- Writer earnings calculations
CREATE INDEX IF NOT EXISTS idx_payments_writer_id ON payments(writer_id, created_at);

-- Payment type filtering (subscriptions vs donations)
CREATE INDEX IF NOT EXISTS idx_payments_kind ON payments(kind, created_at);

-- Stripe payment ID lookups (webhook processing)
CREATE INDEX IF NOT EXISTS idx_payments_stripe_id ON payments(stripe_payment_id) WHERE stripe_payment_id IS NOT NULL;

-- ============================================================================
-- PHOTOS & MEDIA
-- ============================================================================

-- Photos for diary entries
CREATE INDEX IF NOT EXISTS idx_photos_diary_entry_id ON photos(diary_entry_id);

-- Approved photos only (content display)
CREATE INDEX IF NOT EXISTS idx_photos_approved ON photos(diary_entry_id, moderation_status) WHERE moderation_status = 'approved';

-- ============================================================================
-- NOTIFICATIONS & ENGAGEMENT
-- ============================================================================

-- User notifications (if notifications table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') THEN
        CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id, created_at DESC);
        CREATE INDEX IF NOT EXISTS idx_notifications_unread ON notifications(user_id, read_at) WHERE read_at IS NULL;
    END IF;
END $$;

-- Push subscriptions (if push_subscriptions table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'push_subscriptions') THEN
        CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user_id ON push_subscriptions(user_id);
    END IF;
END $$;

-- ============================================================================
-- INVITES & REFERRALS
-- ============================================================================

-- Invite tracking (if invites table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invites') THEN
        CREATE INDEX IF NOT EXISTS idx_invites_inviter_id ON invites(inviter_id, created_at);
        CREATE INDEX IF NOT EXISTS idx_invites_code ON invites(invite_code);
    END IF;
END $$;

-- ============================================================================
-- PROJECTS & BOOKS (if applicable)
-- ============================================================================

-- Project queries (if projects table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects') THEN
        CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id, created_at);
        CREATE INDEX IF NOT EXISTS idx_projects_public ON projects(is_private, created_at) WHERE is_private = false;
    END IF;
END $$;



-- ============================================================================
-- ANALYTICS INDEXES
-- ============================================================================

-- Created date ranges (for analytics queries)
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_diary_entries_created_at ON diary_entries(created_at);
CREATE INDEX IF NOT EXISTS idx_subscriptions_created_at ON subscriptions(created_at);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- ============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- ============================================================================

-- User content with privacy (dashboard queries)
CREATE INDEX IF NOT EXISTS idx_diary_entries_user_privacy ON diary_entries(user_id, is_hidden, created_at DESC);

-- Subscription status with dates (access control)
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(reader_id, writer_id, status);

-- Public content discovery with engagement
CREATE INDEX IF NOT EXISTS idx_diary_entries_discovery ON diary_entries(is_hidden, is_free, view_count DESC, created_at DESC) WHERE is_hidden = false;

-- ============================================================================
-- VACUUM AND ANALYZE
-- ============================================================================

-- Update table statistics for query planner
ANALYZE users;
ANALYZE diary_entries;
ANALYZE subscriptions;
ANALYZE payments;
ANALYZE comments;
ANALYZE photos;

-- Success message
SELECT 'Performance indexes created successfully! Your OnlyDiary queries should be much faster now.' as status;
