-- Create videos table
CREATE TABLE videos (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id uuid REFERENCES diary_entries(id) ON DELETE CASCADE,
  creator_id uuid REFERENCES users(id) ON DELETE CASCADE,
  r2_file_key VARCHAR(255) NOT NULL,
  r2_public_url VARCHAR(500) NOT NULL,
  file_size INTEGER,
  title text,
  is_free boolean DEFAULT false,
  view_count integer DEFAULT 0,
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now()
);

-- Enable RLS on videos
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;

-- Enable RLS on diary_entries if not already enabled
ALTER TABLE diary_entries ENABLE ROW LEVEL SECURITY;

-- RLS Policy for diary_entries
CREATE POLICY "Allow access to free, own, or subscribed diary entries"
ON diary_entries
FOR SELECT USING (
  is_free = true OR
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM subscriptions
    WHERE reader_id = auth.uid()
    AND writer_id = diary_entries.user_id
    AND status = 'active'
  )
);

-- RLS Policy for videos
CREATE POLICY "Allow access to video if free, owner, or subscribed"
ON videos
FOR SELECT USING (
  is_free = true OR
  creator_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM subscriptions
    JOIN diary_entries ON diary_entries.id = videos.post_id
    WHERE reader_id = auth.uid()
    AND writer_id = diary_entries.user_id
    AND status = 'active'
  )
);

-- Policy for inserting videos (creators only)
CREATE POLICY "Allow creators to insert their own videos"
ON videos
FOR INSERT
WITH CHECK (creator_id = auth.uid());

-- Policy for updating videos (creators only)
CREATE POLICY "Allow creators to update their own videos"
ON videos
FOR UPDATE
USING (creator_id = auth.uid());

-- Update subscriptions table structure if needed
CREATE TABLE IF NOT EXISTS subscriptions (
  reader_id uuid REFERENCES users(id) ON DELETE CASCADE,
  writer_id uuid REFERENCES users(id) ON DELETE CASCADE,
  status text DEFAULT 'active',
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now(),
  PRIMARY KEY (reader_id, writer_id)
);

-- Enable RLS on subscriptions
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- RLS Policy for subscriptions
CREATE POLICY "Users can view their own subscriptions"
ON subscriptions
FOR SELECT
USING (reader_id = auth.uid() OR writer_id = auth.uid());
