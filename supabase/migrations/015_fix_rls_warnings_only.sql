-- Fix RLS performance issues for the exact policies mentioned in warnings
-- Replace auth.uid() with (select auth.uid()) to prevent re-evaluation for each row

-- Fix diary_entries policies
DROP POLICY IF EXISTS "Writers can manage their own entries" ON diary_entries;
CREATE POLICY "Writers can manage their own entries" ON diary_entries
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "<PERSON><PERSON> can view all entries" ON diary_entries;
CREATE POLICY "Ad<PERSON> can view all entries" ON diary_entries
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Fix photos policies
DROP POLICY IF EXISTS "Writers can manage photos for their entries" ON photos;
CREATE POLICY "Writers can manage photos for their entries" ON photos
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id AND user_id = (select auth.uid())
        )
    );

DROP POLICY IF EXISTS "Ad<PERSON> can view all photos" ON photos;
CREATE POLICY "Ad<PERSON> can view all photos" ON photos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Fix payments policies
DROP POLICY IF EXISTS "Users can view their payment history" ON payments;
CREATE POLICY "Users can view their payment history" ON payments
    FOR SELECT USING (
        (select auth.uid()) = payer_id OR (select auth.uid()) = writer_id
    );

DROP POLICY IF EXISTS "Admins can view all payments" ON payments;
CREATE POLICY "Admins can view all payments" ON payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Fix post_reads policies
DROP POLICY IF EXISTS "Users can create read records" ON post_reads;
CREATE POLICY "Users can create read records" ON post_reads
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- Fix loves policies if table exists
DROP POLICY IF EXISTS "Users can manage their own loves" ON loves;
CREATE POLICY "Users can manage their own loves" ON loves
    FOR ALL USING ((select auth.uid()) = user_id);

-- Fix additional post_reads policy mentioned in warnings
DROP POLICY IF EXISTS "Writers can view reads of their content" ON post_reads;
CREATE POLICY "Writers can view reads of their content" ON post_reads
    FOR SELECT USING ((select auth.uid()) = writer_id);

-- Fix bookmarks table multiple permissive policies issue
DROP POLICY IF EXISTS "Creators can view their bookmarks" ON bookmarks;
DROP POLICY IF EXISTS "Readers can manage their bookmarks" ON bookmarks;
DROP POLICY IF EXISTS "Users can manage bookmarks" ON bookmarks;

-- Create single optimized policy for bookmarks
CREATE POLICY "Users can manage bookmarks" ON bookmarks
    FOR ALL USING (
        (select auth.uid()) = reader_id OR (select auth.uid()) = creator_id
    );

-- Fix duplicate indexes on loves table
DROP INDEX IF EXISTS idx_loves_entry;
