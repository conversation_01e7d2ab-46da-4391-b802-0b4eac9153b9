-- Enable Row Level Security on comments
ALTER TABLE public.comments
  ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments
  FORCE ROW LEVEL SECURITY;

-- <PERSON><PERSON> can moderate all comments
DROP POLICY IF EXISTS "Ad<PERSON> can moderate all comments" ON public.comments;
CREATE POLICY "<PERSON><PERSON> can moderate all comments" ON public.comments
  FOR ALL
  USING (
    EXISTS (
      SELECT 1
      FROM public.users u
      WHERE u.id = (SELECT auth.uid())::uuid
        AND u.role = 'admin'
    )
  );

-- Anyone can view non-deleted comments
DROP POLICY IF EXISTS "Anyone can view non-deleted comments" ON public.comments;
CREATE POLICY "Anyone can view non-deleted comments" ON public.comments
  FOR SELECT
  USING (
    is_deleted = false
  );

-- Users can view comments
DROP POLICY IF EXISTS "Users can view comments" ON public.comments;
CREATE POLICY "Users can view comments" ON public.comments
  FOR SELECT
  USING (true);

-- Users can comment on accessible entries
DROP POLICY IF EXISTS "Users can comment on accessible entries" ON public.comments;
CREATE POLICY "Users can comment on accessible entries" ON public.comments
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1
      FROM public.diary_entries d
      WHERE d.id = public.comments.diary_entry_id
        AND (
          d.is_free = true
          OR EXISTS (
            SELECT 1
            FROM public.subscriptions s
            WHERE s.subscriber_id = (SELECT auth.uid())::uuid
              AND s.writer_id = d.user_id
              AND s.active_until >= NOW()
          )
        )
    )
  );

-- Users can update their own comments
DROP POLICY IF EXISTS "Users can update their own comments" ON public.comments;
CREATE POLICY "Users can update their own comments" ON public.comments
  FOR UPDATE
  USING (
    user_id = (SELECT auth.uid())::uuid
  );

-- Writers can moderate comments on their entries (update)
DROP POLICY IF EXISTS "Writers can moderate comments on their entries (update)" ON public.comments;
CREATE POLICY "Writers can moderate comments on their entries (update)" ON public.comments
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1
      FROM public.diary_entries d
      WHERE d.id = public.comments.diary_entry_id
        AND d.user_id = (SELECT auth.uid())::uuid
    )
  );

-- Writers can moderate comments on their entries (delete)
DROP POLICY IF EXISTS "Writers can moderate comments on their entries (delete)" ON public.comments;
CREATE POLICY "Writers can moderate comments on their entries (delete)" ON public.comments
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1
      FROM public.diary_entries d
      WHERE d.id = public.comments.diary_entry_id
        AND d.user_id = (SELECT auth.uid())::uuid
    )
  );
