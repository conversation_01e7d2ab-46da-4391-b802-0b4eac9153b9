-- Enable Row Level Security on comments table
-- This fixes the security warning where RLS policies exist but <PERSON><PERSON> is not enabled

ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- Fix search_path for functions to prevent search_path injection attacks
ALTER FUNCTION get_entry_preview(UUID, UUID) SET search_path = '';
ALTER FUNCTION notify_comment_created() SET search_path = '';
ALTER FUNCTION user_can_comment_on_entry(UUID, UUID) SET search_path = '';