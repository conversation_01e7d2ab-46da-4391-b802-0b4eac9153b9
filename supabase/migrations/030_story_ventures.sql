-- Add Story Ventures crowdfunding functionality to diary entries
-- This migration adds crowdfunding capabilities to diary entries

-- Add Story Ventures fields to diary_entries table
ALTER TABLE diary_entries ADD COLUMN IF NOT EXISTS is_story_venture BOOLEAN DEFAULT FALSE;
ALTER TABLE diary_entries ADD COLUMN IF NOT EXISTS funding_goal_cents INTEGER CHECK (funding_goal_cents >= 100 OR funding_goal_cents IS NULL);
ALTER TABLE diary_entries ADD COLUMN IF NOT EXISTS funding_raised_cents INTEGER DEFAULT 0 CHECK (funding_raised_cents >= 0);
ALTER TABLE diary_entries ADD COLUMN IF NOT EXISTS funding_deadline TIMESTAMP WITH TIME ZONE;
ALTER TABLE diary_entries ADD COLUMN IF NOT EXISTS show_funding_publicly BOOLEAN DEFAULT TRUE;
ALTER TABLE diary_entries ADD COLUMN IF NOT EXISTS story_venture_description TEXT;

-- Create story_venture_backers table to track supporters with privacy options
CREATE TABLE IF NOT EXISTS story_venture_backers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
    backer_id UUID REFERENCES users(id) ON DELETE SET NULL,
    backer_name TEXT, -- Store name at time of backing for display
    amount_cents INTEGER NOT NULL CHECK (amount_cents >= 100),
    message TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    show_publicly BOOLEAN DEFAULT FALSE, -- Backer chooses to be public
    stripe_payment_intent_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate backing records for same payment
    UNIQUE(stripe_payment_intent_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_diary_entries_story_venture ON diary_entries(is_story_venture) WHERE is_story_venture = TRUE;
CREATE INDEX IF NOT EXISTS idx_diary_entries_funding_deadline ON diary_entries(funding_deadline) WHERE funding_deadline IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_story_venture_backers_entry_id ON story_venture_backers(diary_entry_id);
CREATE INDEX IF NOT EXISTS idx_story_venture_backers_backer_id ON story_venture_backers(backer_id);
CREATE INDEX IF NOT EXISTS idx_story_venture_backers_created_at ON story_venture_backers(created_at DESC);

-- Enable RLS on story_venture_backers
ALTER TABLE story_venture_backers ENABLE ROW LEVEL SECURITY;

-- RLS Policies for story_venture_backers
-- Anyone can view public backers for published entries
CREATE POLICY "Anyone can view public story venture backers" ON story_venture_backers
    FOR SELECT USING (
        show_publicly = TRUE 
        AND EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id 
            AND is_hidden = FALSE
        )
    );

-- Backers can view their own backing records
CREATE POLICY "Backers can view their own backing records" ON story_venture_backers
    FOR SELECT USING (auth.uid() = backer_id);

-- Entry creators can view all backers for their entries
CREATE POLICY "Creators can view all backers for their entries" ON story_venture_backers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id 
            AND user_id = auth.uid()
        )
    );

-- Only the system can insert/update backer records (via API)
CREATE POLICY "System can manage backer records" ON story_venture_backers
    FOR ALL USING (FALSE); -- Will be handled by service role

-- Create function to get story venture stats
CREATE OR REPLACE FUNCTION get_story_venture_stats(entry_id UUID)
RETURNS TABLE (
    total_raised_cents INTEGER,
    backer_count INTEGER,
    goal_cents INTEGER,
    funding_percentage NUMERIC,
    days_remaining INTEGER,
    is_funded BOOLEAN,
    public_backers_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(svb.amount_cents), 0)::INTEGER as total_raised_cents,
        COUNT(svb.id)::INTEGER as backer_count,
        de.funding_goal_cents as goal_cents,
        CASE 
            WHEN de.funding_goal_cents > 0 THEN 
                ROUND((COALESCE(SUM(svb.amount_cents), 0) * 100.0 / de.funding_goal_cents), 2)
            ELSE 0
        END as funding_percentage,
        CASE 
            WHEN de.funding_deadline IS NOT NULL THEN 
                EXTRACT(DAYS FROM (de.funding_deadline - NOW()))::INTEGER
            ELSE NULL
        END as days_remaining,
        CASE 
            WHEN de.funding_goal_cents IS NOT NULL AND de.funding_goal_cents > 0 THEN
                COALESCE(SUM(svb.amount_cents), 0) >= de.funding_goal_cents
            ELSE FALSE
        END as is_funded,
        COUNT(CASE WHEN svb.show_publicly = TRUE THEN 1 END)::INTEGER as public_backers_count
    FROM diary_entries de
    LEFT JOIN story_venture_backers svb ON de.id = svb.diary_entry_id
    WHERE de.id = entry_id
    GROUP BY de.id, de.funding_goal_cents, de.funding_deadline;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get public backers for display
CREATE OR REPLACE FUNCTION get_public_story_venture_backers(entry_id UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    backer_name TEXT,
    amount_cents INTEGER,
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CASE 
            WHEN svb.is_anonymous = TRUE THEN 'Anonymous'
            WHEN svb.show_publicly = TRUE THEN COALESCE(svb.backer_name, 'Anonymous')
            ELSE 'Anonymous'
        END as backer_name,
        CASE 
            WHEN svb.show_publicly = TRUE THEN svb.amount_cents
            ELSE NULL
        END as amount_cents,
        CASE 
            WHEN svb.show_publicly = TRUE AND svb.message IS NOT NULL THEN svb.message
            ELSE NULL
        END as message,
        svb.created_at
    FROM story_venture_backers svb
    JOIN diary_entries de ON svb.diary_entry_id = de.id
    WHERE svb.diary_entry_id = entry_id
    AND de.is_hidden = FALSE
    AND de.show_funding_publicly = TRUE
    ORDER BY svb.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update get_entry_preview function to include Story Ventures fields
CREATE OR REPLACE FUNCTION get_entry_preview(
    entry_id UUID,
    viewer_id UUID DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    title TEXT,
    body_md TEXT,
    is_free BOOLEAN,
    is_hidden BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    user_id UUID,
    writer_id UUID,
    writer_name TEXT,
    writer_price INTEGER,
    can_read_full BOOLEAN,
    is_story_venture BOOLEAN,
    funding_goal_cents INTEGER,
    funding_raised_cents INTEGER,
    funding_deadline TIMESTAMP WITH TIME ZONE,
    show_funding_publicly BOOLEAN,
    story_venture_description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        de.id,
        de.title,
        de.body_md,
        de.is_free,
        de.is_hidden,
        de.created_at,
        de.user_id,
        de.user_id as writer_id,
        u.name as writer_name,
        COALESCE(u.price_monthly, 999) as writer_price,
        CASE
            -- Entry is free
            WHEN de.is_free = true THEN true
            -- Viewer is the writer
            WHEN viewer_id = de.user_id THEN true
            -- Viewer has credits for this writer
            WHEN viewer_id IS NOT NULL AND EXISTS(
                SELECT 1 FROM post_credits pc
                WHERE pc.user_id = viewer_id
                AND pc.writer_id = de.user_id
                AND pc.credits_remaining > 0
            ) THEN true
            -- Viewer has already read this post
            WHEN viewer_id IS NOT NULL AND EXISTS(
                SELECT 1 FROM post_reads pr
                WHERE pr.user_id = viewer_id
                AND pr.diary_entry_id = de.id
            ) THEN true
            -- Default: cannot read full
            ELSE false
        END as can_read_full,
        -- Story Ventures fields
        COALESCE(de.is_story_venture, false) as is_story_venture,
        de.funding_goal_cents,
        COALESCE(de.funding_raised_cents, 0) as funding_raised_cents,
        de.funding_deadline,
        COALESCE(de.show_funding_publicly, true) as show_funding_publicly,
        de.story_venture_description
    FROM diary_entries de
    JOIN users u ON de.user_id = u.id
    WHERE de.id = entry_id
    AND de.is_hidden = false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment to track migration purpose
COMMENT ON TABLE story_venture_backers IS 'Tracks backers for Story Ventures crowdfunding campaigns on diary entries';
COMMENT ON FUNCTION get_story_venture_stats IS 'Returns comprehensive funding statistics for a Story Venture';
COMMENT ON FUNCTION get_public_story_venture_backers IS 'Returns public backer information for display with privacy controls';
