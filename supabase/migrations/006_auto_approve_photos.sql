-- Function to auto-approve photos that have been pending for more than 5 minutes
CREATE OR REPLACE FUNCTION auto_approve_pending_photos()
RETURNS void AS $$
BEGIN
    UPDATE photos 
    SET moderation_status = 'approved',
        rekognition_labels = jsonb_build_object(
            'auto_approved', true,
            'reason', 'Auto-approved after timeout',
            'processed_at', NOW()
        )
    WHERE moderation_status = 'pending' 
    AND created_at < NOW() - INTERVAL '5 minutes';
    
    -- Log how many photos were auto-approved
    RAISE NOTICE 'Auto-approved % photos that were pending for more than 5 minutes', 
        (SELECT COUNT(*) FROM photos WHERE moderation_status = 'approved' AND rekognition_labels->>'auto_approved' = 'true');
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run this function every minute
-- Note: This requires the pg_cron extension to be enabled in Supabase
-- You can also call this function manually or via an API endpoint

-- Grant execute permission
GRANT EXECUTE ON FUNCTION auto_approve_pending_photos() TO authenticated;
GRANT EXECUTE ON FUNCTION auto_approve_pending_photos() TO service_role;
