-- Add withdrawal status enum
CREATE TYPE withdrawal_status AS ENUM ('pending', 'processing', 'completed', 'rejected');

-- Withdrawals table
CREATE TABLE withdrawals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    writer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    amount_cents INTEGER NOT NULL CHECK (amount_cents >= 1000), -- Minimum $10
    status withdrawal_status DEFAULT 'pending',
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    stripe_transfer_id TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for withdrawals
ALTER TABLE withdrawals ENABLE ROW LEVEL SECURITY;

-- Writers can view their own withdrawals
CREATE POLICY "Writers can view their own withdrawals" ON withdrawals
    FOR SELECT USING (auth.uid() = writer_id);

-- Writers can create withdrawal requests
CREATE POLICY "Writers can create withdrawal requests" ON withdrawals
    FOR INSERT WITH CHECK (auth.uid() = writer_id);

-- Admins can view and manage all withdrawals
CREATE POLICY "Admins can manage all withdrawals" ON withdrawals
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Add indexes for performance
CREATE INDEX idx_withdrawals_writer_id ON withdrawals(writer_id);
CREATE INDEX idx_withdrawals_status ON withdrawals(status);
CREATE INDEX idx_withdrawals_requested_at ON withdrawals(requested_at);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_withdrawals_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_withdrawals_updated_at_trigger
    BEFORE UPDATE ON withdrawals
    FOR EACH ROW EXECUTE FUNCTION update_withdrawals_updated_at();
