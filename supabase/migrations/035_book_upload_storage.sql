-- Storage setup for book uploads
-- Create storage bucket for book files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'book-uploads',
    'book-uploads',
    false,
    52428800, -- 50MB limit
    ARRAY[
        'application/pdf',
        'application/epub+zip',
        'application/x-mobipocket-ebook',
        'text/plain'
    ]
) ON CONFLICT (id) DO NOTHING;

-- RLS policies for book uploads bucket
CREATE POLICY "Authenticated users can upload books" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'book-uploads' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Users can read their uploaded books" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'book-uploads' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their uploaded books" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'book-uploads' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Function to clean up old upload files after processing
CREATE OR REPLACE FUNCTION cleanup_processed_uploads()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Delete upload files for books that have been successfully processed
    -- and are older than 7 days
    DELETE FROM storage.objects
    WHERE bucket_id = 'book-uploads'
    AND created_at < NOW() - INTERVAL '7 days'
    AND name IN (
        SELECT file_path
        FROM projects
        WHERE processing_status = 'completed'
        AND created_at < NOW() - INTERVAL '7 days'
    );
END;
$$;

-- Create a scheduled job to run cleanup weekly (if pg_cron is available)
-- SELECT cron.schedule('cleanup-uploads', '0 2 * * 0', 'SELECT cleanup_processed_uploads();');
