-- Create bookmarks system for readers to follow creators

CREATE TABLE IF NOT EXISTS bookmarks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  reader_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  creator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- One bookmark per reader-creator pair
  CONSTRAINT unique_reader_creator_bookmark UNIQUE (reader_id, creator_id)
);

-- Add indexes for performance
CREATE INDEX idx_bookmarks_reader_id ON bookmarks(reader_id);
CREATE INDEX idx_bookmarks_creator_id ON bookmarks(creator_id);
CREATE INDEX idx_bookmarks_created_at ON bookmarks(created_at);

-- Add RLS policies
ALTER TABLE bookmarks ENABLE ROW LEVEL SECURITY;

-- Readers can manage their own bookmarks
CREATE POLICY "Readers can manage their bookmarks" ON bookmarks
  FOR ALL USING (auth.uid() = reader_id);

-- Creators can view who bookmarked them
CREATE POLICY "Creators can view their bookmarks" ON bookmarks
  FOR SELECT USING (auth.uid() = creator_id);

-- Add bookmark count to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS bookmark_count INTEGER DEFAULT 0;

-- Function to update bookmark count
CREATE OR REPLACE FUNCTION update_bookmark_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE users 
    SET bookmark_count = bookmark_count + 1 
    WHERE id = NEW.creator_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE users 
    SET bookmark_count = GREATEST(bookmark_count - 1, 0) 
    WHERE id = OLD.creator_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update bookmark count
CREATE TRIGGER update_bookmark_count_trigger
  AFTER INSERT OR DELETE ON bookmarks
  FOR EACH ROW
  EXECUTE FUNCTION update_bookmark_count();

-- Initialize bookmark counts for existing users
UPDATE users SET bookmark_count = (
  SELECT COUNT(*) FROM bookmarks WHERE creator_id = users.id
);

SELECT 'Bookmarks system created successfully!' as status;
