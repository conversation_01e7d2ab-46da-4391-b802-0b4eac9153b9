-- Create test subscriptions for subscriber user
-- Subscriber ID from the console logs: aaa7e8be-277c-459e-87aa-c4b736cc143e

-- First, let's find some writers with content
WITH writers_with_content AS (
  SELECT DISTINCT u.id, u.name, u.email, COUNT(de.id) as entry_count
  FROM users u
  INNER JOIN diary_entries de ON de.user_id = u.id
  WHERE u.role = 'writer' 
    AND de.is_published = true
    AND u.id != 'aaa7e8be-277c-459e-87aa-c4b736cc143e' -- not the subscriber
  GROUP BY u.id, u.name, u.email
  ORDER BY entry_count DESC
  LIMIT 5
)
-- Create subscriptions to these writers
INSERT INTO subscriptions (reader_id, writer_id, status, current_period_start, current_period_end)
SELECT 
  'aaa7e8be-277c-459e-87aa-c4b736cc143e' as reader_id,
  id as writer_id,
  'active' as status,
  NOW() as current_period_start,
  NOW() + INTERVAL '30 days' as current_period_end
FROM writers_with_content
ON CONFLICT (reader_id, writer_id) 
DO UPDATE SET 
  status = 'active',
  current_period_start = NOW(),
  current_period_end = NOW() + INTERVAL '30 days',
  updated_at = NOW();

-- Show the subscriptions we created
SELECT 
  s.id,
  u.name as writer_name,
  u.email as writer_email,
  s.status,
  s.current_period_end,
  (SELECT COUNT(*) FROM diary_entries WHERE user_id = s.writer_id AND is_published = true) as published_entries
FROM subscriptions s
INNER JOIN users u ON u.id = s.writer_id
WHERE s.reader_id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e'
  AND s.status = 'active';