-- Check if projects table exists
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name = 'projects'
) as projects_table_exists;

-- Check if project_waitlist table exists
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name = 'project_waitlist'
) as waitlist_table_exists;

-- If projects table exists, show sample data
SELECT 
    id, 
    title, 
    is_private, 
    user_id,
    created_at 
FROM projects 
LIMIT 5;

-- If waitlist table exists, show structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'project_waitlist'
ORDER BY ordinal_position;
