-- Complete ebook schema fix for OnlyDiary
-- This script adds all missing ebook-related columns to the projects table

-- Add missing columns if they don't exist
DO $$ 
BEGIN
    -- Check and add is_ebook column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'is_ebook') THEN
        ALTER TABLE projects ADD COLUMN is_ebook BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added is_ebook column';
    ELSE
        RAISE NOTICE 'is_ebook column already exists';
    END IF;

    -- Check and add book_type column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'book_type') THEN
        ALTER TABLE projects ADD COLUMN book_type TEXT;
        RAISE NOTICE 'Added book_type column';
    ELSE
        RAISE NOTICE 'book_type column already exists';
    END IF;

    -- Check and add average_rating column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'average_rating') THEN
        ALTER TABLE projects ADD COLUMN average_rating DECIMAL(3,2) DEFAULT 0;
        RAISE NOTICE 'Added average_rating column';
    ELSE
        RAISE NOTICE 'average_rating column already exists';
    END IF;

    -- Check and add review_count column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'review_count') THEN
        ALTER TABLE projects ADD COLUMN review_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added review_count column';
    ELSE
        RAISE NOTICE 'review_count column already exists';
    END IF;

    -- Check and add sales_count column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'sales_count') THEN
        ALTER TABLE projects ADD COLUMN sales_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added sales_count column';
    ELSE
        RAISE NOTICE 'sales_count column already exists';
    END IF;

    -- Check and add tags column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'tags') THEN
        ALTER TABLE projects ADD COLUMN tags TEXT[] DEFAULT '{}';
        RAISE NOTICE 'Added tags column';
    ELSE
        RAISE NOTICE 'tags column already exists';
    END IF;

    -- Check and add slug column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'slug') THEN
        ALTER TABLE projects ADD COLUMN slug TEXT;
        RAISE NOTICE 'Added slug column';
    ELSE
        RAISE NOTICE 'slug column already exists';
    END IF;

    -- Check and add author_name column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'author_name') THEN
        ALTER TABLE projects ADD COLUMN author_name TEXT;
        RAISE NOTICE 'Added author_name column';
    ELSE
        RAISE NOTICE 'author_name column already exists';
    END IF;

    -- Check and add price_amount column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'price_amount') THEN
        ALTER TABLE projects ADD COLUMN price_amount INTEGER DEFAULT 0;
        RAISE NOTICE 'Added price_amount column';
    ELSE
        RAISE NOTICE 'price_amount column already exists';
    END IF;
END $$;

-- Update existing ebooks to have proper values
UPDATE projects 
SET 
    is_ebook = true,
    book_type = COALESCE(book_type, genre, 'fiction'),
    average_rating = COALESCE(average_rating, 0),
    review_count = COALESCE(review_count, 0),
    sales_count = COALESCE(sales_count, 0),
    tags = COALESCE(tags, '{}'),
    author_name = COALESCE(author_name, (SELECT name FROM users WHERE users.id = projects.user_id LIMIT 1)),
    price_amount = COALESCE(price_amount, 0)
WHERE file_url IS NOT NULL 
   OR cover_image_url IS NOT NULL;

-- Generate slugs for books that don't have them
UPDATE projects 
SET slug = LOWER(REGEXP_REPLACE(REGEXP_REPLACE(title, '[^a-zA-Z0-9\s]', '', 'g'), '\s+', '-', 'g'))
WHERE slug IS NULL AND title IS NOT NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_is_ebook ON projects(is_ebook);
CREATE INDEX IF NOT EXISTS idx_projects_book_type ON projects(book_type);
CREATE INDEX IF NOT EXISTS idx_projects_genre ON projects(genre);
CREATE INDEX IF NOT EXISTS idx_projects_price_amount ON projects(price_amount);
CREATE INDEX IF NOT EXISTS idx_projects_sales_count ON projects(sales_count);
CREATE INDEX IF NOT EXISTS idx_projects_average_rating ON projects(average_rating);
CREATE INDEX IF NOT EXISTS idx_projects_slug ON projects(slug);

-- Show final schema
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'projects' 
    AND column_name IN (
        'is_ebook', 'book_type', 'average_rating', 'review_count', 
        'sales_count', 'tags', 'slug', 'author_name', 'price_amount'
    )
ORDER BY column_name;

-- Show sample data
SELECT 
    id, 
    title, 
    is_ebook, 
    book_type, 
    price_amount, 
    sales_count, 
    average_rating,
    slug,
    author_name
FROM projects 
WHERE is_ebook = true 
LIMIT 5;
