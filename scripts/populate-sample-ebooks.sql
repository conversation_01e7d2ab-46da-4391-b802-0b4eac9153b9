-- Sample data to populate ebooks for testing the book store
-- Run this after the main migrations to add test data

-- First, let's update some existing complete projects to be ebooks
UPDATE projects 
SET 
  is_ebook = true,
  book_type = COALESCE(genre, 'fiction'),
  average_rating = ROUND((RANDOM() * 4 + 1)::numeric, 2), -- Random rating between 1-5
  review_count = FLOOR(RANDOM() * 50 + 1)::integer, -- Random review count 1-50
  sales_count = FLOOR(RANDOM() * 100 + 5)::integer, -- Random sales 5-105
  slug = LOWER(REGEXP_REPLACE(REGEXP_REPLACE(title, '[^a-zA-Z0-9\s]', '', 'g'), '\s+', '-', 'g')),
  tags = ARRAY['bestseller', 'featured'],
  meta_description = LEFT(description, 160)
WHERE is_complete = true 
  AND price_amount IS NOT NULL 
  AND price_amount > 0
  AND is_ebook IS NOT TRUE;

-- Insert some sample ebooks if no projects exist
INSERT INTO projects (
  user_id,
  title,
  description,
  genre,
  book_type,
  is_complete,
  is_ebook,
  price_amount,
  average_rating,
  review_count,
  sales_count,
  slug,
  tags,
  meta_description
) 
SELECT 
  u.id,
  'Sample Book: ' || u.name || '''s Story',
  'A compelling narrative that explores the depths of human experience through personal storytelling. This book offers readers an intimate look into real-life experiences.',
  'memoir',
  'memoir',
  true,
  true,
  CASE 
    WHEN RANDOM() < 0.3 THEN 0 -- 30% free books
    ELSE FLOOR(RANDOM() * 2000 + 500)::integer -- $5-25 range
  END,
  ROUND((RANDOM() * 4 + 1)::numeric, 2),
  FLOOR(RANDOM() * 30 + 1)::integer,
  FLOOR(RANDOM() * 80 + 1)::integer,
  'sample-book-' || LOWER(REPLACE(u.name, ' ', '-')) || '-story',
  ARRAY['memoir', 'personal', 'inspiring'],
  'A compelling narrative that explores the depths of human experience through personal storytelling.'
FROM users u 
WHERE u.role = 'writer'
  AND NOT EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.user_id = u.id 
    AND p.is_ebook = true
  )
LIMIT 5;

-- Add some sample book reviews
INSERT INTO book_reviews (
  project_id,
  user_id,
  rating,
  review_text,
  is_verified_purchase
)
SELECT 
  p.id,
  u.id,
  FLOOR(RANDOM() * 10 + 1)::integer, -- 1-10 pen rating
  CASE FLOOR(RANDOM() * 5)
    WHEN 0 THEN 'Amazing book! Really touched my heart and made me think differently about life.'
    WHEN 1 THEN 'Well written and engaging. The author has a unique voice that draws you in.'
    WHEN 2 THEN 'Incredible storytelling. I couldn''t put it down once I started reading.'
    WHEN 3 THEN 'Beautiful and honest writing. This book will stay with me for a long time.'
    ELSE 'Highly recommend! A must-read for anyone interested in authentic personal stories.'
  END,
  RANDOM() < 0.7 -- 70% are verified purchases
FROM projects p
CROSS JOIN users u
WHERE p.is_ebook = true 
  AND p.is_complete = true
  AND u.role IN ('subscriber', 'writer')
  AND u.id != p.user_id
  AND RANDOM() < 0.3 -- Only 30% of possible combinations to avoid too many reviews
LIMIT 50;

-- Add some sample book purchases
INSERT INTO book_purchases (
  project_id,
  user_id,
  amount_paid,
  status
)
SELECT 
  p.id,
  u.id,
  p.price_amount,
  'completed'
FROM projects p
CROSS JOIN users u
WHERE p.is_ebook = true 
  AND p.is_complete = true
  AND p.price_amount > 0
  AND u.role IN ('subscriber', 'writer')
  AND u.id != p.user_id
  AND RANDOM() < 0.2 -- Only 20% of possible combinations
LIMIT 30;

-- Update sales counts based on actual purchases
UPDATE projects 
SET sales_count = (
  SELECT COUNT(*) 
  FROM book_purchases 
  WHERE project_id = projects.id 
    AND status = 'completed'
)
WHERE is_ebook = true;

-- Update review counts and average ratings based on actual reviews
UPDATE projects 
SET 
  review_count = (
    SELECT COUNT(*) 
    FROM book_reviews 
    WHERE project_id = projects.id
  ),
  average_rating = (
    SELECT COALESCE(AVG(rating), 0) 
    FROM book_reviews 
    WHERE project_id = projects.id
  )
WHERE is_ebook = true;
