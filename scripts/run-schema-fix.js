const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function runSchemaFix() {
  try {
    // First, let's check what columns currently exist
    console.log('Checking current schema...');
    
    const { data: schemaData, error: schemaError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_name', 'projects')
      .in('column_name', ['is_ebook', 'book_type', 'average_rating', 'review_count', 'sales_count', 'tags', 'slug', 'author_name', 'price_amount']);
    
    if (schemaError) {
      console.error('Error checking schema:', schemaError);
    } else {
      console.log('Current ebook-related columns:', schemaData);
    }
    
    // Now let's try a simple query to see what's failing
    console.log('Testing books query...');
    
    const { data: testData, error: testError } = await supabase
      .from('projects')
      .select('id, title, is_ebook, book_type, price_amount')
      .eq('is_ebook', true)
      .limit(1);
    
    if (testError) {
      console.error('Test query error:', testError);
      console.log('This confirms the schema issue. Let me add the missing columns...');
      
      // Add missing columns one by one
      const columnsToAdd = [
        { name: 'is_ebook', type: 'BOOLEAN DEFAULT false' },
        { name: 'book_type', type: 'TEXT' },
        { name: 'average_rating', type: 'DECIMAL(3,2) DEFAULT 0' },
        { name: 'review_count', type: 'INTEGER DEFAULT 0' },
        { name: 'sales_count', type: 'INTEGER DEFAULT 0' },
        { name: 'tags', type: 'TEXT[] DEFAULT \'{}\'::TEXT[]' },
        { name: 'slug', type: 'TEXT' },
        { name: 'author_name', type: 'TEXT' },
        { name: 'price_amount', type: 'INTEGER DEFAULT 0' }
      ];
      
      for (const column of columnsToAdd) {
        try {
          const { error: addError } = await supabase.rpc('exec_sql', {
            sql_query: `ALTER TABLE projects ADD COLUMN IF NOT EXISTS ${column.name} ${column.type};`
          });
          
          if (addError) {
            console.error(`Error adding ${column.name}:`, addError);
          } else {
            console.log(`✅ Added/verified column: ${column.name}`);
          }
        } catch (err) {
          console.error(`Error with column ${column.name}:`, err);
        }
      }
      
    } else {
      console.log('✅ Test query successful:', testData);
    }
    
  } catch (err) {
    console.error('Script error:', err);
  }
}

runSchemaFix();
