-- Create reader credits system for pay-per-post model

-- Add bundle_count to diary_entries for multi-part posts
ALTER TABLE diary_entries ADD COLUMN IF NOT EXISTS bundle_count INTEGER DEFAULT 1;

-- Create reader_credits table
CREATE TABLE IF NOT EXISTS reader_credits (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  reader_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  writer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  credits_remaining INTEGER DEFAULT 30,
  last_reset DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- One credit record per reader-writer pair
  CONSTRAINT unique_reader_writer UNIQUE (reader_id, writer_id)
);

-- Add indexes for performance
CREATE INDEX idx_reader_credits_reader_id ON reader_credits(reader_id);
CREATE INDEX idx_reader_credits_writer_id ON reader_credits(writer_id);
CREATE INDEX idx_reader_credits_last_reset ON reader_credits(last_reset);

-- Add RLS policies
ALTER TABLE reader_credits ENABLE ROW LEVEL SECURITY;

-- Readers can view and update their own credits
CREATE POLICY "Readers can manage their credits" ON reader_credits
  FOR ALL USING (auth.uid() = reader_id);

-- Writers can view credits for their content
CREATE POLICY "Writers can view their reader credits" ON reader_credits
  FOR SELECT USING (auth.uid() = writer_id);

-- Function to reset monthly credits
CREATE OR REPLACE FUNCTION reset_monthly_credits()
RETURNS void AS $$
BEGIN
  UPDATE reader_credits 
  SET 
    credits_remaining = 30,
    last_reset = CURRENT_DATE,
    updated_at = NOW()
  WHERE last_reset < CURRENT_DATE - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Function to initialize credits when reader subscribes
CREATE OR REPLACE FUNCTION initialize_reader_credits(
  p_reader_id UUID,
  p_writer_id UUID
)
RETURNS void AS $$
BEGIN
  INSERT INTO reader_credits (reader_id, writer_id, credits_remaining, last_reset)
  VALUES (p_reader_id, p_writer_id, 30, CURRENT_DATE)
  ON CONFLICT (reader_id, writer_id) 
  DO UPDATE SET 
    credits_remaining = 30,
    last_reset = CURRENT_DATE,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to use credits for reading
CREATE OR REPLACE FUNCTION use_reading_credits(
  p_reader_id UUID,
  p_writer_id UUID,
  p_credits_needed INTEGER DEFAULT 1
)
RETURNS BOOLEAN AS $$
DECLARE
  current_credits INTEGER;
BEGIN
  -- Get current credits
  SELECT credits_remaining INTO current_credits
  FROM reader_credits
  WHERE reader_id = p_reader_id AND writer_id = p_writer_id;
  
  -- Check if user has enough credits
  IF current_credits IS NULL OR current_credits < p_credits_needed THEN
    RETURN FALSE;
  END IF;
  
  -- Deduct credits
  UPDATE reader_credits
  SET 
    credits_remaining = credits_remaining - p_credits_needed,
    updated_at = NOW()
  WHERE reader_id = p_reader_id AND writer_id = p_writer_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create reading_history table to track what readers have accessed
CREATE TABLE IF NOT EXISTS reading_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  reader_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  diary_entry_id UUID NOT NULL REFERENCES diary_entries(id) ON DELETE CASCADE,
  credits_used INTEGER DEFAULT 1,
  read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent duplicate reads
  CONSTRAINT unique_reader_entry UNIQUE (reader_id, diary_entry_id)
);

-- Add indexes for reading history
CREATE INDEX idx_reading_history_reader_id ON reading_history(reader_id);
CREATE INDEX idx_reading_history_entry_id ON reading_history(diary_entry_id);
CREATE INDEX idx_reading_history_read_at ON reading_history(read_at);

-- Add RLS policies for reading history
ALTER TABLE reading_history ENABLE ROW LEVEL SECURITY;

-- Readers can view their own reading history
CREATE POLICY "Readers can view their reading history" ON reading_history
  FOR SELECT USING (auth.uid() = reader_id);

-- Readers can add to their reading history
CREATE POLICY "Readers can add reading history" ON reading_history
  FOR INSERT WITH CHECK (auth.uid() = reader_id);

-- Function to record reading and use credits
CREATE OR REPLACE FUNCTION record_reading(
  p_reader_id UUID,
  p_diary_entry_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  entry_writer_id UUID;
  entry_bundle_count INTEGER;
  entry_is_free BOOLEAN;
  credits_used INTEGER;
BEGIN
  -- Get entry details
  SELECT user_id, bundle_count, is_free 
  INTO entry_writer_id, entry_bundle_count, entry_is_free
  FROM diary_entries 
  WHERE id = p_diary_entry_id;
  
  -- If entry is free, just record the read
  IF entry_is_free THEN
    INSERT INTO reading_history (reader_id, diary_entry_id, credits_used)
    VALUES (p_reader_id, p_diary_entry_id, 0)
    ON CONFLICT (reader_id, diary_entry_id) DO NOTHING;
    RETURN TRUE;
  END IF;
  
  -- Check if already read
  IF EXISTS (
    SELECT 1 FROM reading_history 
    WHERE reader_id = p_reader_id AND diary_entry_id = p_diary_entry_id
  ) THEN
    RETURN TRUE; -- Already have access
  END IF;
  
  -- Use credits
  credits_used := COALESCE(entry_bundle_count, 1);
  
  IF use_reading_credits(p_reader_id, entry_writer_id, credits_used) THEN
    -- Record the read
    INSERT INTO reading_history (reader_id, diary_entry_id, credits_used)
    VALUES (p_reader_id, p_diary_entry_id, credits_used);
    RETURN TRUE;
  ELSE
    RETURN FALSE; -- Not enough credits
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically initialize credits when subscription is created
CREATE OR REPLACE FUNCTION auto_initialize_credits()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'active' THEN
    PERFORM initialize_reader_credits(NEW.reader_id, NEW.writer_id);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER auto_initialize_credits_trigger
  AFTER INSERT OR UPDATE ON subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION auto_initialize_credits();

-- Update existing active subscriptions to have credits
INSERT INTO reader_credits (reader_id, writer_id, credits_remaining, last_reset)
SELECT DISTINCT reader_id, writer_id, 30, CURRENT_DATE
FROM subscriptions 
WHERE status = 'active'
ON CONFLICT (reader_id, writer_id) DO NOTHING;

SELECT 'Reader credits system created successfully!' as status;
