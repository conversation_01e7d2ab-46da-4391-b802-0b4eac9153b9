-- Add missing ebook-related columns to projects table
-- Run this ONLY if the check script shows these columns are missing

-- Add ebook-specific fields
ALTER TABLE projects ADD COLUMN IF NOT EXISTS is_ebook BOOLEAN DEFAULT FALSE;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS ebook_file_url TEXT;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS ebook_file_type TEXT CHECK (ebook_file_type IN ('pdf', 'epub'));
ALTER TABLE projects ADD COLUMN IF NOT EXISTS author_name TEXT;

-- Add book store fields
ALTER TABLE projects ADD COLUMN IF NOT EXISTS book_type TEXT CHECK (book_type IN ('fiction', 'non_fiction', 'memoir', 'poetry', 'other')) DEFAULT 'other';
ALTER TABLE projects ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,1) DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS review_count INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS sales_count INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}';
ALTER TABLE projects ADD COLUMN IF NOT EXISTS slug TEXT;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS meta_description TEXT;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS social_image_url TEXT;

-- Add reading and discovery fields
ALTER TABLE projects ADD COLUMN IF NOT EXISTS preview_chapters INTEGER DEFAULT 1;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS reading_time_minutes INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS daily_sales INTEGER DEFAULT 0;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS bestseller_rank INTEGER;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS featured_until TIMESTAMP WITH TIME ZONE;

-- Add unique constraint on slug (but allow nulls)
CREATE UNIQUE INDEX IF NOT EXISTS projects_slug_unique ON projects(slug) WHERE slug IS NOT NULL;

-- Create storage bucket for ebooks if it doesn't exist
INSERT INTO storage.buckets (id, name, public) 
VALUES ('ebooks', 'ebooks', true) 
ON CONFLICT (id) DO NOTHING;

-- Create storage bucket for project covers if it doesn't exist
INSERT INTO storage.buckets (id, name, public) 
VALUES ('project-covers', 'project-covers', true) 
ON CONFLICT (id) DO NOTHING;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_projects_is_ebook ON projects(is_ebook);
CREATE INDEX IF NOT EXISTS idx_projects_book_type ON projects(book_type);
CREATE INDEX IF NOT EXISTS idx_projects_average_rating ON projects(average_rating);
CREATE INDEX IF NOT EXISTS idx_projects_sales_count ON projects(sales_count);
CREATE INDEX IF NOT EXISTS idx_projects_slug ON projects(slug);

-- Update RLS policies to include ebook fields
-- Anyone can view published ebooks (drop first if exists, then create)
DROP POLICY IF EXISTS "Anyone can view published ebooks" ON projects;
CREATE POLICY "Anyone can view published ebooks" ON projects
    FOR SELECT USING (
        is_ebook = true AND
        is_complete = true AND
        is_private = false
    );

-- Comments for documentation
COMMENT ON COLUMN projects.is_ebook IS 'Whether this project is an ebook (finished book) vs work-in-progress';
COMMENT ON COLUMN projects.ebook_file_url IS 'URL to the uploaded PDF/EPUB file';
COMMENT ON COLUMN projects.author_name IS 'Custom author name for the book, can be different from user profile name';
COMMENT ON COLUMN projects.book_type IS 'Genre/type of book for categorization';
COMMENT ON COLUMN projects.slug IS 'URL-friendly identifier for SEO-friendly book URLs';
COMMENT ON COLUMN projects.tags IS 'Array of tags for book discovery and filtering';
