-- Debug subscriptions table and data

-- 1. Check if subscriptions table exists and show its structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'subscriptions'
ORDER BY ordinal_position;

-- 2. Show all subscriptions for the current user
SELECT 
    s.*,
    u1.email as subscriber_email,
    u2.email as writer_email,
    u2.name as writer_name
FROM subscriptions s
LEFT JOIN users u1 ON s.subscriber_id = u1.id
LEFT JOIN users u2 ON s.writer_id = u2.id
WHERE s.subscriber_id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e'
ORDER BY s.created_at DESC;

-- 3. Show active subscriptions (checking date comparison)
SELECT 
    s.*,
    u.name as writer_name,
    s.active_until > NOW() as is_active,
    s.active_until
FROM subscriptions s
JOIN users u ON s.writer_id = u.id
WHERE s.subscriber_id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e'
  AND s.active_until > NOW();

-- 4. Show RLS policies on subscriptions table
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE tablename = 'subscriptions';

-- 5. Check current user's role
SELECT id, email, role 
FROM users 
WHERE id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e';

-- 6. Show writers with content (potential people to follow)
SELECT 
    u.id,
    u.email,
    u.name,
    u.price_monthly,
    u.stripe_account_id,
    COUNT(de.id) as entry_count
FROM users u
LEFT JOIN diary_entries de ON u.id = de.user_id AND de.is_published = true
WHERE u.role = 'writer'
GROUP BY u.id
HAVING COUNT(de.id) > 0
ORDER BY COUNT(de.id) DESC
LIMIT 10;