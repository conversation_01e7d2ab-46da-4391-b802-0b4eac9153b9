-- Run this in your Supabase SQL Editor to enable real-time comment notifications

-- Add parent_comment_id for replies (if not already exists)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'comments' AND column_name = 'parent_comment_id'
    ) THEN
        ALTER TABLE comments ADD COLUMN parent_comment_id UUID REFERENCES comments(id) ON DELETE CASCADE;
        CREATE INDEX idx_comments_parent_id ON comments(parent_comment_id);
    END IF;
END $$;

-- Function to send comment notifications
CREATE OR REPLACE FUNCTION notify_comment_created()
RETURNS TRIGGER AS $$
DECLARE
    entry_author_id UUID;
    entry_title TEXT;
    commenter_name TEXT;
    parent_comment_author_id UUID;
BEGIN
    -- Get the diary entry author and title
    SELECT de.user_id, de.title INTO entry_author_id, entry_title
    FROM diary_entries de 
    WHERE de.id = NEW.diary_entry_id;
    
    -- Get commenter name
    SELECT name INTO commenter_name
    FROM users 
    WHERE id = NEW.user_id;
    
    -- If this is a reply to another comment
    IF NEW.parent_comment_id IS NOT NULL THEN
        -- Get the parent comment author
        SELECT c.user_id INTO parent_comment_author_id
        FROM comments c 
        WHERE c.id = NEW.parent_comment_id;
        
        -- Notify the parent comment author (if different from current commenter)
        IF parent_comment_author_id != NEW.user_id THEN
            INSERT INTO notifications (user_id, type, title, body, data)
            VALUES (
                parent_comment_author_id,
                'reply',
                'New reply to your comment',
                commenter_name || ' replied to your comment',
                jsonb_build_object(
                    'comment_id', NEW.id,
                    'diary_entry_id', NEW.diary_entry_id,
                    'diary_title', entry_title,
                    'commenter_name', commenter_name,
                    'url', '/d/' || NEW.diary_entry_id
                )
            );
        END IF;
    END IF;
    
    -- Always notify the diary entry author (if different from commenter)
    IF entry_author_id != NEW.user_id THEN
        INSERT INTO notifications (user_id, type, title, body, data)
        VALUES (
            entry_author_id,
            'comment',
            'New comment on your diary',
            commenter_name || ' commented on "' || entry_title || '"',
            jsonb_build_object(
                'comment_id', NEW.id,
                'diary_entry_id', NEW.diary_entry_id,
                'diary_title', entry_title,
                'commenter_name', commenter_name,
                'url', '/d/' || NEW.diary_entry_id
            )
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS notify_comment_created_trigger ON comments;

-- Create trigger to send notifications when comments are created
CREATE TRIGGER notify_comment_created_trigger
    AFTER INSERT ON comments
    FOR EACH ROW 
    EXECUTE FUNCTION notify_comment_created();

-- Enable real-time for comments table
ALTER PUBLICATION supabase_realtime ADD TABLE comments;

-- Test the setup
SELECT 'Comment notifications setup complete!' as status;
