-- OnlyDiary Database Verification Script
-- Run this after adding the ebook columns to verify everything is set up correctly

-- 1. Check projects table structure (should show all ebook columns)
SELECT '=== PROJECTS TABLE STRUCTURE ===' as section;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'projects' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Verify all required ebook columns exist
SELECT '=== EBOOK COLUMNS VERIFICATION ===' as section;
SELECT
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'is_ebook')
    THEN '✅ EXISTS' ELSE '❌ MISSING' END as is_ebook_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'ebook_file_url')
    THEN '✅ EXISTS' ELSE '❌ MISSING' END as ebook_file_url_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'book_type')
    THEN '✅ EXISTS' ELSE '❌ MISSING' END as book_type_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'average_rating')
    THEN '✅ EXISTS' ELSE '❌ MISSING' END as average_rating_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'review_count')
    THEN '✅ EXISTS' ELSE '❌ MISSING' END as review_count_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'sales_count')
    THEN '✅ EXISTS' ELSE '❌ MISSING' END as sales_count_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'tags')
    THEN '✅ EXISTS' ELSE '❌ MISSING' END as tags_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'slug')
    THEN '✅ EXISTS' ELSE '❌ MISSING' END as slug_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'author_name')
    THEN '✅ EXISTS' ELSE '❌ MISSING' END as author_name_column;

-- 3. Check storage buckets (should include ebooks and project-covers)
SELECT '=== STORAGE BUCKETS ===' as section;
SELECT id, name, public,
  CASE
    WHEN name IN ('ebooks', 'project-covers') THEN '✅ Required for ebooks'
    ELSE '📁 Other bucket'
  END as status
FROM storage.buckets
ORDER BY name;

-- 4. Check indexes (should include ebook-related indexes)
SELECT '=== EBOOK INDEXES ===' as section;
SELECT indexname, tablename,
  CASE
    WHEN indexname LIKE '%ebook%' OR indexname LIKE '%book_type%' OR indexname LIKE '%slug%' THEN '✅ Ebook index'
    ELSE '📊 Other index'
  END as status
FROM pg_indexes
WHERE tablename = 'projects' AND schemaname = 'public'
ORDER BY indexname;

-- 5. Check RLS policies (should include ebook policy)
SELECT '=== RLS POLICIES ===' as section;
SELECT policyname, cmd,
  CASE
    WHEN policyname LIKE '%ebook%' THEN '✅ Ebook policy'
    ELSE '🔒 Other policy'
  END as status
FROM pg_policies
WHERE tablename = 'projects'
ORDER BY policyname;

-- 6. Test query that the books page will use
SELECT '=== TEST BOOKS QUERY ===' as section;
SELECT
  COUNT(*) as total_projects,
  COUNT(*) FILTER (WHERE is_ebook = true) as ebooks,
  COUNT(*) FILTER (WHERE is_ebook = true AND is_complete = true) as complete_ebooks,
  COUNT(*) FILTER (WHERE is_ebook = true AND is_complete = true AND is_private = false) as public_ebooks
FROM projects;

-- 7. Show sample ebook data if any exists
SELECT '=== SAMPLE EBOOK DATA ===' as section;
SELECT id, title, author_name, book_type, is_ebook, is_complete, is_private, slug, created_at
FROM projects
WHERE is_ebook = true OR ebook_file_url IS NOT NULL
ORDER BY created_at DESC
LIMIT 5;

-- 8. Check if the exact query from books page works
SELECT '=== BOOKS PAGE QUERY TEST ===' as section;
SELECT 'Testing books page query...' as test;

-- This should match the exact query from the books page
SELECT id, title, description, cover_image_url, genre, book_type, price_amount,
       average_rating, review_count, sales_count, tags, slug, user_id, created_at, author_name
FROM projects
WHERE is_ebook = true
  AND is_complete = true
  AND price_amount IS NOT NULL
  AND is_private = false
LIMIT 3;
