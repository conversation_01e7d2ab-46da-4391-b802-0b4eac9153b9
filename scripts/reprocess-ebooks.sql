-- <PERSON><PERSON><PERSON> to manually trigger ebook processing for existing books
-- This will show you which books need processing and provide the API calls

-- 1. Show current ebook status
SELECT 
    id,
    title,
    author_name,
    total_words,
    total_chapters,
    reading_time_minutes,
    is_complete,
    ebook_file_type,
    ebook_file_url IS NOT NULL as has_file,
    created_at
FROM projects 
WHERE is_ebook = true
ORDER BY created_at DESC;

-- 2. Show books that need processing (have file but no stats)
SELECT 
    id,
    title,
    author_name,
    ebook_file_type,
    ebook_file_url,
    'curl -X POST http://localhost:3001/api/process-ebook -H "Content-Type: application/json" -d ''{"projectId":"' || id || '","fileUrl":"' || ebook_file_url || '","fileType":"' || ebook_file_type || '"}''' as curl_command
FROM projects 
WHERE is_ebook = true 
  AND ebook_file_url IS NOT NULL
  AND (total_words = 0 OR total_words IS NULL)
ORDER BY created_at DESC;

-- 3. Check if chapters exist
SELECT 
    p.title,
    p.total_chapters as project_chapters,
    COUNT(c.id) as actual_chapters,
    SUM(c.word_count) as chapter_words
FROM projects p
LEFT JOIN chapters c ON p.id = c.project_id
WHERE p.is_ebook = true
GROUP BY p.id, p.title, p.total_chapters
ORDER BY p.created_at DESC;
