-- Create subscriptions table first (required for credits system)

CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  reader_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  writer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired')),
  stripe_subscription_id TEXT,
  current_period_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  current_period_end TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '30 days',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- One subscription per reader-writer pair
  CONSTRAINT unique_reader_writer_subscription UNIQUE (reader_id, writer_id)
);

-- Add indexes for performance
CREATE INDEX idx_subscriptions_reader_id ON subscriptions(reader_id);
CREATE INDEX idx_subscriptions_writer_id ON subscriptions(writer_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_stripe_id ON subscriptions(stripe_subscription_id);

-- Add RLS policies
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Readers can view and manage their own subscriptions
CREATE POLICY "Readers can manage their subscriptions" ON subscriptions
  FOR ALL USING (auth.uid() = reader_id);

-- Writers can view subscriptions to their content
CREATE POLICY "Writers can view their subscriptions" ON subscriptions
  FOR SELECT USING (auth.uid() = writer_id);

-- Function to create or update subscription
CREATE OR REPLACE FUNCTION upsert_subscription(
  p_reader_id UUID,
  p_writer_id UUID,
  p_stripe_subscription_id TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  subscription_id UUID;
BEGIN
  INSERT INTO subscriptions (reader_id, writer_id, stripe_subscription_id, status)
  VALUES (p_reader_id, p_writer_id, p_stripe_subscription_id, 'active')
  ON CONFLICT (reader_id, writer_id) 
  DO UPDATE SET 
    status = 'active',
    stripe_subscription_id = COALESCE(p_stripe_subscription_id, subscriptions.stripe_subscription_id),
    current_period_start = NOW(),
    current_period_end = NOW() + INTERVAL '30 days',
    updated_at = NOW()
  RETURNING id INTO subscription_id;
  
  RETURN subscription_id;
END;
$$ LANGUAGE plpgsql;

-- Function to cancel subscription
CREATE OR REPLACE FUNCTION cancel_subscription(
  p_reader_id UUID,
  p_writer_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE subscriptions 
  SET 
    status = 'cancelled',
    updated_at = NOW()
  WHERE reader_id = p_reader_id AND writer_id = p_writer_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Add subscriber count to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS subscriber_count INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS entry_count INTEGER DEFAULT 0;

-- Function to update subscriber count
CREATE OR REPLACE FUNCTION update_subscriber_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.status = 'active' THEN
    UPDATE users 
    SET subscriber_count = subscriber_count + 1 
    WHERE id = NEW.writer_id;
  ELSIF TG_OP = 'UPDATE' THEN
    IF OLD.status != 'active' AND NEW.status = 'active' THEN
      UPDATE users 
      SET subscriber_count = subscriber_count + 1 
      WHERE id = NEW.writer_id;
    ELSIF OLD.status = 'active' AND NEW.status != 'active' THEN
      UPDATE users 
      SET subscriber_count = GREATEST(subscriber_count - 1, 0) 
      WHERE id = NEW.writer_id;
    END IF;
  ELSIF TG_OP = 'DELETE' AND OLD.status = 'active' THEN
    UPDATE users 
    SET subscriber_count = GREATEST(subscriber_count - 1, 0) 
    WHERE id = OLD.writer_id;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update subscriber count
CREATE TRIGGER update_subscriber_count_trigger
  AFTER INSERT OR UPDATE OR DELETE ON subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_subscriber_count();

-- Function to update entry count
CREATE OR REPLACE FUNCTION update_entry_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.is_published = true THEN
    UPDATE users 
    SET entry_count = entry_count + 1 
    WHERE id = NEW.user_id;
  ELSIF TG_OP = 'UPDATE' THEN
    IF OLD.is_published != NEW.is_published THEN
      IF NEW.is_published = true THEN
        UPDATE users 
        SET entry_count = entry_count + 1 
        WHERE id = NEW.user_id;
      ELSE
        UPDATE users 
        SET entry_count = GREATEST(entry_count - 1, 0) 
        WHERE id = NEW.user_id;
      END IF;
    END IF;
  ELSIF TG_OP = 'DELETE' AND OLD.is_published = true THEN
    UPDATE users 
    SET entry_count = GREATEST(entry_count - 1, 0) 
    WHERE id = OLD.user_id;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update entry count
CREATE TRIGGER update_entry_count_trigger
  AFTER INSERT OR UPDATE OR DELETE ON diary_entries
  FOR EACH ROW
  EXECUTE FUNCTION update_entry_count();

-- Initialize counts for existing users
UPDATE users SET subscriber_count = (
  SELECT COUNT(*) FROM subscriptions WHERE writer_id = users.id AND status = 'active'
);

UPDATE users SET entry_count = (
  SELECT COUNT(*) FROM diary_entries WHERE user_id = users.id AND is_published = true
);

SELECT 'Subscriptions table created successfully!' as status;
