import { createSupabaseServerClient } from "@/lib/supabase/client"
import { notFound, redirect } from "next/navigation"

interface CustomUrlPageProps {
  params: Promise<{
    customUrl: string
  }>
}

export default async function CustomUrlPage({ params }: CustomUrlPageProps) {
  const { customUrl } = await params
  const supabase = await createSupabaseServerClient()

  // Look up user by custom URL
  const { data: user, error } = await supabase
    .from("users")
    .select("id")
    .eq("custom_url", customUrl)
    .single()

  if (error || !user) {
    notFound()
  }

  // Redirect to the actual profile page
  redirect(`/u/${user.id}`)
}
