"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { SmartTypography } from "@/components/SmartTypography"

interface Book {
  id: string
  title: string
  cover_image_url: string
  price_amount: number
  preview_chapters: number
  user_id: string
  users: {
    name: string
    avatar_url: string
  }
}

interface Chapter {
  id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
}

export default function BookPreviewPage() {
  const params = useParams()
  const router = useRouter()
  const [book, setBook] = useState<Book | null>(null)
  const [previewChapters, setPreviewChapters] = useState<Chapter[]>([])
  const [currentChapterIndex, setCurrentChapterIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState(false)
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (params.id) {
      fetchPreviewData()
    }
  }, [params.id])

  const fetchPreviewData = async () => {
    try {
      // Fetch book details
      const { data: bookData, error: bookError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          cover_image_url,
          price_amount,
          preview_chapters,
          user_id,
          users!inner(name, avatar_url)
        `)
        .eq('id', params.id)
        .eq('is_ebook', true)
        .single()

      if (bookError) throw bookError
      setBook(bookData)

      // Fetch preview chapters
      const { data: chaptersData } = await supabase
        .from('chapters')
        .select('id, title, content, chapter_number, word_count')
        .eq('project_id', params.id)
        .eq('is_published', true)
        .order('chapter_number')
        .limit(bookData.preview_chapters || 1)

      setPreviewChapters(chaptersData || [])

    } catch (error) {
      console.error('Error fetching preview data:', error)
      router.push('/books')
    } finally {
      setLoading(false)
    }
  }

  const handlePurchase = async () => {
    setPurchasing(true)
    try {
      const response = await fetch('/api/books/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookId: params.id,
          priceAmount: book?.price_amount
        }),
      })

      const { url } = await response.json()
      if (url) {
        window.location.href = url
      }
    } catch (error) {
      console.error('Error creating checkout session:', error)
    } finally {
      setPurchasing(false)
    }
  }

  const formatPrice = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`
  }

  const truncateContent = (content: string, maxWords: number = 200) => {
    const words = content.split(' ')
    if (words.length <= maxWords) return content
    return words.slice(0, maxWords).join(' ') + '...'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 animate-pulse">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!book || previewChapters.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">📚</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">Preview not available</h2>
          <Link href="/books">
            <Button>Browse Books</Button>
          </Link>
        </div>
      </div>
    )
  }

  const currentChapter = previewChapters[currentChapterIndex]

  return (
    <div className="min-h-screen bg-gray-50">
      
      {/* Preview Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <Link 
                href={`/books/${book.id}`}
                className="text-white/70 hover:text-white text-sm mb-2 inline-block"
              >
                ← Back to book details
              </Link>
              <h1 className="text-2xl font-serif mb-1">{book.title}</h1>
              <p className="text-white/80">by {book.users.name}</p>
            </div>
            <div className="text-right">
              <div className="text-sm text-white/70 mb-1">📖 Free Preview</div>
              <div className="text-lg font-semibold">
                {book.preview_chapters} of {previewChapters.length} chapters
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        
        {/* Preview Navigation */}
        {previewChapters.length > 1 && (
          <div className="flex items-center justify-between mb-8">
            <Button
              variant="outline"
              onClick={() => setCurrentChapterIndex(prev => Math.max(0, prev - 1))}
              disabled={currentChapterIndex === 0}
            >
              ← Previous
            </Button>
            
            <div className="flex gap-2">
              {previewChapters.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentChapterIndex(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentChapterIndex 
                      ? 'bg-purple-600' 
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>
            
            <Button
              variant="outline"
              onClick={() => setCurrentChapterIndex(prev => Math.min(previewChapters.length - 1, prev + 1))}
              disabled={currentChapterIndex === previewChapters.length - 1}
            >
              Next →
            </Button>
          </div>
        )}

        {/* Chapter Content */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8">
          
          {/* Chapter Header */}
          <div className="p-6 sm:p-8 border-b border-gray-200">
            <div className="text-sm text-purple-600 font-medium mb-2">
              Chapter {currentChapter.chapter_number} • Preview
            </div>
            <h2 className="text-2xl sm:text-3xl font-serif text-gray-800 mb-4">
              {currentChapter.title}
            </h2>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>{currentChapter.word_count.toLocaleString()} words</span>
              <span>•</span>
              <span>{Math.ceil(currentChapter.word_count / 200)} min read</span>
              <span>•</span>
              <span className="text-purple-600 font-medium">Free Preview</span>
            </div>
          </div>

          {/* Chapter Content */}
          <div className="p-6 sm:p-8">
            <SmartTypography
              content={truncateContent(currentChapter.content, 300)}
              isPreview={true}
              className="max-w-none"
            />
            
            {/* Preview Fade Effect */}
            <div className="relative mt-8">
              <div className="absolute inset-0 bg-gradient-to-t from-white via-white/80 to-transparent h-24 pointer-events-none"></div>
              <div className="text-center pt-16">
                <div className="inline-flex items-center gap-2 text-gray-500 text-sm mb-4">
                  <span>📖</span>
                  <span>Continue reading with full access</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Purchase Call-to-Action */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-2xl p-8 text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-serif text-gray-900 mb-4">
              Enjoying the preview?
            </h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Get instant access to the complete book with all chapters, plus support the author directly. 
              Your purchase helps independent writers continue creating amazing stories.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                onClick={handlePurchase}
                isLoading={purchasing}
                size="lg"
                className="w-full sm:w-auto"
              >
                💰 Buy Full Book for {formatPrice(book.price_amount)}
              </Button>
              
              <Link href={`/books/${book.id}`}>
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  📋 View Details & Reviews
                </Button>
              </Link>
            </div>

            <div className="mt-6 text-sm text-gray-500">
              <div className="flex items-center justify-center gap-4">
                <span>✓ Instant access</span>
                <span>✓ All chapters</span>
                <span>✓ Support the author</span>
                <span>✓ Read anywhere</span>
              </div>
            </div>
          </div>
        </div>

        {/* Preview Stats */}
        <div className="mt-8 grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="text-2xl font-bold text-purple-600">{book.preview_chapters}</div>
            <div className="text-sm text-gray-600">Preview Chapters</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="text-2xl font-bold text-blue-600">
              {previewChapters.reduce((sum, ch) => sum + ch.word_count, 0).toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Preview Words</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="text-2xl font-bold text-green-600">
              {Math.ceil(previewChapters.reduce((sum, ch) => sum + ch.word_count, 0) / 200)}
            </div>
            <div className="text-sm text-gray-600">Minutes Read</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="text-2xl font-bold text-orange-600">Free</div>
            <div className="text-sm text-gray-600">Preview Cost</div>
          </div>
        </div>
      </div>
    </div>
  )
}
