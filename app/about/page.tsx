import { NavigationButton } from "@/components/NavigationButton"

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 py-20 sm:py-32">
          <div className="text-center">
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-serif text-white mb-6">
              Your Words Could be Worth Millions
            </h1>
            <p className="text-xl sm:text-2xl text-white/90 font-serif max-w-4xl mx-auto mb-8">
              OnlyDiary is the revolutionary platform where authentic storytelling meets financial freedom. 
              Transform your life experiences into literature that pays.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <NavigationButton
                href="/register"
                variant="primary"
                size="lg"
                className="bg-white text-purple-600 hover:bg-gray-100 font-bold"
              >
                Start Writing Today
              </NavigationButton>
              <NavigationButton
                href="/trending"
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white/10 font-bold"
              >
                Discover Stories
              </NavigationButton>
            </div>
          </div>
        </div>
      </div>

      {/* Revolutionary Features */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-5xl font-serif text-gray-800 mb-6">
            The Future of Creative Writing
          </h2>
          <p className="text-xl text-gray-600 font-serif max-w-3xl mx-auto">
            We&apos;ve reimagined what a writing platform can be. OnlyDiary combines cutting-edge technology 
            with human creativity to create something extraordinary.
          </p>
        </div>

        {/* Feature Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">

          {/* Real-Time Everything */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">⚡</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Real-Time Everything</h3>
            <p className="text-gray-600 leading-relaxed">
              Comments appear instantly, replies update live, notifications deliver immediately.
              No refresh needed - experience the future of interactive writing.
            </p>
          </div>

          {/* Push Notifications */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">🔔</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Smart Notifications</h3>
            <p className="text-gray-600 leading-relaxed">
              Get instant push notifications when readers comment or reply.
              Stay connected with your audience without email costs.
            </p>
          </div>

          {/* Dual Writing Modes */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">📚</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Dual Writing Modes</h3>
            <p className="text-gray-600 leading-relaxed">
              Write intimate diary entries or craft unlimited book projects.
              Same beautiful interface, unlimited creative possibilities.
            </p>
          </div>

          {/* Waitlist System */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">📋</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Waitlist Marketing</h3>
            <p className="text-gray-600 leading-relaxed">
              Build anticipation with private projects. Collect waitlists and notify
              subscribers instantly when you publish for maximum launch impact.
            </p>
          </div>

          {/* Smart Typography */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">✨</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Smart Typography</h3>
            <p className="text-gray-600 leading-relaxed">
              Revolutionary auto-formatting with AI-powered paragraph spacing,
              quote detection, and Zen mode for distraction-free writing.
            </p>
          </div>

          {/* Photo Integration */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">📸</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Rich Media</h3>
            <p className="text-gray-600 leading-relaxed">
              Upload photos with click-to-enlarge modals. Professional book covers
              with KDP-compatible aspect ratios for publishing.
            </p>
          </div>

          {/* Threaded Comments */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">💬</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Threaded Conversations</h3>
            <p className="text-gray-600 leading-relaxed">
              Real-time comments with reply threads. Build engaged communities
              around your stories with instant, meaningful conversations.
            </p>
          </div>

          {/* Flexible Monetization */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">💰</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Multiple Revenue Streams</h3>
            <p className="text-gray-600 leading-relaxed">
              Subscriptions, donations, waitlist notifications. Set your own prices,
              keep 80% of subscriptions, 95% of donations. Instant withdrawals.
            </p>
          </div>

          {/* Professional Dashboard */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-violet-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">📊</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Writer Analytics</h3>
            <p className="text-gray-600 leading-relaxed">
              Track earnings, subscriber growth, waitlist analytics.
              Professional dashboard with withdrawal management and revenue insights.
            </p>
          </div>
        </div>

        {/* Advanced Features */}
        <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-3xl p-8 sm:p-12 mb-20 text-white">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-serif mb-4">
              Breakthrough Technology
            </h2>
            <p className="text-lg text-gray-300 font-serif max-w-3xl mx-auto">
              OnlyDiary combines cutting-edge web technology with human creativity
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">⚡</span>
              </div>
              <h4 className="font-semibold mb-2">Real-Time Sync</h4>
              <p className="text-gray-300 text-sm">Comments and replies appear instantly across all devices</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">🔔</span>
              </div>
              <h4 className="font-semibold mb-2">Push Notifications</h4>
              <p className="text-gray-300 text-sm">Zero-cost browser notifications for instant engagement</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">🎨</span>
              </div>
              <h4 className="font-semibold mb-2">Smart Formatting</h4>
              <p className="text-gray-300 text-sm">AI-powered typography that makes every story beautiful</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">📱</span>
              </div>
              <h4 className="font-semibold mb-2">Mobile First</h4>
              <p className="text-gray-300 text-sm">Responsive design optimized for writing on any device</p>
            </div>
          </div>
        </div>

        {/* Revenue Breakdown */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-3xl p-8 sm:p-12 mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-serif text-gray-800 mb-4">
              Multiple Revenue Streams
            </h2>
            <p className="text-lg text-gray-600 font-serif">
              Industry-leading revenue sharing plus innovative monetization features
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6 max-w-6xl mx-auto">
            <div className="bg-white rounded-2xl p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">80%</div>
              <div className="text-lg font-serif text-gray-800 mb-2">Subscription Revenue</div>
              <div className="text-gray-600 text-sm">You keep $8 of every $10 subscription</div>
            </div>

            <div className="bg-white rounded-2xl p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">95%</div>
              <div className="text-lg font-serif text-gray-800 mb-2">Donation Revenue</div>
              <div className="text-gray-600 text-sm">You keep $9.50 of every $10 donation</div>
            </div>

            <div className="bg-white rounded-2xl p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">5¢</div>
              <div className="text-lg font-serif text-gray-800 mb-2">Waitlist Notifications</div>
              <div className="text-gray-600 text-sm">Turn waitlists into instant sales</div>
            </div>
          </div>

          <div className="text-center mt-8">
            <p className="text-gray-600 font-serif">
              <strong>Plus:</strong> Instant withdrawals • No minimum thresholds • Professional analytics
            </p>
          </div>
        </div>

        {/* Success Stories */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-serif text-gray-800 mb-6">
            Where Greatness Begins
          </h2>
          <p className="text-lg text-gray-600 font-serif max-w-3xl mx-auto mb-12">
            Every great writer started with a single story. OnlyDiary provides the platform, 
            tools, and audience to turn your authentic experiences into financial success.
          </p>
          
          <div className="grid sm:grid-cols-4 gap-6 max-w-5xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">$0</div>
              <div className="text-gray-600 text-sm">Platform fees to start</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">∞</div>
              <div className="text-gray-600 text-sm">Earning potential</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">24/7</div>
              <div className="text-gray-600 text-sm">Instant withdrawals</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">⚡</div>
              <div className="text-gray-600 text-sm">Real-time everything</div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gray-900 rounded-3xl p-8 sm:p-12 text-center text-white">
          <h2 className="text-3xl sm:text-4xl font-serif mb-6">
            Your Story is Worth Telling
          </h2>
          <p className="text-xl font-serif mb-8 opacity-90">
            Join the writers who are turning their authentic experiences into sustainable income. 
            Your words could be worth millions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <NavigationButton
              href="/register"
              variant="primary"
              size="lg"
              className="bg-white text-gray-900 hover:bg-gray-100 font-bold"
            >
              Start Writing for Free
            </NavigationButton>
            <NavigationButton
              href="/trending"
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white/10 font-bold"
            >
              Read Success Stories
            </NavigationButton>
          </div>
        </div>
      </div>
    </div>
  )
}
