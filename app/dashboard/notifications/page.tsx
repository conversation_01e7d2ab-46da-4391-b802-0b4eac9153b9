'use client'

import { useEffect, useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { WaitlistNotificationsPromo } from '@/components/WaitlistNotificationsPromo'
import { useWaitlistStats } from '@/hooks/useWaitlistStats'
import Link from 'next/link'

export default function NotificationsPage() {
  const [user, setUser] = useState<{ id: string; email?: string } | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }
    getUser()
  }, [supabase])

  const { stats, loading: statsLoading } = useWaitlistStats(user?.id ?? null)

  if (loading || statsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading notifications...</div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-serif text-gray-800 mb-4">Please Sign In</h1>
          <p className="text-gray-600 mb-6">You need to be signed in to access notifications.</p>
          <Link href="/auth" className="text-blue-600 hover:text-blue-700 font-medium">
            Sign In
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <Link href="/dashboard" className="text-blue-600 hover:text-blue-700 font-medium mb-4 inline-block">
            ← Back to Dashboard
          </Link>
          <h1 className="text-3xl font-serif text-gray-800 mb-2">
            Waitlist Notifications
          </h1>
          <p className="text-gray-600">
            Turn your waitlists into instant sales with push notifications
          </p>
        </div>

        {/* Promo Component */}
        <WaitlistNotificationsPromo waitlistStats={stats} />

        {/* Additional Info */}
        <div className="mt-12 grid md:grid-cols-2 gap-8">
          
          {/* How It Works */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-xl font-serif text-gray-800 mb-4">How It Works</h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-600 text-sm font-bold">1</span>
                </div>
                <div>
                  <div className="font-medium text-gray-800">Build Your Waitlist</div>
                  <div className="text-sm text-gray-600">Keep projects private while you write. Readers join waitlists.</div>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-600 text-sm font-bold">2</span>
                </div>
                <div>
                  <div className="font-medium text-gray-800">Ready to Launch?</div>
                  <div className="text-sm text-gray-600">Pay $0.50 per 100 people to send push notifications.</div>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-600 text-sm font-bold">3</span>
                </div>
                <div>
                  <div className="font-medium text-gray-800">Instant Sales</div>
                  <div className="text-sm text-gray-600">Notifications delivered immediately. Readers purchase right away.</div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-xl font-serif text-gray-800 mb-4">Frequently Asked Questions</h3>
            <div className="space-y-4">
              <div>
                <div className="font-medium text-gray-800 mb-1">Why charge for notifications?</div>
                <div className="text-sm text-gray-600">
                  Push notifications have delivery costs and ensure only serious launches use the system.
                </div>
              </div>
              
              <div>
                <div className="font-medium text-gray-800 mb-1">What&apos;s the success rate?</div>
                <div className="text-sm text-gray-600">
                  Waitlist subscribers convert 10-15x higher than regular visitors because they&apos;re already interested.
                </div>
              </div>
              
              <div>
                <div className="font-medium text-gray-800 mb-1">Can I test it first?</div>
                <div className="text-sm text-gray-600">
                  Yes! The minimum charge is just $0.50 for up to 100 people. Perfect for testing.
                </div>
              </div>
              
              <div>
                <div className="font-medium text-gray-800 mb-1">What if notifications fail?</div>
                <div className="text-sm text-gray-600">
                  You only pay for successfully delivered notifications. Failed sends are automatically refunded.
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="mt-12 text-center bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
          <h3 className="text-2xl font-serif mb-3">Ready to Turn Waitlists into Revenue?</h3>
          <p className="text-purple-100 mb-6 max-w-2xl mx-auto">
            Join successful authors who are using waitlist notifications to maximize their launch sales.
          </p>
          {stats.totalWaitlistSize > 0 ? (
            <div className="space-y-2">
              <div className="text-lg">
                You have <strong>{stats.totalWaitlistSize} people</strong> waiting for your content
              </div>
              <div className="text-purple-200">
                That&apos;s ${((stats.totalWaitlistSize * 0.005) * 100).toFixed(0)} in potential immediate sales!
              </div>
            </div>
          ) : (
            <Link href="/write" className="inline-block bg-white text-purple-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors">
              Create Your First Project
            </Link>
          )}
        </div>
      </div>
    </div>
  )
}
