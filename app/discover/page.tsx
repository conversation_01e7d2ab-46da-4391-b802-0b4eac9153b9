'use client'

import { useState, useEffect, useCallback } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { Search, Users, BookOpen, Heart } from 'lucide-react'
import { FollowButton } from '@/components/FollowButton'
import Image from 'next/image'

interface Creator {
  id: string
  name: string
  bio?: string
  avatar?: string
  profile_picture_url?: string
  subscriber_count: number
  entry_count: number
  bookmark_count: number
  is_bookmarked?: boolean
}

export default function DiscoverPage() {
  const [creators, setCreators] = useState<Creator[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filter, setFilter] = useState<'all' | 'new' | 'popular'>('all')
  const [user, setUser] = useState<{ id: string; role: string } | null>(null)
  // const [bookmarkingId, setBookmarkingId] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  const checkUser = useCallback(async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      if (authUser) {
        const { data: profile } = await supabase
          .from('users')
          .select('*')
          .eq('id', authUser.id)
          .single()
        setUser(profile)
      }
    } catch (error) {
      console.error('Error checking user:', error)
    }
  }, [supabase])

  const loadCreators = useCallback(async () => {
    setLoading(true)
    try {
      // Simple query first to debug
      let query = supabase
        .from('users')
        .select('id, name, bio, avatar, profile_picture_url, subscriber_count, entry_count')
        .eq('role', 'writer')

      if (filter === 'popular') {
        query = query.order('subscriber_count', { ascending: false })
      } else if (filter === 'new') {
        query = query.order('created_at', { ascending: false })
      } else {
        query = query.order('entry_count', { ascending: false })
      }

      const { data, error } = await query.limit(20)

      if (error) {
        console.error('Error loading creators:', error)
      } else {
        // Get dynamic counts for creators
        const creatorIds = (data || []).filter(creator => (creator.entry_count || 0) > 0).map(c => c.id)
        
        // Batch fetch follower counts
        const { data: followerCounts } = await supabase
          .from('follows')
          .select('writer_id')
          .in('writer_id', creatorIds)
        
        // Batch fetch love counts (get diary entry IDs first, then loves)
        const { data: creatorEntries } = await supabase
          .from('diary_entries')
          .select('id, user_id')
          .in('user_id', creatorIds)
        
        const entryIds = creatorEntries?.map(e => e.id) || []
        const { data: loveCounts } = await supabase
          .from('loves')
          .select('diary_entry_id')
          .in('diary_entry_id', entryIds)
        
        // Count followers per creator
        const followerCountMap = followerCounts?.reduce((acc, follow) => {
          acc[follow.writer_id] = (acc[follow.writer_id] || 0) + 1
          return acc
        }, {} as Record<string, number>) || {}
        
        // Count loves per creator
        const loveCountMap = loveCounts?.reduce((acc, love) => {
          const entry = creatorEntries?.find(e => e.id === love.diary_entry_id)
          if (entry) {
            acc[entry.user_id] = (acc[entry.user_id] || 0) + 1
          }
          return acc
        }, {} as Record<string, number>) || {}

        let creatorsWithBookmarks = (data || [])
          .filter(creator => (creator.entry_count || 0) > 0)
          .map(creator => ({
            ...creator,
            subscriber_count: followerCountMap[creator.id] || 0,
            bookmark_count: loveCountMap[creator.id] || 0,
            is_bookmarked: false
          }))

        // If user is logged in, apply filters
        if (user) {
          console.log('Current user ID:', user.id)
          console.log('Creators before filtering:', creatorsWithBookmarks.map(c => ({ id: c.id, name: c.name })))

          // First, exclude the current user from seeing themselves
          creatorsWithBookmarks = creatorsWithBookmarks.filter(creator => {
            const shouldInclude = creator.id !== user.id
            if (!shouldInclude) {
              console.log('Filtering out current user:', creator.name, creator.id)
            }
            return shouldInclude
          })

          console.log('Creators after self-filter:', creatorsWithBookmarks.map(c => ({ id: c.id, name: c.name })))

          try {
            // Use subscriptions table instead of bookmarks
            const { data: subscriptions } = await supabase
              .from('subscriptions')
              .select('writer_id')
              .eq('subscriber_id', user.id)
              .gte('active_until', new Date().toISOString())

            const subscribedIds = new Set(subscriptions?.map(s => s.writer_id) || [])

            // Hide creators you're already subscribed to from discover page
            creatorsWithBookmarks = creatorsWithBookmarks.filter(creator =>
              !subscribedIds.has(creator.id)
            )

            // Check for follows
            const { data: follows, error: followsError } = await supabase
              .from('follows')
              .select('writer_id')
              .eq('follower_id', user.id)

            if (followsError) {
              console.error('Error fetching follows:', followsError)
            } else {
              const followingIds = new Set(follows?.map(f => f.writer_id) || [])
              creatorsWithBookmarks = creatorsWithBookmarks.map(creator => ({
                ...creator,
                is_bookmarked: followingIds.has(creator.id)
              }))
            }
          } catch (subscriptionError) {
            console.log('Subscriptions query error:', subscriptionError)
          }
        }

        setCreators(creatorsWithBookmarks)
      }
    } catch (err) {
      console.error('Error loading creators:', err)
    } finally {
      setLoading(false)
    }
  }, [filter, user, supabase])

  useEffect(() => {
    checkUser()
  }, [checkUser])

  useEffect(() => {
    if (user) {
      loadCreators()
    }
  }, [user, loadCreators])

  // const toggleBookmark = async (creatorId: string) => {
  //   if (!user) {
  //     console.log('No user found for subscription action')
  //     return
  //   }

  //   console.log('Toggling subscription for creator:', creatorId, 'User:', user.id)
  //   setBookmarkingId(creatorId)

  //   try {
  //     const creator = creators.find(c => c.id === creatorId)
  //     if (!creator) {
  //       console.log('Creator not found:', creatorId)
  //       return
  //     }

  //     if (creator.is_bookmarked) {
  //       // Remove subscription
  //       console.log('Removing subscription...')
  //       const { data, error } = await supabase
  //         .from('subscriptions')
  //         .delete()
  //         .eq('subscriber_id', user.id)
  //         .eq('writer_id', creatorId)

  //       console.log('Remove subscription result:', { data, error })

  //       if (!error) {
  //         setCreators(prev => prev.map(c =>
  //           c.id === creatorId
  //             ? { ...c, is_bookmarked: false, bookmark_count: Math.max(0, c.bookmark_count - 1) }
  //             : c
  //         ))
  //         console.log('Subscription removed successfully')
  //       } else {
  //         console.error('Error removing subscription:', error)
  //       }
  //     } else {
  //       // Add subscription (redirect to subscription page)
  //       console.log('Redirecting to subscription page...')
  //       window.location.href = `/u/${creatorId}`
  //     }
  //   } catch (error) {
  //     console.error('Error toggling subscription:', error)
  //     alert('Something went wrong. Please try again.')
  //   } finally {
  //     setBookmarkingId(null)
  //   }
  // }

  const filteredCreators = creators.filter(creator =>
    creator.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    creator.bio?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-serif text-gray-800 mb-2">
            Discover Creators
          </h1>
          <p className="text-gray-600 font-serif">
            Find authentic voices sharing their most personal stories
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <div className="relative max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search creators by name or bio..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => setFilter('all')}
              className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                filter === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              All Creators
            </button>
            <button
              onClick={() => setFilter('popular')}
              className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                filter === 'popular'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              Most Popular
            </button>
            <button
              onClick={() => setFilter('new')}
              className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                filter === 'new'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              Newest
            </button>
          </div>
        </div>

        {/* Creators Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-white rounded-2xl p-6 shadow-sm animate-pulse">
                <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full mb-4"></div>
                <div className="flex justify-center gap-4">
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredCreators.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCreators.map((creator) => (
              <div
                key={creator.id}
                className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 relative group"
              >

                <Link href={`/u/${creator.id}`} className="block">
                  <div className="text-center">
                    <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mx-auto mb-4 overflow-hidden">
                      {creator.avatar || creator.profile_picture_url ? (
                        <Image
                          src={(creator.avatar || creator.profile_picture_url) as string}
                          alt={creator.name}
                          width={80}
                          height={80}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-2xl font-serif text-gray-500">
                          {creator.name.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                  
                  <h3 className="text-lg font-serif text-gray-800 mb-2">
                    {creator.name}
                  </h3>
                  
                  {creator.bio && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {creator.bio}
                    </p>
                  )}
                  
                    <div className="flex justify-center gap-6 text-sm text-gray-500 mb-4">
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span>{creator.subscriber_count || 0}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <BookOpen className="w-4 h-4" />
                        <span>{creator.entry_count || 0}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        <span>{creator.bookmark_count || 0}</span>
                      </div>
                    </div>



                    <div className="flex gap-2">
                      <div className="bg-blue-600 text-white px-4 py-2 rounded-xl font-medium hover:bg-blue-700 transition-colors flex-1 text-center">
                        View Profile
                      </div>
                    </div>
                  </div>
                </Link>

                {/* Follow Action */}
                {user && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <FollowButton
                      writerId={creator.id}
                      writerName={creator.name}
                      initialIsFollowing={creator.is_bookmarked}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-serif text-gray-800 mb-2">No creators found</h3>
            <p className="text-gray-600">
              {searchQuery
                ? 'Try adjusting your search terms'
                : user
                ? 'You&apos;re already following all available creators! Check back later for new writers.'
                : 'No creators match the current filter'
              }
            </p>
            {user && !searchQuery && (
              <div className="mt-4">
                <Link
                  href="/timeline"
                  className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  📖 Go to Timeline
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
