import { createSupabaseServerClient } from "@/lib/supabase/client"
import { notFound } from "next/navigation"
import { MailingListLandingClient } from "@/components/MailingListLandingClient"

interface MailingListPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function MailingListPage({ params }: MailingListPageProps) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()

  // Look up user by ID
  const { data: creator, error } = await supabase
    .from("users")
    .select(`
      id,
      name,
      bio,
      profile_picture_url,
      avatar,
      custom_url,
      role,
      hide_subscriber_count
    `)
    .eq("id", id)
    .single()

  if (error || !creator) {
    notFound()
  }

  // Only writers/creators can have mailing lists
  if (creator.role !== 'writer' && creator.role !== 'admin') {
    notFound()
  }

  // Get subscriber count (if not hidden)
  let subscriberCount = 0
  if (!creator.hide_subscriber_count) {
    const { data: subscriptions } = await supabase
      .from("creator_notification_subscriptions")
      .select("id")
      .eq("creator_id", creator.id)

    subscriberCount = subscriptions?.length || 0
  }

  return (
    <MailingListLandingClient 
      creator={creator}
      subscriberCount={subscriberCount}
      showSubscriberCount={!creator.hide_subscriber_count}
    />
  )
}

// Generate metadata for SEO
export async function generateMetadata({ params }: MailingListPageProps) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()

  const { data: creator } = await supabase
    .from("users")
    .select("name, bio")
    .eq("id", id)
    .single()

  if (!creator) {
    return {
      title: "Mailing List - OnlyDiary",
      description: "Join this creator's mailing list for exclusive updates"
    }
  }

  return {
    title: `Join ${creator.name}'s Mailing List - OnlyDiary`,
    description: creator.bio || `Get exclusive updates and notifications from ${creator.name}`,
    openGraph: {
      title: `Join ${creator.name}'s Mailing List`,
      description: creator.bio || `Get exclusive updates and notifications from ${creator.name}`,
      type: 'website',
    },
    twitter: {
      card: 'summary',
      title: `Join ${creator.name}'s Mailing List`,
      description: creator.bio || `Get exclusive updates and notifications from ${creator.name}`,
    }
  }
}
