type FavoriteCreator = {
  id: string;
  created_at: string;
  writer: {
    id: string;
    name: string;
    avatar: string;
    profile_picture_url: string;
    bio: string;
  } | null;
};

import { createServerSupabaseClient } from '@/lib/supabase/server';
import Head from 'next/head';
import { notFound } from 'next/navigation';
import SubscriberProfileClient from '@/components/SubscriberProfileClient';


export default async function SubscriberProfilePage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const supabase = await createServerSupabaseClient();

  // Fetch subscriber profile
  const { data: profile, error: profileError } = await supabase
    .from('users')
    .select(`
      id, 
      name, 
      avatar, 
      bio, 
      profile_picture_url,
      social_twitter,
      social_instagram,
      social_website,
      flower_count,
      created_at
    `)
    .eq('id', id)
    .single();

  if (profileError || !profile) {
    notFound();
  }

  // Fetch flowers received by the subscriber
  const { data: flowers, error: flowersError } = await supabase
    .from('flowers')
    .select(`
      id, 
      message, 
      created_at, 
      giver_id,
      giver:giver_id (
        name,
        avatar
      )
    `)
    .eq('receiver_id', id)
    .order('created_at', { ascending: false });

  if (flowersError) {
    console.error('Error fetching flowers:', flowersError);
  }

  // Fetch favorite creators - use manual join
  let favoriteCreators: FavoriteCreator[] = [];
  try {
    const { data: favorites } = await supabase
      .from('favorite_creators')
      .select('id, created_at, writer_id')
      .eq('user_id', id)
      .order('created_at', { ascending: false });

    if (favorites && favorites.length > 0) {
      // Get writer details separately
      const writerIds = favorites.map(f => f.writer_id);
      const { data: writers } = await supabase
        .from('users')
        .select('id, name, avatar, profile_picture_url, bio')
        .in('id', writerIds);

      // Combine the data
      favoriteCreators = favorites.map(fav => {
        const writer = writers?.find(w => w.id === fav.writer_id);
        return {
          id: fav.id,
          created_at: fav.created_at,
          writer: writer
            ? {
                id: writer.id,
                name: writer.name ?? '',
                avatar: writer.avatar ?? '',
                profile_picture_url: writer.profile_picture_url ?? '',
                bio: writer.bio ?? ''
              }
            : null
        };
      });
    }
  } catch (err) {
    console.log('Error fetching favorites:', err);
    favoriteCreators = [];
  }

  const typedFlowers = flowers as unknown as Array<{
    id: string;
    message: string;
    created_at: string;
    giver_id: string;
    giver: {
      name: string;
      avatar: string;
    } | null;
  }>;

return (
    <>
      <Head>
        <title>{profile.name ?? undefined}</title>
        <meta property="og:title" content={profile.name ?? undefined} />
        <meta property="og:description" content={profile.bio || ''} />
        <meta name="twitter:card" content="summary" />
      </Head>
      <SubscriberProfileClient
        profile={profile}
        flowers={typedFlowers || []}
        favoriteCreators={favoriteCreators}
      />
    </>
  );
}
