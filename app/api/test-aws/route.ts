import { NextResponse } from "next/server"

export async function GET() {
  try {
    // Check if AWS credentials are configured
    const hasCredentials = !!(
      process.env.AWS_ACCESS_KEY_ID && 
      process.env.AWS_SECRET_ACCESS_KEY
    )

    if (!hasCredentials) {
      return NextResponse.json({
        configured: false,
        message: "AWS credentials not found in environment variables"
      })
    }

    // Try to import and test AWS SDK
    try {
      const { RekognitionClient, DetectLabelsCommand } = await import('@aws-sdk/client-rekognition')
      
      const client = new RekognitionClient({
        region: process.env.AWS_REGION || 'us-east-1',
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        },
      })

      // Test with a simple operation (this will fail but tells us if credentials work)
      try {
        await client.send(new DetectLabelsCommand({
          Image: { Bytes: new Uint8Array(0) }, // Empty image will fail but test auth
          MaxLabels: 1
        }))
      } catch (testError: unknown) {
        const error = testError as { name?: string }
        // If it's an auth error, credentials are wrong
        if (error.name === 'UnauthorizedOperation' || error.name === 'InvalidUserID.NotFound') {
          return NextResponse.json({
            configured: false,
            message: "AWS credentials are invalid"
          })
        }
        // Other errors mean credentials work but request was invalid (expected)
      }

      return NextResponse.json({
        configured: true,
        message: "AWS Rekognition is properly configured",
        region: process.env.AWS_REGION || 'us-east-1'
      })

    } catch {
      return NextResponse.json({
        configured: false,
        message: "AWS SDK not installed. Run: npm install @aws-sdk/client-rekognition"
      })
    }

  } catch (error) {
    return NextResponse.json({
      configured: false,
      message: `Error testing AWS: ${error}`
    })
  }
}
