import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { stripe } from '@/lib/stripe'

// Add a simple GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    status: 'API is working',
    timestamp: new Date().toISOString(),
    hasStripe: !!stripe
  })
}

export async function POST() {
  console.log('=== STRIPE CONNECT API CALLED ===')

  try {
    // Check if Stripe is configured
    if (!stripe) {
      console.error('Stripe not configured - missing STRIPE_SECRET_KEY')
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 })
    }

    console.log('Stripe client available')

    const supabase = await createSupabaseServerClient()
    console.log('Supabase client created')

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    console.log('Auth check:', { hasUser: !!user, authError })

    if (authError || !user) {
      console.error('Auth error:', authError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()

    console.log('Profile query:', { hasProfile: !!profile, profileError })

    if (profileError || !profile) {
      console.error('Profile error:', profileError)
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Check if user already has a Stripe account
    if (profile.stripe_account_id) {
      console.log('User has existing Stripe account:', profile.stripe_account_id)

      try {
        // Verify the account exists and create account link
        const accountLink = await stripe.accountLinks.create({
          account: profile.stripe_account_id,
          refresh_url: `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?stripe_refresh=true`,
          return_url: `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?stripe_success=true`,
          type: 'account_onboarding',
        })

        console.log('Account link created for existing account')
        return NextResponse.json({ url: accountLink.url })
      } catch (accountError) {
        console.log('Existing Stripe account is invalid, clearing and creating new one:', accountError.message)

        // Clear the invalid stripe_account_id from database
        await supabase
          .from('users')
          .update({
            stripe_account_id: null,
            stripe_onboarding_complete: false
          })
          .eq('id', user.id)

        // Continue to create new account below
      }
    }

    // Create new Stripe Connect account
    console.log('Creating new Stripe account for user:', user.email)

    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US', // You might want to make this dynamic
      email: user.email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'individual',
      metadata: {
        user_id: user.id,
        platform: 'onlydiary'
      }
    })

    console.log('Stripe account created:', account.id)

    // Save Stripe account ID to database
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        stripe_account_id: account.id,
        stripe_onboarding_complete: false
      })
      .eq('id', user.id)

    if (updateError) {
      console.error('Error updating user with Stripe account:', updateError)
      return NextResponse.json({ error: 'Failed to save Stripe account' }, { status: 500 })
    }

    // Create account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?stripe_refresh=true`,
      return_url: `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?stripe_success=true`,
      type: 'account_onboarding',
    })

    return NextResponse.json({ url: accountLink.url })

  } catch (error: any) {
    console.error('Stripe Connect error:', error)

    // Return more detailed error information
    const errorMessage = error?.message || 'Unknown error'
    const errorDetails = {
      message: errorMessage,
      type: error?.type || 'Unknown',
      code: error?.code,
      param: error?.param,
      statusCode: error?.statusCode,
      requestId: error?.requestId,
      // Include the full error for debugging
      fullError: JSON.stringify(error, null, 2)
    }

    console.error('Detailed Stripe error:', errorDetails)

    return NextResponse.json(
      {
        error: 'Failed to create Stripe account',
        details: errorDetails,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
