import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { stripe } from '@/lib/stripe'

export async function GET() {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's Stripe account ID
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('stripe_account_id, stripe_onboarding_complete')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    if (!profile.stripe_account_id) {
      return NextResponse.json({ 
        available: 0, 
        pending: 0, 
        currency: 'usd',
        message: 'No Stripe account connected' 
      })
    }

    if (!profile.stripe_onboarding_complete) {
      return NextResponse.json({ 
        available: 0, 
        pending: 0, 
        currency: 'usd',
        message: 'Stripe onboarding not complete' 
      })
    }

    // Get the actual balance from Stripe Connect account
    const balance = await stripe.balance.retrieve({
      stripeAccount: profile.stripe_account_id
    })

    // Get the available balance (what can be withdrawn)
    const availableBalance = balance.available.find(b => b.currency === 'usd')?.amount || 0
    const pendingBalance = balance.pending.find(b => b.currency === 'usd')?.amount || 0

    return NextResponse.json({
      available: availableBalance, // in cents
      pending: pendingBalance, // in cents
      currency: 'usd',
      message: 'Balance retrieved from Stripe Connect'
    })

  } catch (error) {
    console.error('Error fetching Stripe balance:', error)
    return NextResponse.json(
      { error: 'Failed to fetch balance' },
      { status: 500 }
    )
  }
}
