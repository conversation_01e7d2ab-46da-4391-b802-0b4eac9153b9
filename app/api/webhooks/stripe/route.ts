import { createSupabaseServerClient } from "@/lib/supabase/client"
import { NextRequest, NextResponse } from "next/server"
import Stripe from "stripe"

const stripe = process.env.STRIPE_SECRET_KEY 
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: "2025-05-28.basil",
    })
  : null

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET

export async function POST(request: NextRequest) {
  if (!stripe || !webhookSecret) {
    return NextResponse.json(
      { error: "Stripe not configured" },
      { status: 503 }
    )
  }

  try {
    const body = await request.text()
    const signature = request.headers.get("stripe-signature")

    if (!signature) {
      return NextResponse.json(
        { error: "No signature found" },
        { status: 400 }
      )
    }

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error("Webhook signature verification failed:", err)
      return NextResponse.json(
        { error: "Webhook signature verification failed" },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        
        if (session.mode === 'subscription' && session.subscription) {
          const subscriberId = session.metadata?.subscriber_id
          const writerId = session.metadata?.writer_id
          
          if (!subscriberId || !writerId) {
            console.error("Missing metadata in checkout session")
            break
          }

          // Get the subscription details
          await stripe.subscriptions.retrieve(session.subscription as string)
          
          // Calculate active until date (30 days from now)
          const activeUntil = new Date()
          activeUntil.setMonth(activeUntil.getMonth() + 1)

          // Create or update subscription record
          const { error: subscriptionError } = await supabase
            .from("subscriptions")
            .upsert({
              subscriber_id: subscriberId,
              writer_id: writerId,
              active_until: activeUntil.toISOString(),
              stripe_subscription_id: session.subscription as string,
            }, {
              onConflict: "subscriber_id,writer_id"
            })

          if (subscriptionError) {
            console.error("Failed to create subscription record:", subscriptionError)
          }

          // Record the payment
          const { error: paymentError } = await supabase
            .from("payments")
            .insert({
              payer_id: subscriberId,
              writer_id: writerId,
              kind: "sub",
              amount_cents: session.amount_total || 0,
              stripe_payment_id: session.payment_intent as string || session.id,
            })

          if (paymentError) {
            console.error("Failed to record payment:", paymentError)
          }
        }
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        
        const subscriptionId = (invoice as any).subscription // eslint-disable-line @typescript-eslint/no-explicit-any
        if (subscriptionId && typeof subscriptionId === 'string') {
          // Get subscription details  
          const subscription = await stripe.subscriptions.retrieve(subscriptionId)
          
          // Find the subscription record
          const { data: subRecord } = await supabase
            .from("subscriptions")
            .select("*")
            .eq("stripe_subscription_id", subscription.id)
            .single()

          if (subRecord) {
            // Extend subscription by one month
            const newActiveUntil = new Date(subRecord.active_until)
            newActiveUntil.setMonth(newActiveUntil.getMonth() + 1)

            const { error: updateError } = await supabase
              .from("subscriptions")
              .update({
                active_until: newActiveUntil.toISOString()
              })
              .eq("id", subRecord.id)

            if (updateError) {
              console.error("Failed to extend subscription:", updateError)
            }

            // Record the payment
            const { error: paymentError } = await supabase
              .from("payments")
              .insert({
                payer_id: subRecord.subscriber_id,
                writer_id: subRecord.writer_id,
                kind: "sub",
                amount_cents: invoice.amount_paid || 0,
                stripe_payment_id: ((invoice as any).payment_intent as string) || invoice.id, // eslint-disable-line @typescript-eslint/no-explicit-any
              })

            if (paymentError) {
              console.error("Failed to record recurring payment:", paymentError)
            }
          }
        }
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        
        // Mark subscription as expired
        const { error: expireError } = await supabase
          .from("subscriptions")
          .update({
            active_until: new Date().toISOString() // Set to now (expired)
          })
          .eq("stripe_subscription_id", subscription.id)

        if (expireError) {
          console.error("Failed to expire subscription:", expireError)
        }
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        
        const subscriptionId = (invoice as any).subscription // eslint-disable-line @typescript-eslint/no-explicit-any
        if (subscriptionId && typeof subscriptionId === 'string') {
          // You might want to send an email notification here
          // or implement a retry mechanism
          console.log(`Payment failed for subscription: ${subscriptionId}`)
        }
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error("Webhook error:", error)
    return NextResponse.json(
      { error: "Webhook handler failed" },
      { status: 500 }
    )
  }
}