import { NextRequest, NextResponse } from 'next/server'
import { sendWelcomeEmail } from '@/lib/email/notifications'

export async function POST(request: NextRequest) {
  try {
    const { userId, userEmail, userName, userRole } = await request.json()
    
    // Validate required fields
    if (!userId || !userEmail || !userName || !userRole) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate role
    if (!['subscriber', 'writer'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Invalid user role' },
        { status: 400 }
      )
    }

    // Send welcome email
    const result = await sendWelcomeEmail(userId, userEmail, userName, userRole)
    
    if (!result.success) {
      console.error('Welcome email failed:', result.error)
      // Don't fail the registration if email fails
      return NextResponse.json(
        { 
          success: false, 
          error: result.error,
          message: 'Welcome email failed but registration succeeded'
        },
        { status: 200 } // Still return 200 so registration doesn't fail
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Welcome email sent successfully',
      id: result.id
    })

  } catch (error) {
    console.error('Welcome email API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Welcome email failed but registration succeeded'
      },
      { status: 200 } // Still return 200 so registration doesn't fail
    )
  }
}
