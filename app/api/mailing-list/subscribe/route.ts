import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/client"

export async function POST(request: NextRequest) {
  try {
    const { creatorId, email, name } = await request.json()

    if (!creatorId || !email) {
      return NextResponse.json(
        { error: "Creator ID and email are required" },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()

    // Verify creator exists and is a writer
    const { data: creator, error: creatorError } = await supabase
      .from("users")
      .select("id, name, role")
      .eq("id", creatorId)
      .single()

    if (creatorError || !creator) {
      return NextResponse.json(
        { error: "Creator not found" },
        { status: 404 }
      )
    }

    if (creator.role !== 'writer' && creator.role !== 'admin') {
      return NextResponse.json(
        { error: "Invalid creator" },
        { status: 400 }
      )
    }

    // Check if already subscribed
    const { data: existing } = await supabase
      .from("creator_notification_subscriptions")
      .select("id")
      .eq("creator_id", creatorId)
      .eq("subscriber_email", email.toLowerCase().trim())
      .single()

    if (existing) {
      return NextResponse.json(
        { error: "Already subscribed to this creator's notifications" },
        { status: 409 }
      )
    }

    // Get current user if authenticated
    const { data: { user } } = await supabase.auth.getUser()

    // Create subscription
    const { error: subscribeError } = await supabase
      .from("creator_notification_subscriptions")
      .insert({
        creator_id: creatorId,
        subscriber_id: user?.id || null,
        subscriber_email: email.toLowerCase().trim(),
        subscriber_name: name?.trim() || null,
        notification_preferences: { email: true, push: true }
      })

    if (subscribeError) {
      console.error("Subscription error:", subscribeError)
      return NextResponse.json(
        { error: "Failed to subscribe" },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error("Mailing list subscription error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { creatorId, email } = await request.json()

    if (!creatorId || !email) {
      return NextResponse.json(
        { error: "Creator ID and email are required" },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()

    // Remove subscription
    const { error: unsubscribeError } = await supabase
      .from("creator_notification_subscriptions")
      .delete()
      .eq("creator_id", creatorId)
      .eq("subscriber_email", email.toLowerCase().trim())

    if (unsubscribeError) {
      console.error("Unsubscribe error:", unsubscribeError)
      return NextResponse.json(
        { error: "Failed to unsubscribe" },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error("Mailing list unsubscribe error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
