import { NextRequest, NextResponse } from 'next/server'
import { Resend } from 'resend'

export async function POST(request: NextRequest) {
  try {
    const { testEmail } = await request.json()
    
    if (!testEmail) {
      return NextResponse.json({ error: 'Email required' }, { status: 400 })
    }

    const resend = new Resend(process.env.RESEND_API_KEY!)
    
    console.log('🔍 Debug Info:')
    console.log('API Key:', process.env.RESEND_API_KEY?.substring(0, 10) + '...')
    console.log('From Email:', process.env.RESEND_FROM_EMAIL)
    console.log('To Email:', testEmail)

    // Try the most basic email possible
    const result = await resend.emails.send({
      from: '<EMAIL>', // Use Resend's default sender first
      to: testEmail,
      subject: 'Basic Test Email',
      html: '<h1>This is a test</h1><p>If you see this, <PERSON>sen<PERSON> is working!</p>',
    })

    console.log('📧 Resend Response:', result)

    if (result.error) {
      return NextResponse.json({
        success: false,
        error: result.error.message,
        details: result.error
      })
    }

    return NextResponse.json({
      success: true,
      id: result.data?.id,
      message: 'Email sent using Resend default sender'
    })

  } catch (error) {
    console.error('❌ Debug email error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    })
  }
}
