import { NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/client"

export async function POST() {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Auto-approve photos that have been pending for more than 5 minutes
    const { error } = await supabase.rpc('auto_approve_pending_photos')
    
    if (error) {
      console.error('Error auto-approving photos:', error)
      return NextResponse.json(
        { error: "Failed to auto-approve photos" },
        { status: 500 }
      )
    }

    // Also manually approve any photos that are still pending
    const { data: pendingPhotos, error: selectError } = await supabase
      .from('photos')
      .select('id')
      .eq('moderation_status', 'pending')

    if (selectError) {
      console.error('Error selecting pending photos:', selectError)
    } else if (pendingPhotos && pendingPhotos.length > 0) {
      const { error: updateError } = await supabase
        .from('photos')
        .update({ 
          moderation_status: 'approved',
          rekognition_labels: {
            auto_approved: true,
            reason: 'Manual auto-approval via API',
            processed_at: new Date().toISOString()
          }
        })
        .eq('moderation_status', 'pending')

      if (updateError) {
        console.error('Error updating pending photos:', updateError)
      } else {
        console.log(`Auto-approved ${pendingPhotos.length} pending photos`)
      }
    }

    return NextResponse.json({
      success: true,
      message: "Auto-approval process completed"
    })

  } catch (error) {
    console.error("Error in auto-approve endpoint:", error)
    return NextResponse.json(
      { error: "Failed to process auto-approval" },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'OnlyDiary Auto-Approval API',
    description: 'Automatically approves photos that have been pending for too long'
  })
}
