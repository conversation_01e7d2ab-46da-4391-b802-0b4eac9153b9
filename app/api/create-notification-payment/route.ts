import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { createSupabaseClient } from '@/lib/supabase/client'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil'
})

export async function POST(request: NextRequest) {
  try {
    const { projectId } = await request.json()

    // Calculate cost: $0.50 per 100 people, minimum $0.50
    // const _costCents = Math.max(50, Math.ceil(waitlistSize / 100) * 50)

    // Verify user owns the project and get waitlist
    const supabase = createSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify project ownership
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title, user_id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Get actual waitlist count
    const { count: actualWaitlistSize } = await supabase
      .from('project_waitlist')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)

    if (!actualWaitlistSize || actualWaitlistSize === 0) {
      return NextResponse.json({ error: 'No waitlist found for this project' }, { status: 400 })
    }

    // Recalculate cost based on actual waitlist size
    const actualCostCents = Math.max(50, Math.ceil(actualWaitlistSize / 100) * 50)

    // Create Stripe Payment Intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: actualCostCents,
      currency: 'usd',
      metadata: {
        type: 'waitlist_notifications',
        project_id: projectId,
        project_title: project.title,
        user_id: user.id,
        waitlist_size: actualWaitlistSize.toString(),
        cost_per_notification: '0.5' // cents
      },
      description: `Waitlist notifications for "${project.title}" (${actualWaitlistSize} recipients)`
    })

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      amount: actualCostCents,
      waitlistSize: actualWaitlistSize,
      projectTitle: project.title
    })

  } catch (error) {
    console.error('Error creating payment intent:', error)
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    )
  }
}
