import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    // Check if R2 is properly configured
    if (!process.env.CLOUDFLARE_R2_BUCKET_NAME || !process.env.CLOUDFLARE_R2_TOKEN || !process.env.CLOUDFLARE_ACCOUNT_ID) {
      return NextResponse.json({
        success: false,
        error: "R2 configuration missing",
        config: {
          hasBucket: !!process.env.CLOUDFLARE_R2_BUCKET_NAME,
          hasToken: !!process.env.CLOUDFLARE_R2_TOKEN,
          hasAccountId: !!process.env.CLOUDFLARE_ACCOUNT_ID,
          hasPublicUrl: !!process.env.CLOUDFLARE_R2_PUBLIC_URL
        }
      }, { status: 500 });
    }

    // Test connection using Cloudflare API
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${process.env.CLOUDFLARE_ACCOUNT_ID}/r2/buckets/${process.env.CLOUDFLARE_R2_BUCKET_NAME}/objects?max-keys=5`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.CLOUDFLARE_R2_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json({
        success: false,
        error: "R2 API connection failed",
        details: `${response.status}: ${errorText}`
      }, { status: 500 });
    }

    const data = await response.json();

    return NextResponse.json({
      success: true,
      message: "R2 connection successful",
      config: {
        bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
        accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
        publicUrl: process.env.CLOUDFLARE_R2_PUBLIC_URL
      },
      stats: {
        objectCount: data.result?.length || 0,
        sampleObjects: data.result?.slice(0, 3) || []
      }
    });

  } catch (error) {
    console.error("R2 test error:", error);
    return NextResponse.json({
      success: false,
      error: "R2 connection failed",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
