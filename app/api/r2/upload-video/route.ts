import { NextRequest, NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/client";
import r2Client from "@/lib/r2-client";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

export async function POST(request: NextRequest) {
  try {
    const { filename, postId } = await request.json();

    if (!filename || !postId) {
      return NextResponse.json(
        { error: "Filename and postId are required" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedExtensions = ['.mp4', '.mov'];
    const fileExtension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    if (!allowedExtensions.includes(fileExtension)) {
      return NextResponse.json(
        { error: "Only MP4 and MOV files are allowed" },
        { status: 400 }
      );
    }

    // Check R2 configuration
    if (!process.env.CLOUDFLARE_R2_BUCKET_NAME || !process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || !process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY) {
      console.error("Missing Cloudflare R2 environment variables");
      return NextResponse.json(
        { error: "R2 configuration missing" },
        { status: 500 }
      );
    }

    const supabase = await createSupabaseServerClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Create unique file path
    const timestamp = Date.now();
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    const fileKey = `users/${user.id}/${timestamp}-${sanitizedFilename}`;

    // Generate presigned URL for direct client upload
    const command = new PutObjectCommand({
      Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      Key: fileKey,
      ContentType: fileExtension === '.mp4' ? 'video/mp4' : 'video/quicktime',
    });

    const uploadUrl = await getSignedUrl(r2Client, command, { expiresIn: 3600 }); // 1 hour
    const publicUrl = `${process.env.CLOUDFLARE_R2_PUBLIC_URL}/${fileKey}`;

    // Create video record in database
    const { data: video, error: dbError } = await supabase
      .from("videos")
      .insert({
        post_id: postId,
        creator_id: user.id,
        title: filename.replace(/\.[^/.]+$/, ""), // Remove file extension
        r2_file_key: fileKey,
        r2_public_url: publicUrl,
        is_free: false
      })
      .select()
      .single();

    if (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Failed to save video record" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      uploadUrl,
      publicUrl,
      video
    });

  } catch (error) {
    console.error("Error generating upload URL:", error);
    return NextResponse.json(
      { error: "Failed to generate upload URL" },
      { status: 500 }
    );
  }
}
