import { NextRequest, NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/client";

export async function POST(request: NextRequest) {
  try {
    const { videoId, fileSize } = await request.json();

    if (!videoId || !fileSize) {
      return NextResponse.json(
        { error: "Video ID and file size are required" },
        { status: 400 }
      );
    }

    const supabase = await createSupabaseServerClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Update video record with file size
    const { error: updateError } = await supabase
      .from("videos")
      .update({
        file_size: fileSize,
        updated_at: new Date().toISOString()
      })
      .eq("id", videoId)
      .eq("creator_id", user.id); // Ensure user can only update their own videos

    if (updateError) {
      console.error("Database update error:", updateError);
      return NextResponse.json(
        { error: "Failed to update video record" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error("Error updating video:", error);
    return NextResponse.json(
      { error: "Failed to update video" },
      { status: 500 }
    );
  }
}
