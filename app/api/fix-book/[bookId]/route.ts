import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { processEbook } from '@/lib/ebook-processor'

export async function POST(
  request: NextRequest,
  { params }: { params: { bookId: string } }
) {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get book details
    const { data: book, error: bookError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        user_id,
        is_ebook,
        ebook_file_url,
        ebook_file_type,
        meta_description
      `)
      .eq('id', params.bookId)
      .eq('user_id', user.id)
      .single()

    if (bookError || !book) {
      return NextResponse.json({ error: 'Book not found or access denied' }, { status: 404 })
    }

    if (!book.is_ebook || !book.ebook_file_url) {
      return NextResponse.json({ error: 'This is not an ebook or no file URL found' }, { status: 400 })
    }

    console.log(`Re-processing EPUB for book: ${book.title}`)
    console.log(`File URL: ${book.ebook_file_url}`)

    try {
      // Re-process the EPUB file
      const result = await processEbook(book.ebook_file_url, book.ebook_file_type as 'pdf' | 'epub')
      
      console.log(`Extracted ${result.chapters.length} chapters from EPUB`)
      
      if (result.chapters.length === 0) {
        return NextResponse.json({ 
          error: 'No chapters could be extracted from the EPUB file'
        }, { status: 400 })
      }

      // Log the first chapter content to verify it's correct
      console.log('First chapter preview:', {
        title: result.chapters[0]?.title,
        contentLength: result.chapters[0]?.content?.length,
        contentStart: result.chapters[0]?.content?.substring(0, 200)
      })

      // Clear existing chapters
      console.log('Clearing existing chapters...')
      await supabase
        .from('chapters')
        .delete()
        .eq('project_id', params.bookId)

      // Create new chapters with proper content
      console.log(`Creating ${result.chapters.length} new chapters...`)
      
      const chaptersToInsert = result.chapters.map((chapter, index) => ({
        project_id: params.bookId,
        user_id: user.id,
        title: chapter.title,
        content: chapter.content,
        chapter_number: index + 1,
        word_count: chapter.wordCount || chapter.word_count,
        is_published: true
      }))

      const { error: chaptersError, data: insertedChapters } = await supabase
        .from('chapters')
        .insert(chaptersToInsert)
        .select()

      if (chaptersError) {
        console.error('Error creating chapters:', chaptersError)
        throw new Error(`Failed to create chapters: ${chaptersError.message}`)
      }

      // Update book stats
      const { error: updateError } = await supabase
        .from('projects')
        .update({
          total_chapters: result.chapters.length,
          total_words: result.wordCount,
          reading_time_minutes: result.readingTimeMinutes,
          is_complete: true
        })
        .eq('id', params.bookId)

      if (updateError) {
        console.error('Error updating book stats:', updateError)
      }

      // Verify the fix worked
      const { data: newChapters } = await supabase
        .from('chapters')
        .select('id, title, content, chapter_number, word_count')
        .eq('project_id', params.bookId)
        .order('chapter_number')

      console.log('Verification - new chapters:', newChapters?.map(c => ({
        chapter: c.chapter_number,
        title: c.title,
        hasContent: !!c.content,
        contentLength: c.content?.length || 0,
        contentStart: c.content?.substring(0, 100)
      })))

      return NextResponse.json({
        success: true,
        message: 'Book chapters fixed successfully! Your e-reader should now show the correct content.',
        data: {
          chaptersCreated: result.chapters.length,
          totalWords: result.wordCount,
          readingTime: result.readingTimeMinutes,
          firstChapterPreview: newChapters?.[0]?.content?.substring(0, 200) || 'No content'
        }
      })

    } catch (processingError) {
      console.error('Error processing EPUB:', processingError)
      return NextResponse.json({
        error: 'Failed to process EPUB file',
        details: processingError.message
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Fix API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
