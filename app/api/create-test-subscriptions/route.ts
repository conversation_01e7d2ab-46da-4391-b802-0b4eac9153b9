import { createSupabaseClient } from '@/lib/supabase/client'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = createSupabaseClient()
    
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    // First, find some writers with content
    const { data: writers, error: writersError } = await supabase
      .from('users')
      .select(`
        id,
        name,
        email,
        diary_entries!inner(id)
      `)
      .eq('role', 'writer')
      .neq('id', user.id)
      .limit(5)

    if (writersError) {
      return NextResponse.json({ error: 'Error finding writers', details: writersError }, { status: 500 })
    }

    if (!writers || writers.length === 0) {
      return NextResponse.json({ error: 'No writers found' }, { status: 404 })
    }

    // Create subscriptions to these writers
    const subscriptions = []
    for (const writer of writers) {
      const { data: sub, error: subError } = await supabase
        .from('subscriptions')
        .upsert({
          reader_id: user.id,
          writer_id: writer.id,
          status: 'active',
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        }, {
          onConflict: 'reader_id,writer_id'
        })
        .select()
        .single()

      if (!subError && sub) {
        subscriptions.push({
          ...sub,
          writer_name: writer.name,
          writer_email: writer.email
        })
      }
    }

    return NextResponse.json({
      message: `Created ${subscriptions.length} test subscriptions`,
      subscriptions,
      user_id: user.id
    })

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json({ error: errorMessage }, { status: 500 })
  }
}