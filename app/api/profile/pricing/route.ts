import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function PUT(request: NextRequest) {
  try {
    const { price_monthly } = await request.json()
    
    const supabase = await createSupabaseServerClient()
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Validate price range
    if (!price_monthly || price_monthly < 299) {
      return NextResponse.json({
        error: 'Minimum price is $2.99'
      }, { status: 400 })
    }
    if (price_monthly > 5000) {
      return NextResponse.json({
        error: 'Maximum price is $50.00'
      }, { status: 400 })
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 })
    }

    // Only writers and admins can set prices
    if (profile.role !== 'writer' && profile.role !== 'admin') {
      return NextResponse.json({ 
        error: 'Only creators can set subscription prices' 
      }, { status: 403 })
    }

    // Update price
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        price_monthly: price_monthly,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)

    if (updateError) {
      console.error('Error updating price:', updateError)
      return NextResponse.json({ 
        error: 'Failed to update price' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      price_monthly,
      message: 'Subscription price updated successfully'
    })

  } catch (error) {
    console.error('Price update error:', error)
    return NextResponse.json(
      { error: 'Failed to update price' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current price
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('price_monthly, role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 })
    }

    return NextResponse.json({ 
      price_monthly: profile.price_monthly || 999, // Default $9.99
      role: profile.role
    })

  } catch (error) {
    console.error('Get price error:', error)
    return NextResponse.json(
      { error: 'Failed to get price' },
      { status: 500 }
    )
  }
}
