import { NextRequest, NextResponse } from 'next/server'
import { processEbookBuffer } from '@/lib/ebook-processor'

export async function POST(request: NextRequest) {
  console.log('Preview API endpoint called')

  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      console.error('No file provided in request')
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    console.log('File received:', {
      name: file.name,
      size: file.size,
      type: file.type
    })

    // Check file type
    const fileType = file.name.toLowerCase().endsWith('.epub') ? 'epub' : 'pdf'

    if (!['epub', 'pdf'].includes(fileType)) {
      console.error('Invalid file type:', file.name)
      return NextResponse.json(
        { error: 'File must be EPUB or PDF' },
        { status: 400 }
      )
    }

    console.log(`Processing ${fileType.toUpperCase()} file for preview:`, file.name)

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    console.log('Buffer created, size:', buffer.length)

    // Process the ebook in PREVIEW MODE (Chapter 1 only, preserve formatting)
    const result = await processEbookBuffer(buffer, fileType as 'pdf' | 'epub', true)
    
    console.log('Preview processing completed:', {
      chapters: result.chapters.length,
      wordCount: result.wordCount,
      pageCount: result.pageCount
    })
    
    if (result.chapters.length === 0) {
      return NextResponse.json(
        { error: 'No chapters could be extracted from this file' },
        { status: 400 }
      )
    }

    // Return the processed chapters
    return NextResponse.json({
      success: true,
      chapters: result.chapters.map((chapter, index) => ({
        id: `preview-${index}`,
        title: chapter.title,
        content: chapter.content,
        chapter_number: index + 1,
        word_count: chapter.wordCount || chapter.word_count || 0
      })),
      metadata: {
        totalChapters: result.chapters.length,
        totalWords: result.wordCount,
        readingTime: result.readingTimeMinutes,
        pageCount: result.pageCount
      }
    })

  } catch (error) {
    console.error('Preview processing error:', error)

    // Ensure we always return JSON, even for unexpected errors
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'

    return NextResponse.json(
      {
        success: false,
        error: `Failed to process file: ${errorMessage}`,
        details: error instanceof Error ? error.stack : String(error)
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}



