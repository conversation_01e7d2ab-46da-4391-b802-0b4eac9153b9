import { NextRequest, NextResponse } from 'next/server'
import { testEmailConnection } from '@/lib/email/resend'
import { sendTestEmail } from '@/lib/email/notifications'

export async function POST(request: NextRequest) {
  try {
    const { testEmail } = await request.json()
    
    if (!testEmail) {
      return NextResponse.json(
        { error: 'Test email address is required' },
        { status: 400 }
      )
    }

    // Test Resend connection first
    const connectionTest = await testEmailConnection()
    if (!connectionTest.success) {
      return NextResponse.json(
        { 
          error: 'Resend connection failed', 
          details: connectionTest.error 
        },
        { status: 500 }
      )
    }

    // Send test email
    const emailResult = await sendTestEmail(testEmail)
    if (!emailResult.success) {
      return NextResponse.json(
        { 
          error: 'Failed to send test email', 
          details: emailResult.error 
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Test email sent successfully!',
      id: emailResult.id
    })

  } catch (error) {
    console.error('Test email error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint to check email system status
export async function GET() {
  try {
    const connectionTest = await testEmailConnection()
    
    return NextResponse.json({
      resendConnected: connectionTest.success,
      fromEmail: process.env.RESEND_FROM_EMAIL,
      error: connectionTest.error || null
    })

  } catch (error) {
    console.error('Email status check error:', error)
    return NextResponse.json(
      { 
        resendConnected: false,
        error: 'Failed to check email system status'
      },
      { status: 500 }
    )
  }
}
