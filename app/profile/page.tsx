"use client"

import { useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"

export default function ProfilePage() {
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const redirectToPublicProfile = async () => {
      const { data: { user } } = await supabase.auth.getUser()

      if (user) {
        // Redirect to public profile view
        router.replace(`/u/${user.id}`)
      } else {
        // Redirect to login if not authenticated
        router.replace('/login')
      }
    }

    redirectToPublicProfile()
  }, [router, supabase])

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to your profile...</p>
      </div>
    </div>
  )
}