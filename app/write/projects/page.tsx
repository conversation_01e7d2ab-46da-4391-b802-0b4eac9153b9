'use client'

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"

interface Project {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  is_private: boolean
  is_complete: boolean
  price_type: 'project' | 'chapters'
  price_amount: number
  total_chapters: number
  total_words: number
  created_at: string
  updated_at: string
}

export default function ProjectsPage() {
  const [user, setUser] = useState<unknown>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const initializePage = async () => {
      const { data: { user: authUser }, error } = await supabase.auth.getUser()
      
      if (error || !authUser) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profile } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single()

      if (!profile || (profile.role !== 'writer' && profile.role !== 'admin')) {
        router.push('/')
        return
      }

      setUser(profile)

      // Load user's projects
      const { data: userProjects } = await supabase
        .from("projects")
        .select("*")
        .eq("user_id", authUser.id)
        .order("updated_at", { ascending: false })

      setProjects(userProjects || [])
      setLoading(false)
    }

    initializePage()
  }, [router, supabase])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    )
  }

  if (!user) return null

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-8">
        
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-serif text-gray-800 mb-2">
              Your Book Projects
            </h1>
            <p className="text-gray-600 font-serif">
              Create and manage your creative writing projects
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Link
              href="/write"
              className="text-gray-600 hover:text-gray-800 font-medium text-sm sm:text-base transition-colors"
            >
              ← Back to Write
            </Link>
            
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-purple-600 text-white hover:bg-purple-700"
            >
              + New Project
            </Button>
          </div>
        </div>

        {/* Projects Grid */}
        {projects.length === 0 ? (
          <div className="text-center py-20">
            <div className="w-24 h-24 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-4xl">📚</span>
            </div>
            <h2 className="text-xl font-serif text-gray-800 mb-4">
              No projects yet
            </h2>
            <p className="text-gray-600 font-serif mb-8 max-w-md mx-auto">
              Start your first book project and begin crafting your masterpiece. 
              Create novels, short stories, or any creative work.
            </p>
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-purple-600 text-white hover:bg-purple-700"
            >
              Create Your First Project
            </Button>
          </div>
        ) : (
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {projects.map((project) => (
              <Link
                key={project.id}
                href={`/write/projects/${project.id}`}
                className="group"
              >
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-100/50 group-hover:scale-[1.02]">
                  
                  {/* Cover Image - KDP Standard 10:16 ratio */}
                  <div className="aspect-[10/16] bg-gradient-to-br from-purple-100 to-blue-100 relative overflow-hidden">
                    {project.cover_image_url ? (
                      <img
                        src={project.cover_image_url}
                        alt={project.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <span className="text-6xl opacity-50">📖</span>
                      </div>
                    )}
                    
                    {/* Status Badges */}
                    <div className="absolute top-3 left-3 flex gap-2">
                      {project.is_private && (
                        <span className="bg-gray-900/80 text-white text-xs px-2 py-1 rounded-full">
                          Private
                        </span>
                      )}
                      {project.is_complete && (
                        <span className="bg-green-500/80 text-white text-xs px-2 py-1 rounded-full">
                          Complete
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Project Info */}
                  <div className="p-4">
                    <h3 className="font-serif text-lg text-gray-800 mb-2 line-clamp-2 group-hover:text-purple-600 transition-colors">
                      {project.title}
                    </h3>
                    
                    {project.description && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {project.description}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{project.total_chapters} chapters</span>
                      <span>{project.total_words.toLocaleString()} words</span>
                    </div>
                    
                    {project.price_amount && (
                      <div className="mt-2 text-sm font-medium text-purple-600">
                        ${(project.price_amount / 100).toFixed(2)} 
                        {project.price_type === 'chapters' ? ' per 30 chapters' : ' full access'}
                      </div>
                    )}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}

        {/* Create Project Modal */}
        {showCreateModal && (
          <CreateProjectModal
            onClose={() => setShowCreateModal(false)}
            onSuccess={(projectId) => {
              setShowCreateModal(false)
              router.push(`/write/projects/${projectId}`)
            }}
            userId={user.id}
          />
        )}
      </div>
    </div>
  )
}

// Create Project Modal Component (simplified for now)
function CreateProjectModal({ onClose, onSuccess, userId }: {
  onClose: () => void
  onSuccess: (projectId: string) => void
  userId: string
}) {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [genre, setGenre] = useState("")
  const [loading, setLoading] = useState(false)
  const supabase = createSupabaseClient()

  const handleCreate = async () => {
    if (!title.trim()) return

    setLoading(true)
    try {
      const { data: project, error } = await supabase
        .from("projects")
        .insert({
          user_id: userId,
          title: title.trim(),
          description: description.trim() || null,
          genre: genre.trim() || null,
        })
        .select()
        .single()

      if (error) throw error

      onSuccess(project.id)
    } catch (error) {
      console.error('Error creating project:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-md">
        <h2 className="text-xl font-serif text-gray-800 mb-4">Create New Project</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Project Title
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="My Amazing Novel"
              maxLength={100}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description (optional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="A brief description of your project..."
              rows={3}
              maxLength={500}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Genre (optional)
            </label>
            <input
              type="text"
              value={genre}
              onChange={(e) => setGenre(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Fiction, Romance, Sci-Fi..."
              maxLength={50}
            />
          </div>
        </div>
        
        <div className="flex gap-3 mt-6">
          <Button
            onClick={onClose}
            variant="secondary"
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            onClick={handleCreate}
            isLoading={loading}
            disabled={!title.trim()}
            className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
          >
            Create Project
          </Button>
        </div>
      </div>
    </div>
  )
}
