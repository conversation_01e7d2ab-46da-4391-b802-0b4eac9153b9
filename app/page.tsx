import Link from "next/link"
import { UserRedirect } from "@/components/ReaderRedirect";
import { StructuredData } from "@/components/StructuredData";

export default function Home() {
  return (
    <>
      <UserRedirect />
      <StructuredData type="website" data={{}} />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="text-center mb-12 sm:mb-16">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-serif mb-4 sm:mb-6 text-gray-800 leading-tight">
            OnlyDiary
          </h1>
          <p className="text-lg sm:text-xl font-serif text-gray-600 max-w-2xl mx-auto leading-relaxed px-2">
            Where life becomes income. Read authentic stories and intimate diary entries from creators,
            or start sharing your own thoughts and experiences to build a devoted audience.
          </p>
        </div>

        <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-12 sm:mb-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100/50">
            <h2 className="text-xl sm:text-2xl font-serif mb-3 sm:mb-4 text-gray-800">READ</h2>
            <p className="text-gray-600 font-serif mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">
              Discover authentic stories and intimate diary entries. Subscribe to your favorite creators
              and get exclusive access to their most personal thoughts and daily experiences.
            </p>
            <div className="space-y-3 text-sm text-gray-600 mb-6">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                Content too personal for social media
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                Free tiers available to build connections
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                Support creators on their authentic journeys
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                Intimate connection with your favorite entertainers
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                Be part of their story as it unfolds
              </div>
            </div>
            <Link
              href="/register"
              className="block w-full bg-blue-600 text-white text-center py-3 sm:py-4 px-4 rounded-xl font-medium hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]"
            >
              Start Reading
            </Link>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100/50">
            <h2 className="text-xl sm:text-2xl font-serif mb-3 sm:mb-4 text-gray-800">CREATE</h2>
            <p className="text-gray-600 font-serif mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">
              Turn your thoughts into income. Share your most intimate diary entries, daily experiences, or creative stories.
              Build a devoted audience and get paid instantly for your authentic writing.
            </p>
            <div className="space-y-3 text-sm text-gray-600 mb-6">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                Write diary entries & unlimited book projects
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                Monetize your authentic thoughts & experiences
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                Keep 80% of subscriptions + 95% of donations
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                Instant withdrawals + real-time engagement
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                Smart formatting & distraction-free writing
              </div>
            </div>
            <Link
              href="/register"
              className="block w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white text-center py-3 sm:py-4 px-4 rounded-xl font-medium hover:from-purple-700 hover:to-pink-700 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]"
            >
              Start Writing & Earning
            </Link>
          </div>
        </div>

        <div className="text-center">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-all duration-300 max-w-md mx-auto border border-gray-100/50">
            <h3 className="text-lg sm:text-xl font-serif mb-3 sm:mb-4 text-gray-800">See How It Works</h3>
            <p className="text-gray-600 font-serif mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">
              Read trending diary entries from creators who are monetizing their authentic stories:
            </p>
            <Link
              href="/trending"
              className="inline-block bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 sm:py-4 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]"
            >
              Read Trending Stories
            </Link>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
