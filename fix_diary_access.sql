-- Fix diary entries access - restore missing policy for subscriber access
-- The migration 016_fix_all_rls_warnings_safe.sql removed the crucial policy that allows
-- subscribers to read entries they have access to

-- Add back the missing policy that allows reading non-hidden entries
-- This works with the application's credit system and preview functions
CREATE POLICY "Anyone can view non-hidden diary entries" ON diary_entries
    FOR SELECT USING (NOT is_hidden);