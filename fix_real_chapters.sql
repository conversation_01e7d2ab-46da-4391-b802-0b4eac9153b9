-- Fix the API issues first, then trigger real EPUB processing

-- 1. Fix the database schema issue by removing the language field reference
-- This fixes the "Could not find the 'language' column" error

-- 2. Check what columns actually exist in projects table
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'projects' 
AND column_name IN ('language', 'total_chapters', 'total_words', 'reading_time_minutes', 'is_complete')
ORDER BY column_name;

-- 3. Add missing columns if they don't exist
DO $$ 
BEGIN
    -- Add language column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'language') THEN
        ALTER TABLE projects ADD COLUMN language TEXT DEFAULT 'en';
    END IF;
    
    -- Add total_chapters if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'total_chapters') THEN
        ALTER TABLE projects ADD COLUMN total_chapters INTEGER DEFAULT 0;
    END IF;
    
    -- Add total_words if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'total_words') THEN
        ALTER TABLE projects ADD COLUMN total_words INTEGER DEFAULT 0;
    END IF;
    
    -- Add reading_time_minutes if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'reading_time_minutes') THEN
        ALTER TABLE projects ADD COLUMN reading_time_minutes INTEGER DEFAULT 0;
    END IF;
    
    -- Add is_complete if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'is_complete') THEN
        ALTER TABLE projects ADD COLUMN is_complete BOOLEAN DEFAULT false;
    END IF;
END $$;

-- 4. Verify the columns now exist
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'projects' 
AND column_name IN ('language', 'total_chapters', 'total_words', 'reading_time_minutes', 'is_complete')
ORDER BY column_name;
