# 🔔 Push Notifications Setup Guide

## 📦 1. Install Dependencies

```bash
npm install web-push @types/web-push
```

## 🔑 2. Generate VAPID Keys

```bash
npx web-push generate-vapid-keys
```

This will output something like:
```
Public Key: BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBr1XRKkJNNEjqbMqIVs
Private Key: tUkzMjHlAREBaFUhbXvqvk4TAQjjvmKWHn5FJyM9FmM
```

## 🌍 3. Add Environment Variables

Add these to your `.env.local`:

```env
# VAPID Keys for Push Notifications
NEXT_PUBLIC_VAPID_PUBLIC_KEY=BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBr1XRKkJNNEjqbMqIVs
VAPID_PRIVATE_KEY=tUkzMjHlAREBaFUhbXvqvk4TAQjjvmKWHn5FJyM9FmM
NEXT_PUBLIC_VAPID_EMAIL=<EMAIL>

# Optional: For cron job security
CRON_SECRET_TOKEN=your-random-secret-token

# Your site URL (for notifications)
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
```

## 🗄️ 4. Run Database Migration

Copy and paste this into your Supabase SQL Editor:

```sql
-- Copy the entire content from scripts/run-comment-notifications.sql
```

## 🚀 5. Test the System

### Manual Test:
1. **Visit your site** → Push notification prompt should appear
2. **Click "Enable Notifications"** → Browser asks for permission
3. **Allow notifications** → Subscription saved to database
4. **Post a comment** → Notification should appear instantly

### API Test:
```bash
# Test notification processing
curl -X POST http://localhost:3000/api/send-push-notifications
```

## ⏰ 6. Set Up Automatic Processing (Optional)

### Option A: Vercel Cron (Recommended)
Add to `vercel.json`:
```json
{
  "crons": [
    {
      "path": "/api/cron/process-notifications",
      "schedule": "*/2 * * * *"
    }
  ]
}
```

### Option B: External Cron Service
Set up a cron job to call:
```
POST https://yourdomain.com/api/cron/process-notifications
Authorization: Bearer your-cron-secret-token
```

## 🎯 How It Works

### User Flow:
1. **User visits site** → Push notification prompt appears
2. **User enables notifications** → Browser permission + subscription saved
3. **Someone comments** → Database trigger creates notification
4. **Cron job runs** → Processes notifications and sends push notifications
5. **User receives notification** → Clicks to view comment

### Real-Time Flow:
1. **Comment posted** → Appears instantly via real-time subscription
2. **Notification created** → Database trigger fires
3. **Push notification sent** → Within 2 minutes via cron job
4. **User clicks notification** → Navigates to diary entry

## 🔧 Troubleshooting

### No Notifications Appearing:
1. **Check browser permissions** → Settings > Notifications
2. **Check VAPID keys** → Make sure they're in .env.local
3. **Check database** → Look for records in push_subscriptions table
4. **Check console** → Look for JavaScript errors

### Notifications Not Sending:
1. **Check API logs** → `/api/send-push-notifications` endpoint
2. **Check cron job** → Make sure it's running every 2 minutes
3. **Check VAPID email** → Must be valid email address
4. **Check subscription validity** → Invalid subscriptions are auto-removed

### Database Issues:
1. **Run migration** → Make sure all tables and triggers exist
2. **Check RLS policies** → Users should be able to insert subscriptions
3. **Check real-time** → Comments table should have real-time enabled

## 📊 Monitoring

### Check Push Subscriptions:
```sql
SELECT COUNT(*) as total_subscriptions FROM push_subscriptions;
```

### Check Recent Notifications:
```sql
SELECT * FROM notifications 
WHERE sent_at > NOW() - INTERVAL '1 hour' 
ORDER BY sent_at DESC;
```

### Check Notification Processing:
```sql
SELECT 
  type,
  COUNT(*) as total,
  COUNT(CASE WHEN clicked_at IS NOT NULL THEN 1 END) as processed
FROM notifications 
WHERE sent_at > NOW() - INTERVAL '24 hours'
GROUP BY type;
```

## 🎉 Success!

Once set up, users will get:
- ✅ **Instant comment notifications** when someone comments on their diary
- ✅ **Reply notifications** when someone replies to their comment  
- ✅ **Real-time comment updates** without page refresh
- ✅ **Professional notification experience** with click-to-view functionality

**Cost: $0** - No email service needed, just browser push notifications!
